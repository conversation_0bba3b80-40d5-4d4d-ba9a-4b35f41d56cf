# نظام الإجازات المتكامل - دليل المستخدم

## مقدمة
هذا الدليل يشرح كيفية استخدام نظام الإجازات المتكامل المطور الذي يوفر حلاً شاملاً لإدارة إجازات الموظفين بطريقة فعالة وسهلة الاستخدام.

## الميزات الرئيسية
1. **لوحة معلومات تفاعلية**: عرض مرئي للبيانات الإحصائية للإجازات
2. **نموذج إدخال الإجازات**: واجهة سهلة الاستخدام لإدخال طلبات الإجازات الجديدة
3. **تقارير الإجازات**: إمكانية إنشاء تقارير مفصلة حسب الموظف، القسم، أو نوع الإجازة
4. **حساب رصيد الإجازات**: حساب تلقائي لرصيد الإجازات المتبقي لكل موظف
5. **نظام التنبيهات**: إشعارات آلية للإجازات المقبلة وانتهاء الأرصدة
6. **قائمة رئيسية**: واجهة سهلة للتنقل بين مختلف أقسام النظام

## كيفية الاستخدام

### تثبيت الماكرو
1. افتح ملف Excel الخاص بنظام الإجازات
2. اضغط Alt+F11 لفتح محرر VBA
3. انقر بزر الماوس الأيمن على اسم المشروع في نافذة Project Explorer
4. اختر "استيراد ملف" (Import File)
5. حدد ملف `تطوير_نظام_الإجازات.bas`
6. اضغط على "فتح" (Open)

### تشغيل النظام
1. بعد استيراد الماكرو، اضغط Alt+F8 لفتح قائمة الماكرو
2. اختر `تنفيذ_جميع_الوظائف` من القائمة
3. انقر على "تشغيل" (Run)
4. سيتم إنشاء جميع أوراق العمل والواجهات اللازمة تلقائياً

### استخدام القائمة الرئيسية
بعد تشغيل النظام، ستظهر ورقة "القائمة الرئيسية" التي تحتوي على الأزرار التالية:

- **لوحة المعلومات**: لعرض الإحصائيات والرسوم البيانية
- **إدخال إجازة جديدة**: لإضافة طلب إجازة جديد
- **تقرير الإجازات**: لعرض وطباعة تقارير الإجازات
- **رصيد الإجازات**: لمراجعة أرصدة الإجازات للموظفين
- **التنبيهات**: لعرض وإدارة التنبيهات

## الوظائف التفصيلية

### لوحة المعلومات
- عرض إجمالي الإجازات حسب النوع
- عرض الإجازات حسب القسم
- عرض الإجازات المقبلة
- عرض ملخص إحصائي للإجازات

### نموذج إدخال الإجازات
- إدخال بيانات الموظف
- تحديد نوع الإجازة من قائمة منسدلة
- تحديد تاريخ بداية ونهاية الإجازة
- حساب تلقائي لعدد أيام الإجازة
- إضافة ملاحظات إضافية

### تقرير الإجازات
- عرض جميع الإجازات المسجلة
- إمكانية التصفية حسب الموظف، القسم، أو نوع الإجازة
- إمكانية الطباعة أو التصدير

### رصيد الإجازات
- عرض الرصيد السنوي لكل موظف
- حساب الإجازات المستخدمة
- حساب تلقائي للرصيد المتبقي

### نظام التنبيهات
- تنبيهات للإجازات المقبلة
- تنبيهات لانتهاء أرصدة الإجازات
- تنبيهات للتداخل بين الإجازات

## نصائح وإرشادات

1. **حماية البيانات**: تأكد من حفظ الملف بانتظام وإنشاء نسخ احتياطية
2. **تحديث البيانات**: قم بتحديث بيانات الموظفين بشكل دوري
3. **الأمان**: استخدم ميزات حماية الخلايا وأوراق العمل لمنع التعديل غير المصرح به
4. **التخصيص**: يمكن تعديل النظام حسب احتياجات المؤسسة من خلال تعديل كود VBA

## الدعم الفني
إذا واجهتك أي مشكلة أو كان لديك استفسار، يرجى التواصل مع قسم تكنولوجيا المعلومات.

---

*تم تطوير هذا النظام لتحسين إدارة الإجازات وزيادة الكفاءة في العمل.*