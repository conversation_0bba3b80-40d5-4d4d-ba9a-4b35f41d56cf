# نظام إدارة إجازات الموظفين المتطور

## 📋 نظرة عامة

نظام شامل ومتطور لإدارة إجازات الموظفين مع دعم كامل للغة العربية والتقويم الميلادي. تم تطوير النظام ليكون سهل الاستخدام ومتجاوب مع جميع الأجهزة.

## ✨ الميزات الرئيسية

### 🎯 الميزات الأساسية
- **إدارة شاملة للموظفين** مع دعم الرصيد المرحل
- **إدارة متقدمة للإجازات** (سنوية/مرضية/طارئة)
- **تقارير وإحصائيات مفصلة** مع رسوم بيانية تفاعلية
- **بحث وفلترة متقدمة** مع معايير متعددة
- **تصدير البيانات** بصيغ CSV و JSON

### 🔧 الميزات التقنية
- **حفظ تلقائي** كل 30 ثانية
- **نسخ احتياطية تلقائية** يومية
- **تشفير البيانات** للحماية
- **دعم الوضع الليلي** للراحة البصرية
- **اختصارات لوحة المفاتيح** لسرعة العمل

## 📁 هيكل الملفات

```
├── index.html          # الهيكل الأساسي للتطبيق
├── styles.css          # جميع تنسيقات CSS
├── utils.js            # الوظائف المساعدة والأدوات
├── core.js             # الإحصائيات والبحث والفلترة
├── sections.js         # تحميل محتوى الأقسام
├── functions.js        # إدارة الموظفين والجداول
├── features.js         # إدارة الإجازات والتقارير
├── extras.js           # الرسوم البيانية والبيانات التجريبية
├── app.js              # الوظائف الرئيسية والتنقل
├── init.js             # التهيئة واختصارات لوحة المفاتيح
└── README.md           # هذا الملف
```

## 🚀 التشغيل

1. **تحميل الملفات**: تأكد من وجود جميع الملفات في نفس المجلد
2. **فتح النظام**: افتح ملف `index.html` في أي متصفح حديث
3. **البدء**: سيتم تحميل النظام تلقائياً مع واجهة ترحيبية

## 📖 دليل الاستخدام

### 🏠 لوحة التحكم
- عرض إحصائيات شاملة للموظفين والإجازات
- رسوم بيانية تفاعلية للأقسام وحالات الرصيد
- نظرة سريعة على آخر الموظفين المضافين

### 👥 إدارة الموظفين
- إضافة وتعديل بيانات الموظفين
- **دعم الرصيد المرحل** من السنوات السابقة
- بحث متقدم وفلترة حسب معايير متعددة
- جدول تفاعلي مع تصفح الصفحات

### 📅 إدارة الإجازات
- إضافة إجازات بأنواعها (سنوية/مرضية/طارئة)
- حساب تلقائي للأيام والرصيد المتبقي
- سجل شامل لجميع الإجازات
- إمكانية حذف وتعديل الإجازات

### 📈 التقارير
- تصدير البيانات بصيغة CSV للتحليل
- إنشاء نسخ احتياطية بصيغة JSON
- تقارير مفصلة حسب الأقسام

## ⌨️ اختصارات لوحة المفاتيح

### 🧭 التنقل
- `Ctrl + 1` - لوحة التحكم
- `Ctrl + 2` - إدارة الموظفين
- `Ctrl + 3` - إدارة الإجازات
- `Ctrl + 4` - التقارير
- `Ctrl + 5` - الإعدادات

### 💾 الحفظ والتصدير
- `Ctrl + S` - حفظ البيانات
- `Ctrl + E` - تصدير CSV
- `Ctrl + B` - نسخة احتياطية

### 🔍 البحث والفلترة
- `Ctrl + F` - التركيز على البحث
- `Ctrl + R` - مسح الفلاتر
- `F3` - البحث المتقدم

### ➕ إضافة سريعة
- `Ctrl + N` - موظف جديد
- `Ctrl + L` - إجازة جديدة
- `Ctrl + T` - بيانات تجريبية

## 🔧 المتطلبات التقنية

- **متصفح حديث** يدعم HTML5 و CSS3 و JavaScript ES6
- **دعم التخزين المحلي** (localStorage)
- **دقة شاشة** 1024x768 أو أعلى (مُحسن للشاشات الكبيرة)

## 📊 البيانات والأمان

- **التخزين المحلي**: جميع البيانات محفوظة في متصفحك
- **التشفير**: البيانات مشفرة بتشفير بسيط للحماية
- **النسخ الاحتياطية**: نسخ احتياطية تلقائية لآخر 7 أيام
- **الخصوصية**: لا يتم إرسال أي بيانات لخوادم خارجية

## 🎨 التخصيص

### الوضع الليلي
- تبديل تلقائي بين الوضع النهاري والليلي
- حفظ تفضيلات المستخدم

### التصميم المتجاوب
- دعم كامل للهواتف والأجهزة اللوحية
- تخطيط متكيف حسب حجم الشاشة

## 🧪 البيانات التجريبية

يتضمن النظام بيانات تجريبية شاملة:
- 5 موظفين من أقسام مختلفة
- أسماء عربية وإنجليزية
- قيم متنوعة للرصيد المرحل
- حالات مختلفة (طبيعي/منخفض/نفد الرصيد)
- سجلات إجازات شاملة

## 🔄 التحديثات والتطوير

### الإصدار الحالي: 4.0
- تقسيم الكود إلى ملفات منفصلة
- تحسين الأداء والصيانة
- إضافة ميزات جديدة
- تحسين الأمان

### التحسينات المستقبلية
- تكامل مع قواعد بيانات خارجية
- تطبيق ويب تقدمي (PWA)
- دعم العمل بدون اتصال
- تقارير PDF متقدمة

## 📞 الدعم والمساعدة

- **دليل المستخدم التفاعلي**: متاح داخل النظام
- **اختصارات لوحة المفاتيح**: `Ctrl + ?`
- **نصائح وحيل**: متاحة في قسم الإعدادات

## 📝 الترخيص

هذا النظام مطور للاستخدام الداخلي ويمكن تخصيصه حسب احتياجات المؤسسة.

---

**تم التطوير بـ ❤️ مع التركيز على سهولة الاستخدام والأداء العالي**
