<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 تشخيص الأخطاء - نظام إدارة الإجازات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            margin: 20px;
            background: #f8f9fa;
        }
        .diagnostic-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .error-section {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #e74c3c;
            background: #fdf2f2;
        }
        .success-section {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
            background: #f2fdf2;
        }
        .warning-section {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #f39c12;
            background: #fefbf2;
        }
        .info-section {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            background: #f2f8fd;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn.danger {
            background: #e74c3c;
        }
        .btn.success {
            background: #27ae60;
        }
        .btn.warning {
            background: #f39c12;
        }
        .diagnostic-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .diagnostic-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-ok { background: #27ae60; }
        .status-error { background: #e74c3c; }
        .status-warning { background: #f39c12; }
        .status-unknown { background: #95a5a6; }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <h1>🔍 تشخيص شامل للأخطاء - نظام إدارة إجازات الموظفين</h1>
        
        <div class="error-section">
            <h3>❌ الخطأ المبلغ عنه</h3>
            <p><strong>الرسالة:</strong> "حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة إذا استمر المشكل."</p>
            <p><strong>التوقيت:</strong> بعد التحسينات والإصلاحات الأخيرة</p>
        </div>

        <div class="info-section">
            <h3>🎯 خطة التشخيص</h3>
            <ol>
                <li>فحص تحميل الملفات والمكتبات</li>
                <li>التحقق من استدعاءات الوظائف</li>
                <li>فحص المتغيرات العامة</li>
                <li>اختبار الوظائف الحرجة</li>
                <li>تحليل أخطاء JavaScript</li>
                <li>فحص التعديلات الأخيرة</li>
            </ol>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button class="btn success" onclick="runFullDiagnostic()">🚀 تشغيل التشخيص الشامل</button>
            <button class="btn" onclick="checkFileLoading()">📁 فحص تحميل الملفات</button>
            <button class="btn" onclick="checkFunctions()">⚙️ فحص الوظائف</button>
            <button class="btn" onclick="checkVariables()">📊 فحص المتغيرات</button>
            <button class="btn warning" onclick="simulateError()">⚠️ محاكاة الخطأ</button>
            <button class="btn danger" onclick="clearDiagnostic()">🗑️ مسح النتائج</button>
        </div>

        <div id="diagnosticResults"></div>

        <div class="diagnostic-grid">
            <div class="diagnostic-card">
                <h4>📁 حالة تحميل الملفات</h4>
                <div id="fileLoadingStatus">جاري الفحص...</div>
            </div>
            <div class="diagnostic-card">
                <h4>⚙️ حالة الوظائف الأساسية</h4>
                <div id="functionsStatus">جاري الفحص...</div>
            </div>
            <div class="diagnostic-card">
                <h4>📊 حالة المتغيرات العامة</h4>
                <div id="variablesStatus">جاري الفحص...</div>
            </div>
            <div class="diagnostic-card">
                <h4>🔧 حالة النظام العام</h4>
                <div id="systemStatus">جاري الفحص...</div>
            </div>
        </div>

        <div id="errorLog" class="code-block" style="display: none;">
            <h4>📋 سجل الأخطاء التفصيلي:</h4>
            <div id="errorLogContent"></div>
        </div>

        <div id="solutionSection" style="display: none;">
            <div class="success-section">
                <h3>💡 الحلول المقترحة</h3>
                <div id="suggestedSolutions"></div>
            </div>
        </div>
    </div>

    <!-- تحميل ملفات النظام للفحص -->
    <script src="utils.js"></script>
    <script src="core.js"></script>
    <script src="sections.js"></script>
    <script src="functions.js"></script>
    <script src="features.js"></script>
    <script src="extras.js"></script>
    <script src="reports-advanced.js"></script>
    <script src="settings-functions.js"></script>
    <script src="app.js"></script>
    <script>
        // متغيرات التشخيص
        let diagnosticResults = [];
        let errorLog = [];
        let systemErrors = [];

        // تسجيل الأخطاء
        window.addEventListener('error', function(e) {
            const error = {
                message: e.message,
                filename: e.filename,
                lineno: e.lineno,
                colno: e.colno,
                stack: e.error ? e.error.stack : 'غير متاح',
                timestamp: new Date().toLocaleString('ar-SA')
            };
            systemErrors.push(error);
            logError(`خطأ JavaScript: ${error.message} في ${error.filename}:${error.lineno}`);
        });

        // تسجيل أخطاء الوعود
        window.addEventListener('unhandledrejection', function(e) {
            const error = {
                reason: e.reason,
                timestamp: new Date().toLocaleString('ar-SA')
            };
            systemErrors.push(error);
            logError(`خطأ Promise: ${error.reason}`);
        });

        function logError(message) {
            errorLog.push(`[${new Date().toLocaleTimeString('ar-SA')}] ${message}`);
            updateErrorLog();
        }

        function logSuccess(message) {
            errorLog.push(`[${new Date().toLocaleTimeString('ar-SA')}] ✅ ${message}`);
            updateErrorLog();
        }

        function logWarning(message) {
            errorLog.push(`[${new Date().toLocaleTimeString('ar-SA')}] ⚠️ ${message}`);
            updateErrorLog();
        }

        function updateErrorLog() {
            const errorLogElement = document.getElementById('errorLog');
            const errorLogContent = document.getElementById('errorLogContent');
            
            if (errorLog.length > 0) {
                errorLogElement.style.display = 'block';
                errorLogContent.innerHTML = errorLog.join('\n');
            }
        }

        function addDiagnosticResult(category, test, status, details = '') {
            diagnosticResults.push({
                category,
                test,
                status,
                details,
                timestamp: new Date().toLocaleString('ar-SA')
            });
            updateDiagnosticResults();
        }

        function updateDiagnosticResults() {
            const resultsElement = document.getElementById('diagnosticResults');
            
            if (diagnosticResults.length === 0) return;

            const groupedResults = diagnosticResults.reduce((groups, result) => {
                if (!groups[result.category]) groups[result.category] = [];
                groups[result.category].push(result);
                return groups;
            }, {});

            let html = '<h3>📊 نتائج التشخيص</h3>';
            
            Object.entries(groupedResults).forEach(([category, results]) => {
                html += `<div class="diagnostic-card">`;
                html += `<h4>${category}</h4>`;
                
                results.forEach(result => {
                    const statusClass = result.status === 'success' ? 'status-ok' : 
                                      result.status === 'error' ? 'status-error' : 
                                      result.status === 'warning' ? 'status-warning' : 'status-unknown';
                    
                    html += `<div style="margin: 8px 0;">`;
                    html += `<span class="status-indicator ${statusClass}"></span>`;
                    html += `<strong>${result.test}</strong>`;
                    if (result.details) {
                        html += `<br><small style="color: #666; margin-right: 20px;">${result.details}</small>`;
                    }
                    html += `</div>`;
                });
                
                html += `</div>`;
            });

            resultsElement.innerHTML = html;
        }

        // ===== وظائف التشخيص الأساسية =====

        function checkFileLoading() {
            logSuccess('بدء فحص تحميل الملفات...');

            const requiredFiles = [
                'utils.js',
                'core.js',
                'sections.js',
                'functions.js',
                'features.js',
                'extras.js',
                'reports-advanced.js',
                'settings-functions.js',
                'app.js'
            ];

            const scripts = Array.from(document.scripts);
            const loadedFiles = scripts.map(script => {
                const src = script.src;
                return src ? src.split('/').pop() : null;
            }).filter(Boolean);

            let allFilesLoaded = true;
            requiredFiles.forEach(file => {
                const isLoaded = loadedFiles.includes(file);
                if (isLoaded) {
                    addDiagnosticResult('📁 تحميل الملفات', file, 'success', 'تم التحميل بنجاح');
                    logSuccess(`ملف ${file} محمل بنجاح`);
                } else {
                    addDiagnosticResult('📁 تحميل الملفات', file, 'error', 'لم يتم العثور على الملف');
                    logError(`ملف ${file} غير محمل`);
                    allFilesLoaded = false;
                }
            });

            updateFileLoadingStatus(allFilesLoaded);
            return allFilesLoaded;
        }

        function checkFunctions() {
            logSuccess('بدء فحص الوظائف الأساسية...');

            const criticalFunctions = [
                'showSection',
                'addEmployee',
                'editEmployee',
                'deleteEmployee',
                'updateTable',
                'filterEmployees',
                'loadTestData',
                'saveData',
                'loadDataFromStorage',
                'showAlert',
                'getEmployeeStatus'
            ];

            let allFunctionsAvailable = true;
            criticalFunctions.forEach(funcName => {
                try {
                    if (typeof window[funcName] === 'function') {
                        addDiagnosticResult('⚙️ الوظائف الأساسية', funcName, 'success', 'الوظيفة متاحة');
                        logSuccess(`وظيفة ${funcName} متاحة`);
                    } else {
                        addDiagnosticResult('⚙️ الوظائف الأساسية', funcName, 'error', 'الوظيفة غير متاحة');
                        logError(`وظيفة ${funcName} غير متاحة`);
                        allFunctionsAvailable = false;
                    }
                } catch (error) {
                    addDiagnosticResult('⚙️ الوظائف الأساسية', funcName, 'error', `خطأ: ${error.message}`);
                    logError(`خطأ في فحص وظيفة ${funcName}: ${error.message}`);
                    allFunctionsAvailable = false;
                }
            });

            updateFunctionsStatus(allFunctionsAvailable);
            return allFunctionsAvailable;
        }

        function checkVariables() {
            logSuccess('بدء فحص المتغيرات العامة...');

            const criticalVariables = [
                { name: 'employees', type: 'array', description: 'مصفوفة الموظفين' },
                { name: 'filteredEmployees', type: 'array', description: 'الموظفين المفلترين' },
                { name: 'editingIndex', type: 'number', description: 'فهرس التعديل' },
                { name: 'systemSettings', type: 'object', description: 'إعدادات النظام' }
            ];

            let allVariablesOk = true;
            criticalVariables.forEach(variable => {
                try {
                    const value = window[variable.name];
                    const actualType = Array.isArray(value) ? 'array' : typeof value;

                    if (value !== undefined) {
                        if (actualType === variable.type || (variable.type === 'array' && Array.isArray(value))) {
                            addDiagnosticResult('📊 المتغيرات العامة', variable.name, 'success',
                                `${variable.description} - النوع: ${actualType}`);
                            logSuccess(`متغير ${variable.name} موجود ونوعه صحيح`);
                        } else {
                            addDiagnosticResult('📊 المتغيرات العامة', variable.name, 'warning',
                                `نوع خاطئ - متوقع: ${variable.type}, فعلي: ${actualType}`);
                            logWarning(`متغير ${variable.name} نوعه خاطئ`);
                        }
                    } else {
                        addDiagnosticResult('📊 المتغيرات العامة', variable.name, 'error',
                            `${variable.description} - غير معرف`);
                        logError(`متغير ${variable.name} غير معرف`);
                        allVariablesOk = false;
                    }
                } catch (error) {
                    addDiagnosticResult('📊 المتغيرات العامة', variable.name, 'error',
                        `خطأ في الفحص: ${error.message}`);
                    logError(`خطأ في فحص متغير ${variable.name}: ${error.message}`);
                    allVariablesOk = false;
                }
            });

            updateVariablesStatus(allVariablesOk);
            return allVariablesOk;
        }

        function runFullDiagnostic() {
            logSuccess('🚀 بدء التشخيص الشامل للنظام...');

            // مسح النتائج السابقة
            diagnosticResults = [];
            errorLog = [];

            // تشغيل جميع الفحوصات
            const fileCheck = checkFileLoading();
            setTimeout(() => {
                const functionCheck = checkFunctions();
                setTimeout(() => {
                    const variableCheck = checkVariables();
                    setTimeout(() => {
                        checkSystemIntegrity();
                        setTimeout(() => {
                            generateSolutions();
                        }, 500);
                    }, 500);
                }, 500);
            }, 500);
        }

        // ===== وظائف التشخيص المتقدمة =====

        function checkSystemIntegrity() {
            logSuccess('بدء فحص تكامل النظام...');

            // فحص localStorage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                addDiagnosticResult('🔧 تكامل النظام', 'localStorage', 'success', 'يعمل بشكل صحيح');
                logSuccess('localStorage يعمل بشكل صحيح');
            } catch (error) {
                addDiagnosticResult('🔧 تكامل النظام', 'localStorage', 'error', `خطأ: ${error.message}`);
                logError(`localStorage لا يعمل: ${error.message}`);
            }

            // فحص console
            try {
                console.log('اختبار console');
                addDiagnosticResult('🔧 تكامل النظام', 'console', 'success', 'يعمل بشكل صحيح');
                logSuccess('console يعمل بشكل صحيح');
            } catch (error) {
                addDiagnosticResult('🔧 تكامل النظام', 'console', 'error', `خطأ: ${error.message}`);
                logError(`console لا يعمل: ${error.message}`);
            }

            // فحص DOM
            try {
                const testElement = document.createElement('div');
                document.body.appendChild(testElement);
                document.body.removeChild(testElement);
                addDiagnosticResult('🔧 تكامل النظام', 'DOM manipulation', 'success', 'يعمل بشكل صحيح');
                logSuccess('DOM manipulation يعمل بشكل صحيح');
            } catch (error) {
                addDiagnosticResult('🔧 تكامل النظام', 'DOM manipulation', 'error', `خطأ: ${error.message}`);
                logError(`DOM manipulation لا يعمل: ${error.message}`);
            }

            // فحص الأخطاء المسجلة
            if (systemErrors.length > 0) {
                addDiagnosticResult('🔧 تكامل النظام', 'JavaScript Errors', 'error',
                    `تم رصد ${systemErrors.length} خطأ`);
                logError(`تم رصد ${systemErrors.length} خطأ JavaScript`);

                systemErrors.forEach((error, index) => {
                    logError(`خطأ ${index + 1}: ${error.message || error.reason}`);
                });
            } else {
                addDiagnosticResult('🔧 تكامل النظام', 'JavaScript Errors', 'success', 'لا توجد أخطاء مسجلة');
                logSuccess('لا توجد أخطاء JavaScript مسجلة');
            }
        }

        function generateSolutions() {
            logSuccess('إنشاء الحلول المقترحة...');

            const errorCount = diagnosticResults.filter(r => r.status === 'error').length;
            const warningCount = diagnosticResults.filter(r => r.status === 'warning').length;

            let solutions = [];

            if (errorCount === 0 && warningCount === 0) {
                solutions.push({
                    priority: 'info',
                    title: '✅ النظام يعمل بشكل صحيح',
                    description: 'لم يتم العثور على مشاكل في التشخيص. الخطأ قد يكون مؤقت أو ناتج عن عوامل خارجية.',
                    actions: [
                        'تحديث الصفحة',
                        'مسح cache المتصفح',
                        'التحقق من اتصال الإنترنت',
                        'إعادة تشغيل المتصفح'
                    ]
                });
            }

            // حلول للملفات المفقودة
            const missingFiles = diagnosticResults.filter(r => r.category === '📁 تحميل الملفات' && r.status === 'error');
            if (missingFiles.length > 0) {
                solutions.push({
                    priority: 'high',
                    title: '❌ ملفات مفقودة',
                    description: `تم العثور على ${missingFiles.length} ملف مفقود`,
                    actions: [
                        'التحقق من وجود الملفات في المجلد',
                        'التأكد من صحة مسارات الملفات في index.html',
                        'إعادة رفع الملفات المفقودة',
                        'التحقق من أذونات الملفات'
                    ]
                });
            }

            // حلول للوظائف المفقودة
            const missingFunctions = diagnosticResults.filter(r => r.category === '⚙️ الوظائف الأساسية' && r.status === 'error');
            if (missingFunctions.length > 0) {
                solutions.push({
                    priority: 'high',
                    title: '❌ وظائف مفقودة',
                    description: `تم العثور على ${missingFunctions.length} وظيفة مفقودة`,
                    actions: [
                        'التحقق من تحميل الملفات بالترتيب الصحيح',
                        'البحث عن أخطاء syntax في الملفات',
                        'التأكد من عدم وجود تضارب في أسماء الوظائف',
                        'إعادة تعريف الوظائف المفقودة'
                    ]
                });
            }

            // حلول للمتغيرات المفقودة
            const missingVariables = diagnosticResults.filter(r => r.category === '📊 المتغيرات العامة' && r.status === 'error');
            if (missingVariables.length > 0) {
                solutions.push({
                    priority: 'medium',
                    title: '⚠️ متغيرات مفقودة',
                    description: `تم العثور على ${missingVariables.length} متغير مفقود`,
                    actions: [
                        'تهيئة المتغيرات في بداية التطبيق',
                        'التحقق من ترتيب تحميل الملفات',
                        'إضافة المتغيرات المفقودة',
                        'استخدام قيم افتراضية للمتغيرات'
                    ]
                });
            }

            displaySolutions(solutions);
        }

        function displaySolutions(solutions) {
            const solutionSection = document.getElementById('solutionSection');
            const suggestedSolutions = document.getElementById('suggestedSolutions');

            if (solutions.length === 0) return;

            let html = '';
            solutions.forEach((solution, index) => {
                const priorityClass = solution.priority === 'high' ? 'error-section' :
                                    solution.priority === 'medium' ? 'warning-section' : 'info-section';

                html += `<div class="${priorityClass}" style="margin: 15px 0;">`;
                html += `<h4>${solution.title}</h4>`;
                html += `<p>${solution.description}</p>`;
                html += `<h5>الإجراءات المقترحة:</h5>`;
                html += `<ul>`;
                solution.actions.forEach(action => {
                    html += `<li>${action}</li>`;
                });
                html += `</ul>`;
                html += `</div>`;
            });

            suggestedSolutions.innerHTML = html;
            solutionSection.style.display = 'block';
        }

        // ===== وظائف مساعدة =====

        function updateFileLoadingStatus(allLoaded) {
            const status = document.getElementById('fileLoadingStatus');
            if (allLoaded) {
                status.innerHTML = '<span class="status-indicator status-ok"></span>جميع الملفات محملة بنجاح';
            } else {
                status.innerHTML = '<span class="status-indicator status-error"></span>بعض الملفات مفقودة';
            }
        }

        function updateFunctionsStatus(allAvailable) {
            const status = document.getElementById('functionsStatus');
            if (allAvailable) {
                status.innerHTML = '<span class="status-indicator status-ok"></span>جميع الوظائف متاحة';
            } else {
                status.innerHTML = '<span class="status-indicator status-error"></span>بعض الوظائف مفقودة';
            }
        }

        function updateVariablesStatus(allOk) {
            const status = document.getElementById('variablesStatus');
            if (allOk) {
                status.innerHTML = '<span class="status-indicator status-ok"></span>جميع المتغيرات صحيحة';
            } else {
                status.innerHTML = '<span class="status-indicator status-error"></span>بعض المتغيرات مفقودة أو خاطئة';
            }
        }

        function simulateError() {
            logWarning('محاكاة خطأ للاختبار...');
            try {
                // محاكاة خطأ
                throw new Error('خطأ تجريبي للاختبار');
            } catch (error) {
                logError(`خطأ محاكى: ${error.message}`);
            }
        }

        function clearDiagnostic() {
            diagnosticResults = [];
            errorLog = [];
            systemErrors = [];

            document.getElementById('diagnosticResults').innerHTML = '';
            document.getElementById('errorLog').style.display = 'none';
            document.getElementById('solutionSection').style.display = 'none';

            // إعادة تعيين حالة الكروت
            document.getElementById('fileLoadingStatus').innerHTML = 'جاري الفحص...';
            document.getElementById('functionsStatus').innerHTML = 'جاري الفحص...';
            document.getElementById('variablesStatus').innerHTML = 'جاري الفحص...';
            document.getElementById('systemStatus').innerHTML = 'جاري الفحص...';

            logSuccess('تم مسح جميع نتائج التشخيص');
        }

        // تشغيل فحص أولي عند التحميل
        window.addEventListener('load', () => {
            setTimeout(() => {
                logSuccess('تم تحميل أدوات التشخيص بنجاح');
                logSuccess('اضغط على "تشغيل التشخيص الشامل" لبدء الفحص');
            }, 1000);
        });

        console.log('🔍 تم تحميل جميع أدوات التشخيص');
    </script>
</body>
</html>
