<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر التعديل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            margin: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            margin: 2px;
            font-size: 12px;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-danger {
            background: #dc3545;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار زر التعديل</h1>
        
        <div id="testResults"></div>
        
        <h3>اختبار الأزرار:</h3>
        <div style="margin: 20px 0;">
            <button class="btn btn-secondary" onclick="testEditEmployee(0)" title="تعديل البيانات">✏️ تعديل الموظف 0</button>
            <button class="btn btn-secondary" onclick="testEditEmployee(1)" title="تعديل البيانات">✏️ تعديل الموظف 1</button>
            <button class="btn btn-secondary" onclick="testEditEmployee(5)" title="تعديل البيانات">✏️ تعديل الموظف 5</button>
        </div>
        
        <h3>معلومات النظام:</h3>
        <div id="systemInfo"></div>
        
        <h3>سجل الاختبار:</h3>
        <div id="testLog" style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto;"></div>
    </div>

    <!-- تحميل ملفات النظام -->
    <script src="utils.js"></script>
    <script src="core.js"></script>
    <script src="sections.js"></script>
    <script src="functions.js"></script>
    <script src="features.js"></script>
    <script src="extras.js"></script>
    <script src="reports-advanced.js"></script>
    <script src="settings-functions.js"></script>
    <script src="app.js"></script>

    <script>
        let testResults = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logElement = document.getElementById('testLog');
            const typeIcon = {
                'info': 'ℹ️',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️'
            };
            
            logElement.innerHTML += `[${timestamp}] ${typeIcon[type]} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function addTestResult(testName, result, details = '') {
            testResults.push({ testName, result, details });
            updateTestResults();
        }
        
        function updateTestResults() {
            const resultsElement = document.getElementById('testResults');
            resultsElement.innerHTML = testResults.map(test => `
                <div class="test-result ${test.result}">
                    <strong>${test.testName}</strong>: ${test.result}
                    ${test.details ? `<br><small>${test.details}</small>` : ''}
                </div>
            `).join('');
        }
        
        function testEditEmployee(index) {
            log(`بدء اختبار تعديل الموظف رقم ${index}`, 'info');
            
            try {
                // التحقق من وجود البيانات
                if (!window.employees || window.employees.length === 0) {
                    log('لا توجد بيانات موظفين، تحميل البيانات التجريبية...', 'warning');
                    if (typeof loadTestData === 'function') {
                        loadTestData();
                        log(`تم تحميل ${window.employees.length} موظف`, 'success');
                    } else {
                        addTestResult(`تعديل الموظف ${index}`, 'error', 'وظيفة loadTestData غير متاحة');
                        return;
                    }
                }
                
                // التحقق من وجود الموظف
                if (index >= window.employees.length) {
                    addTestResult(`تعديل الموظف ${index}`, 'error', `الموظف رقم ${index} غير موجود (إجمالي الموظفين: ${window.employees.length})`);
                    log(`الموظف رقم ${index} غير موجود`, 'error');
                    return;
                }
                
                // التحقق من وجود وظيفة editEmployee
                if (typeof editEmployee !== 'function') {
                    addTestResult(`تعديل الموظف ${index}`, 'error', 'وظيفة editEmployee غير متاحة');
                    log('وظيفة editEmployee غير متاحة', 'error');
                    return;
                }
                
                // التحقق من وجود العناصر المطلوبة
                const requiredElements = [
                    'employeeName', 'employeeId', 'department', 'hireDate',
                    'annualLeave', 'carriedOverLeave', 'sickLeave', 'emergencyLeave'
                ];
                
                const missingElements = requiredElements.filter(id => !document.getElementById(id));
                
                if (missingElements.length > 0) {
                    addTestResult(`تعديل الموظف ${index}`, 'error', `عناصر مفقودة: ${missingElements.join(', ')}`);
                    log(`عناصر مفقودة: ${missingElements.join(', ')}`, 'error');
                    return;
                }
                
                // تنفيذ التعديل
                const employee = window.employees[index];
                log(`محاولة تعديل الموظف: ${employee.name} (${employee.id})`, 'info');
                
                editEmployee(index);
                
                // التحقق من نجاح التعديل
                setTimeout(() => {
                    const nameField = document.getElementById('employeeName');
                    if (nameField && nameField.value === employee.name) {
                        addTestResult(`تعديل الموظف ${index}`, 'success', `تم تحميل بيانات ${employee.name} بنجاح`);
                        log(`تم تحميل بيانات ${employee.name} في النموذج بنجاح`, 'success');
                    } else {
                        addTestResult(`تعديل الموظف ${index}`, 'error', 'فشل في تحميل البيانات في النموذج');
                        log('فشل في تحميل البيانات في النموذج', 'error');
                    }
                }, 500);
                
            } catch (error) {
                addTestResult(`تعديل الموظف ${index}`, 'error', error.message);
                log(`خطأ في تعديل الموظف ${index}: ${error.message}`, 'error');
            }
        }
        
        function updateSystemInfo() {
            const info = document.getElementById('systemInfo');
            info.innerHTML = `
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-size: 14px;">
                    <strong>معلومات النظام:</strong><br>
                    - عدد الموظفين: ${window.employees ? window.employees.length : 0}<br>
                    - وظيفة editEmployee: ${typeof editEmployee === 'function' ? '✅ متاحة' : '❌ غير متاحة'}<br>
                    - وظيفة loadTestData: ${typeof loadTestData === 'function' ? '✅ متاحة' : '❌ غير متاحة'}<br>
                    - عنصر employeeName: ${document.getElementById('employeeName') ? '✅ موجود' : '❌ مفقود'}<br>
                    - عنصر employeeId: ${document.getElementById('employeeId') ? '✅ موجود' : '❌ مفقود'}<br>
                    - متغير editingIndex: ${typeof editingIndex !== 'undefined' ? '✅ معرف' : '❌ غير معرف'}<br>
                </div>
            `;
        }
        
        // تشغيل الاختبارات عند التحميل
        window.addEventListener('load', () => {
            log('تم تحميل صفحة اختبار زر التعديل', 'info');
            updateSystemInfo();
            
            // تحميل البيانات التجريبية إذا لم تكن موجودة
            if (!window.employees || window.employees.length === 0) {
                if (typeof loadTestData === 'function') {
                    loadTestData();
                    log('تم تحميل البيانات التجريبية تلقائياً', 'success');
                    updateSystemInfo();
                }
            }
        });
        
        // تحديث معلومات النظام كل ثانية
        setInterval(updateSystemInfo, 1000);
    </script>
</body>
</html>
