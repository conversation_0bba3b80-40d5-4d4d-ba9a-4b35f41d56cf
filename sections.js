/**
 * نظام إدارة إجازات الموظفين المتطور
 * ملف وظائف الأقسام
 * يحتوي على وظائف تحميل محتوى الأقسام المختلفة
 */

// ===== وظائف تحميل الأقسام =====

/**
 * تحميل قسم إدارة الموظفين
 */
function loadEmployeesSection() {
    const employeesElement = document.getElementById('employees');
    employeesElement.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">➕ إضافة موظف جديد</h3>
            </div>
            <div class="card-body">
                <div class="form-row">
                    <div class="form-group">
                        <label>اسم الموظف *</label>
                        <input type="text" id="employeeName" placeholder="أدخل اسم الموظف" required
                               oninput="validateTextInput(this, /^[\\u0600-\\u06FFa-zA-Z\\s]+$/, 'اسم الموظف يجب أن يحتوي على أحرف عربية أو إنجليزية فقط')"
                               title="أدخل اسم الموظف (أحرف عربية أو إنجليزية فقط)">
                    </div>
                    <div class="form-group">
                        <label>الرقم الوظيفي *</label>
                        <input type="text" id="employeeId" placeholder="أدخل الرقم الوظيفي" required
                               oninput="validateTextInput(this, /^[a-zA-Z0-9]+$/, 'الرقم الوظيفي يجب أن يحتوي على أرقام وأحرف إنجليزية فقط')"
                               title="أدخل الرقم الوظيفي (أرقام وأحرف إنجليزية فقط)">
                    </div>
                    <div class="form-group">
                        <label>القسم *</label>
                        <input type="text" id="department" placeholder="أدخل القسم" required
                               oninput="validateTextInput(this, /^[\\u0600-\\u06FFa-zA-Z\\s]+$/, 'اسم القسم يجب أن يحتوي على أحرف عربية أو إنجليزية فقط')"
                               title="أدخل اسم القسم (أحرف عربية أو إنجليزية فقط)">
                    </div>
                    <div class="form-group">
                        <label>تاريخ التوظيف *</label>
                        <input type="date" id="hireDate" required
                               onchange="validateDateInput(this, false)"
                               title="أدخل تاريخ التوظيف">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>الرصيد السنوي (يوم)</label>
                        <input type="number" id="annualLeave" value="30" min="0" max="365" step="1"
                               oninput="validateNumberInput(this, 0, 365)"
                               title="أدخل رقم بين 0 و 365">
                    </div>
                    <div class="form-group">
                        <label>الرصيد المرحل (يوم)</label>
                        <input type="number" id="carriedOverLeave" value="0" min="0" max="365" step="1"
                               oninput="validateNumberInput(this, 0, 365)"
                               title="أدخل رقم بين 0 و 365 - الرصيد المتبقي من السنوات السابقة">
                    </div>
                    <div class="form-group">
                        <label>إجازة مرضية (يوم)</label>
                        <input type="number" id="sickLeave" value="15" min="0" max="90" step="1"
                               oninput="validateNumberInput(this, 0, 90)"
                               title="أدخل رقم بين 0 و 90">
                    </div>
                    <div class="form-group">
                        <label>إجازة طارئة (يوم)</label>
                        <input type="number" id="emergencyLeave" value="5" min="0" max="30" step="1"
                               oninput="validateNumberInput(this, 0, 30)"
                               title="أدخل رقم بين 0 و 30">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <button class="btn btn-primary btn-large" onclick="addEmployee()" id="addEmployeeBtn">
                            ➕ إضافة موظف
                        </button>
                    </div>
                    <div class="form-group">
                        <div style="padding: 15px; background: linear-gradient(135deg, #e8f5e8, #f0f8f0); border-radius: 8px; font-size: 13px; color: #2d5a2d; border: 1px solid #c8e6c9;">
                            💡 <strong>الرصيد المرحل:</strong> أيام الإجازة المتبقية من السنوات السابقة والتي لم تُستخدم
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="card-title">🔍 البحث والفلترة</h3>
            </div>
            <div class="card-body">
                <div class="form-row">
                    <div class="form-group">
                        <label>🔍 البحث في الموظفين</label>
                        <input type="text" id="searchInput" class="search-input" placeholder="ابحث بالاسم، الرقم الوظيفي، أو القسم..."
                               oninput="debouncedFilterEmployees()" title="ابحث في بيانات الموظفين">
                    </div>
                    <div class="form-group">
                        <label>📊 فلترة حسب الحالة</label>
                        <select id="statusFilter" onchange="filterEmployees()">
                            <option value="">جميع الحالات</option>
                            <option value="طبيعي">طبيعي</option>
                            <option value="رصيد منخفض">رصيد منخفض</option>
                            <option value="نفد الرصيد">نفد الرصيد</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>🏢 فلترة حسب القسم</label>
                        <select id="departmentFilter" onchange="filterEmployees()">
                            <option value="">جميع الأقسام</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-warning" onclick="clearFilters()" title="مسح جميع الفلاتر">
                            🗑️ مسح الفلاتر
                        </button>
                    </div>
                </div>
                <div style="margin-top: 15px; font-size: 14px; color: #666;">
                    <span id="filterResults">عرض جميع الموظفين</span>
                </div>

                <!-- البحث المتقدم -->
                <div class="advanced-search" id="advancedSearch" style="display: none;">
                    <h4 style="margin-bottom: 15px; color: #2c3e50;">🔍 البحث المتقدم</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label>تاريخ التوظيف من</label>
                            <input type="date" id="hireDateFrom" onchange="filterEmployees()">
                        </div>
                        <div class="form-group">
                            <label>تاريخ التوظيف إلى</label>
                            <input type="date" id="hireDateTo" onchange="filterEmployees()">
                        </div>
                        <div class="form-group">
                            <label>سنوات الخدمة (من)</label>
                            <input type="number" id="serviceYearsFrom" min="0" max="50" onchange="filterEmployees()">
                        </div>
                        <div class="form-group">
                            <label>سنوات الخدمة (إلى)</label>
                            <input type="number" id="serviceYearsTo" min="0" max="50" onchange="filterEmployees()">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>الرصيد المتبقي (من)</label>
                            <input type="number" id="remainingFrom" min="0" onchange="filterEmployees()">
                        </div>
                        <div class="form-group">
                            <label>الرصيد المتبقي (إلى)</label>
                            <input type="number" id="remainingTo" min="0" onchange="filterEmployees()">
                        </div>
                        <div class="form-group">
                            <button class="btn btn-secondary" onclick="clearAdvancedSearch()">مسح البحث المتقدم</button>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 10px;">
                    <button class="btn btn-primary btn-small" onclick="toggleAdvancedSearch()">
                        <span id="advancedSearchToggle">🔍 البحث المتقدم</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="card-title">👥 قائمة الموظفين</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="employeeTable" style="width: 100%; border-collapse: collapse; background: white; border-radius: 10px; overflow: hidden;">
                        <thead>
                            <tr style="background: linear-gradient(135deg, #34495e, #2c3e50); color: white;">
                                <th style="padding: 15px; font-weight: 600; text-align: center;">اسم الموظف</th>
                                <th style="padding: 15px; font-weight: 600; text-align: center;">الرقم الوظيفي</th>
                                <th style="padding: 15px; font-weight: 600; text-align: center;">القسم</th>
                                <th style="padding: 15px; font-weight: 600; text-align: center;">تاريخ التوظيف</th>
                                <th style="padding: 15px; font-weight: 600; text-align: center;">سنوات الخدمة</th>
                                <th style="padding: 15px; font-weight: 600; text-align: center;">الرصيد السنوي</th>
                                <th style="padding: 15px; font-weight: 600; text-align: center;">الرصيد المرحل</th>
                                <th style="padding: 15px; font-weight: 600; text-align: center;">إجمالي المتاح</th>
                                <th style="padding: 15px; font-weight: 600; text-align: center;">المستخدم</th>
                                <th style="padding: 15px; font-weight: 600; text-align: center;">المتبقي</th>
                                <th style="padding: 15px; font-weight: 600; text-align: center;">إجازة مرضية</th>
                                <th style="padding: 15px; font-weight: 600; text-align: center;">إجازة طارئة</th>
                                <th style="padding: 15px; font-weight: 600; text-align: center;">الحالة</th>
                                <th style="padding: 15px; font-weight: 600; text-align: center;">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="employeeTableBody">
                        </tbody>
                    </table>
                </div>
                <div id="paginationControls"></div>
            </div>
        </div>
    `;

    // تعيين تاريخ التوظيف الافتراضي لليوم
    document.getElementById('hireDate').value = new Date().toISOString().split('T')[0];

    updateTable();
    updateDepartmentFilter();
}

/**
 * تحميل قسم إدارة الإجازات
 */
function loadLeavesSection() {
    const leavesElement = document.getElementById('leaves');
    leavesElement.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">👥 اختيار الموظف</h3>
            </div>
            <div class="card-body">
                <div class="form-row">
                    <div class="form-group">
                        <label>اختر الموظف لإدارة إجازاته</label>
                        <select id="leaveEmployeeSelect" onchange="selectEmployeeForLeave()" style="font-size: 16px; padding: 15px;">
                            <option value="">-- اختر موظف --</option>
                        </select>
                    </div>
                </div>
                <div id="selectedEmployeeInfo" style="display: none; margin-top: 20px; padding: 20px; background: linear-gradient(135deg, #e8f5e8, #f0f8f0); border-radius: 12px; border: 1px solid #c8e6c9;">
                </div>
            </div>
        </div>

        <div id="leaveManagementSection" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">➕ إضافة إجازة جديدة</h3>
                </div>
                <div class="card-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label>نوع الإجازة</label>
                            <select id="leaveType" style="font-size: 16px; padding: 15px;">
                                <option value="annual">إجازة سنوية</option>
                                <option value="sick">إجازة مرضية</option>
                                <option value="emergency">إجازة طارئة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>عدد الأيام</label>
                            <input type="number" id="leaveDays" min="0.5" max="365" step="0.5"
                                   placeholder="أدخل عدد الأيام" style="font-size: 16px; padding: 15px;"
                                   oninput="validateNumberInput(this, 0.5, 365)"
                                   title="أدخل رقم بين 0.5 و 365">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>تاريخ البداية</label>
                            <input type="date" id="leaveStartDate" style="font-size: 16px; padding: 15px;"
                                   onchange="validateDateInput(this, true); calculateLeaveDays()"
                                   title="أدخل تاريخ بداية الإجازة">
                        </div>
                        <div class="form-group">
                            <label>تاريخ النهاية</label>
                            <input type="date" id="leaveEndDate" style="font-size: 16px; padding: 15px;"
                                   onchange="validateDateInput(this, true); calculateLeaveDays()"
                                   title="أدخل تاريخ نهاية الإجازة">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>سبب الإجازة</label>
                            <textarea id="leaveReason" placeholder="أدخل سبب الإجازة..."
                                      style="font-size: 16px; padding: 15px; min-height: 100px; resize: vertical;"></textarea>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <button class="btn btn-success btn-large" onclick="addLeaveRecord()" style="font-size: 16px; padding: 15px 30px;">
                                ➕ إضافة الإجازة
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">📋 سجل الإجازات</h3>
                </div>
                <div class="card-body">
                    <div id="leaveHistoryContent" style="max-height: 500px; overflow-y: auto;">
                        <p style="text-align: center; color: #7f8c8d; padding: 40px;">
                            لا توجد إجازات مسجلة لهذا الموظف
                        </p>
                    </div>
                </div>
            </div>
        </div>
    `;

    updateLeaveEmployeeSelect();
}

/**
 * تحميل قسم التقارير
 */
function loadReportsSection() {
    const reportsElement = document.getElementById('reports');
    reportsElement.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">📊 تقارير الإجازات</h3>
            </div>
            <div class="card-body">
                <!-- التقارير الأساسية -->
                <div class="form-row">
                    <div class="form-group">
                        <button class="btn btn-success btn-large" onclick="exportToCSV()">
                            📊 تصدير تقرير CSV
                        </button>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-primary btn-large" onclick="exportToJSON()">
                            💾 تصدير نسخة احتياطية JSON
                        </button>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-secondary btn-large" onclick="generateDetailedReport()">
                            📋 تقرير مفصل
                        </button>
                    </div>
                </div>

                <!-- التقارير المتقدمة -->
                <div class="advanced-reports-section">
                    <h3>🚀 التقارير المتقدمة</h3>
                    <div class="advanced-reports-buttons">
                        <button class="advanced-report-btn" onclick="showAdvancedReportDialog()">
                            📊 تقرير شامل متقدم
                        </button>
                        <button class="advanced-report-btn" onclick="generateComprehensiveReport()">
                            📄 PDF شامل
                        </button>
                        <button class="advanced-report-btn" onclick="generateAdvancedExcelReport()">
                            📈 Excel متعدد الأوراق
                        </button>
                        <button class="advanced-report-btn" onclick="showDepartmentReportOptions()">
                            🏢 تقارير الأقسام
                        </button>
                        <button class="advanced-report-btn" onclick="showEmployeeReportOptions()">
                            👤 تقارير فردية
                        </button>
                        <button class="advanced-report-btn" onclick="showReportPreview('comprehensive', {})">
                            👁️ معاينة التقارير
                        </button>
                    </div>
                </div>

                <div id="detailedReportContent" style="margin-top: 30px;">
                    <p style="text-align: center; color: #7f8c8d; padding: 40px;">
                        انقر على "تقرير مفصل" لعرض التقرير الشامل أو استخدم التقارير المتقدمة أعلاه
                    </p>
                </div>
            </div>
        </div>
    `;
}

/**
 * تحميل قسم الإعدادات
 */
function loadSettingsSection() {
    const settingsElement = document.getElementById('settings');
    settingsElement.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">📚 دليل المستخدم التفاعلي</h3>
            </div>
            <div class="card-body">
                <div class="form-row">
                    <div class="form-group">
                        <button class="btn btn-primary btn-large" onclick="startUserGuide()" style="background: linear-gradient(135deg, #6c5ce7, #a29bfe);">
                            📖 بدء الجولة التعريفية
                        </button>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-secondary btn-large" onclick="showKeyboardShortcuts()">
                            ⌨️ اختصارات لوحة المفاتيح
                        </button>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-success btn-large" onclick="showTips()">
                            💡 نصائح وحيل
                        </button>
                    </div>
                </div>

                <div id="guideContent" style="margin-top: 20px; display: none;">
                    <!-- محتوى الدليل سيتم تحميله هنا -->
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="card-title">🧪 البيانات التجريبية والاختبار</h3>
            </div>
            <div class="card-body">
                <div class="form-row">
                    <div class="form-group">
                        <button class="btn btn-large" onclick="loadTestData()"
                                style="background: linear-gradient(135deg, #9c27b0, #673ab7); font-size: 16px;">
                            🎯 تحميل بيانات تجريبية
                        </button>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-large" onclick="runComprehensiveTest()"
                                style="background: linear-gradient(135deg, #ff9800, #f57c00); font-size: 16px;">
                            🧪 اختبار شامل
                        </button>
                    </div>
                </div>

                <div style="margin-top: 20px; padding: 15px; background: #e3f2fd; border-radius: 8px; font-size: 13px; color: #1565c0;">
                    🧪 <strong>البيانات التجريبية تشمل:</strong> 5 موظفين من أقسام مختلفة، أسماء عربية وإنجليزية، قيم متنوعة للرصيد المرحل، حالات مختلفة (طبيعي/منخفض/نفد الرصيد)، سجلات إجازات شاملة.
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="card-title">🗂️ إدارة البيانات</h3>
            </div>
            <div class="card-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="importFile" class="btn btn-secondary btn-large" style="margin: 0; cursor: pointer;">
                            📁 استيراد نسخة احتياطية
                        </label>
                        <input type="file" id="importFile" style="display: none;" accept=".json" onchange="importData()">
                    </div>
                    <div class="form-group">
                        <button class="btn btn-warning btn-large" onclick="resetData()">
                            🔄 إعادة تعيين الإجازات
                        </button>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-danger btn-large" onclick="clearAllData()">
                            🗑️ مسح جميع البيانات
                        </button>
                    </div>
                </div>

                <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; font-size: 14px; color: #666;">
                    💡 <strong>ملاحظة:</strong> يتم حفظ البيانات تلقائياً في التخزين المحلي للمتصفح. البيانات ستبقى محفوظة حتى بعد إغلاق المتصفح.
                </div>
            </div>
        </div>
    `;
}
