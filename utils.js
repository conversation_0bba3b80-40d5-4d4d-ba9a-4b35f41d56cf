/**
 * نظام إدارة إجازات الموظفين المتطور
 * ملف الوظائف المساعدة والأدوات
 * يحتوي على الوظائف المساعدة والأدوات العامة
 */

// ===== المتغيرات العامة =====
window.employees = window.employees || [];
window.filteredEmployees = window.filteredEmployees || [];
window.currentSection = window.currentSection || 'dashboard';

// متغيرات التصفح
window.currentPage = window.currentPage || 1;
window.itemsPerPage = window.itemsPerPage || 50;
window.totalPages = window.totalPages || 1;

// متغيرات التحرير
window.editingIndex = window.editingIndex || -1;
window.currentEmployeeIndex = window.currentEmployeeIndex || -1;

// متغيرات الوضع الليلي
window.isDarkMode = window.isDarkMode || false;

// متغيرات الإشعارات
window.notificationQueue = window.notificationQueue || [];
window.notificationTimer = window.notificationTimer || null;

// متغيرات البحث
window.searchTimeout = window.searchTimeout || null;

// مراجع محلية للمتغيرات العامة للتوافق مع الكود القديم
let currentPage = window.currentPage;
let itemsPerPage = window.itemsPerPage;
let totalPages = window.totalPages;
let editingIndex = window.editingIndex;
let currentEmployeeIndex = window.currentEmployeeIndex;
let isDarkMode = window.isDarkMode;
let notificationQueue = window.notificationQueue;
let notificationTimer = window.notificationTimer;
let searchTimeout = window.searchTimeout;

// ===== وظائف مزامنة المتغيرات العامة =====

/**
 * مزامنة المتغيرات المحلية مع المتغيرات العامة
 */
function syncGlobalVariables() {
    window.currentPage = currentPage;
    window.itemsPerPage = itemsPerPage;
    window.totalPages = totalPages;
    window.editingIndex = editingIndex;
    window.currentEmployeeIndex = currentEmployeeIndex;
    window.isDarkMode = isDarkMode;
    window.notificationQueue = notificationQueue;
    window.notificationTimer = notificationTimer;
    window.searchTimeout = searchTimeout;
}

/**
 * تحديث المتغيرات المحلية من المتغيرات العامة
 */
function updateLocalVariables() {
    currentPage = window.currentPage;
    itemsPerPage = window.itemsPerPage;
    totalPages = window.totalPages;
    editingIndex = window.editingIndex;
    currentEmployeeIndex = window.currentEmployeeIndex;
    isDarkMode = window.isDarkMode;
    notificationQueue = window.notificationQueue;
    notificationTimer = window.notificationTimer;
    searchTimeout = window.searchTimeout;
}

// ===== Polyfills للمتصفحات القديمة =====
if (!Array.prototype.includes) {
    Array.prototype.includes = function(searchElement) {
        return this.indexOf(searchElement) !== -1;
    };
}

if (!Array.prototype.find) {
    Array.prototype.find = function(predicate) {
        for (let i = 0; i < this.length; i++) {
            if (predicate(this[i], i, this)) {
                return this[i];
            }
        }
        return undefined;
    };
}

if (!Array.prototype.findIndex) {
    Array.prototype.findIndex = function(predicate) {
        for (let i = 0; i < this.length; i++) {
            if (predicate(this[i], i, this)) {
                return i;
            }
        }
        return -1;
    };
}

if (!Object.assign) {
    Object.assign = function(target) {
        for (let i = 1; i < arguments.length; i++) {
            let source = arguments[i];
            for (let key in source) {
                if (source.hasOwnProperty(key)) {
                    target[key] = source[key];
                }
            }
        }
        return target;
    };
}

// ===== وظائف مساعدة عامة =====

/**
 * وظيفة debounce لتأخير تنفيذ الوظائف
 */
function debounce(func, delay) {
    return function(...args) {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => func.apply(this, args), delay);
    };
}

/**
 * حساب سنوات الخدمة
 */
function calculateYearsOfService(hireDate) {
    const hire = new Date(hireDate);
    const now = new Date();
    const years = Math.floor((now - hire) / (365.25 * 24 * 60 * 60 * 1000));
    return years;
}

/**
 * تحديد حالة الموظف بناءً على رصيد الإجازات
 */
function getEmployeeStatus(employee) {
    const totalAvailable = employee.annualLeave + employee.carriedOverLeave;
    const remainingTotal = totalAvailable - employee.usedAnnual;
    if (remainingTotal <= 0) return 'نفد الرصيد';
    if (remainingTotal <= 5) return 'رصيد منخفض';
    return 'طبيعي';
}

/**
 * تنظيف وتعقيم المدخلات
 */
function sanitizeInput(input) {
    if (typeof input !== 'string') return input;
    
    return input
        .replace(/[<>]/g, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+=/gi, '')
        .replace(/&/g, '&amp;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;')
        .trim();
}

/**
 * تعقيم بيانات الموظف
 */
function sanitizeEmployee(employee) {
    return {
        name: sanitizeInput(employee.name),
        id: sanitizeInput(employee.id),
        department: sanitizeInput(employee.department),
        hireDate: employee.hireDate,
        annualLeave: parseInt(employee.annualLeave) || 0,
        carriedOverLeave: parseInt(employee.carriedOverLeave) || 0,
        sickLeave: parseInt(employee.sickLeave) || 0,
        emergencyLeave: parseInt(employee.emergencyLeave) || 0,
        usedAnnual: parseFloat(employee.usedAnnual) || 0,
        usedSick: parseFloat(employee.usedSick) || 0,
        usedEmergency: parseFloat(employee.usedEmergency) || 0,
        leaveHistory: employee.leaveHistory ? employee.leaveHistory.map(record => ({
            id: record.id,
            type: sanitizeInput(record.type),
            typeName: sanitizeInput(record.typeName),
            days: parseFloat(record.days) || 0,
            startDate: record.startDate,
            endDate: record.endDate,
            reason: sanitizeInput(record.reason),
            addedDate: record.addedDate
        })) : []
    };
}

// ===== وظائف التحقق من صحة البيانات =====

/**
 * التحقق من صحة المدخلات الرقمية
 */
function validateNumberInput(input, min, max) {
    const value = parseFloat(input.value);
    
    input.value = input.value.replace(/[^0-9.-]/g, '');
    
    if (isNaN(value)) {
        input.style.borderColor = '#e74c3c';
        return false;
    }
    
    if (value < min || value > max) {
        input.style.borderColor = '#f39c12';
        input.title = `القيمة يجب أن تكون بين ${min} و ${max}`;
        return false;
    }
    
    input.style.borderColor = '#27ae60';
    input.title = '';
    return true;
}

/**
 * التحقق من صحة التواريخ
 */
function validateDateInput(dateInput, allowFuture = false) {
    const inputDate = new Date(dateInput.value);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (isNaN(inputDate.getTime())) {
        dateInput.style.borderColor = '#e74c3c';
        return false;
    }
    
    if (!allowFuture && inputDate > today) {
        dateInput.style.borderColor = '#f39c12';
        dateInput.title = 'التاريخ لا يمكن أن يكون في المستقبل';
        return false;
    }
    
    const fiftyYearsAgo = new Date();
    fiftyYearsAgo.setFullYear(fiftyYearsAgo.getFullYear() - 50);
    
    if (inputDate < fiftyYearsAgo) {
        dateInput.style.borderColor = '#f39c12';
        dateInput.title = 'التاريخ قديم جداً';
        return false;
    }
    
    dateInput.style.borderColor = '#27ae60';
    dateInput.title = '';
    return true;
}

/**
 * التحقق من صحة النصوص
 */
function validateTextInput(input, pattern, errorMessage) {
    const value = input.value.trim();
    
    if (!value) {
        input.style.borderColor = '#e74c3c';
        return false;
    }
    
    if (pattern && !pattern.test(value)) {
        input.style.borderColor = '#f39c12';
        input.title = errorMessage;
        return false;
    }
    
    input.style.borderColor = '#27ae60';
    input.title = '';
    return true;
}

// ===== وظائف التشفير والأمان =====

/**
 * تشفير بسيط للبيانات
 */
function simpleEncrypt(text) {
    const shifted = text.split('').map(char =>
        String.fromCharCode(char.charCodeAt(0) + 3)
    ).join('');
    return btoa(shifted);
}

/**
 * فك تشفير البيانات
 */
function simpleDecrypt(encryptedText) {
    try {
        const decoded = atob(encryptedText);
        return decoded.split('').map(char =>
            String.fromCharCode(char.charCodeAt(0) - 3)
        ).join('');
    } catch (error) {
        return null;
    }
}

/**
 * ضغط البيانات
 */
function compressData(data) {
    return JSON.stringify(data).replace(/\s+/g, ' ');
}

/**
 * إنشاء checksum للبيانات
 */
function generateChecksum(data) {
    const str = JSON.stringify(data);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
    }
    return hash.toString();
}

// ===== وظائف التصفح والفلترة =====

/**
 * حساب التصفح
 */
function calculatePagination() {
    const totalItems = window.filteredEmployees.length || window.employees.length;
    totalPages = Math.ceil(totalItems / itemsPerPage);
    if (currentPage > totalPages) currentPage = 1;
}

/**
 * الحصول على البيانات المصفحة
 */
function getPaginatedData() {
    const data = window.filteredEmployees.length > 0 || hasActiveFilters() ? window.filteredEmployees : window.employees;
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return data.slice(startIndex, endIndex);
}

/**
 * التحقق من وجود فلاتر نشطة
 */
function hasActiveFilters() {
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const departmentFilter = document.getElementById('departmentFilter');
    
    return (searchInput && searchInput.value.trim() !== '') ||
           (statusFilter && statusFilter.value !== '') ||
           (departmentFilter && departmentFilter.value !== '');
}

/**
 * تغيير الصفحة
 */
function changePage(page) {
    if (page < 1 || page > totalPages) return;
    currentPage = page;
    updateTable();
}

// ===== وظائف تحميل وحفظ البيانات =====

/**
 * تنظيف النسخ الاحتياطية القديمة
 */
function cleanOldBackups() {
    const keys = Object.keys(localStorage);
    const backupKeys = keys.filter(key => key.startsWith('backup_'));
    
    if (backupKeys.length > 7) {
        backupKeys.sort().slice(0, -7).forEach(key => {
            localStorage.removeItem(key);
        });
    }
}

/**
 * تحميل البيانات من النسخة الاحتياطية
 */
function loadFromBackup() {
    try {
        const keys = Object.keys(localStorage);
        const backupKeys = keys.filter(key => key.startsWith('backup_')).sort().reverse();
        
        for (const backupKey of backupKeys) {
            try {
                const backupData = localStorage.getItem(backupKey);
                const decryptedData = simpleDecrypt(backupData);
                if (decryptedData) {
                    const data = JSON.parse(decryptedData);
                    if (data.employees && Array.isArray(data.employees)) {
                        window.employees = data.employees;
                        showNotification(`تم استرداد البيانات من النسخة الاحتياطية: ${backupKey}`, 'info', 5000);
                        return true;
                    }
                }
            } catch (error) {
                continue;
            }
        }
        return false;
    } catch (error) {
        console.error('Error loading from backup:', error);
        return false;
    }
}

// ===== وظائف التصدير =====

/**
 * تحميل ملف
 */
function downloadFile(content, filename, contentType) {
    const blob = new Blob([content], { type: contentType });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
}
