/**
 * نظام إدارة إجازات الموظفين المتطور
 * ملف الوظائف المساعدة والأدوات
 * يحتوي على الوظائف المساعدة والأدوات العامة
 */

// ===== المتغيرات العامة =====
window.employees = window.employees || [];
window.filteredEmployees = window.filteredEmployees || [];
window.currentSection = window.currentSection || 'dashboard';

// متغيرات التصفح
window.currentPage = window.currentPage || 1;
window.itemsPerPage = window.itemsPerPage || 50;
window.totalPages = window.totalPages || 1;

// متغيرات التحرير
window.editingIndex = window.editingIndex || -1;
window.currentEmployeeIndex = window.currentEmployeeIndex || -1;

// متغيرات الوضع الليلي
window.isDarkMode = window.isDarkMode || false;

// متغيرات الإشعارات
window.notificationQueue = window.notificationQueue || [];
window.notificationTimer = window.notificationTimer || null;

// متغيرات البحث المحسن
window.searchTimeout = window.searchTimeout || null;
window.searchCache = window.searchCache || new Map();
window.lastSearchTerm = window.lastSearchTerm || '';
window.searchHistory = window.searchHistory || [];

// مراجع محلية للمتغيرات العامة للتوافق مع الكود القديم
let currentPage = window.currentPage;
let itemsPerPage = window.itemsPerPage;
let totalPages = window.totalPages;
let editingIndex = window.editingIndex;
let currentEmployeeIndex = window.currentEmployeeIndex;
let isDarkMode = window.isDarkMode;
let notificationQueue = window.notificationQueue;
let notificationTimer = window.notificationTimer;
let searchTimeout = window.searchTimeout;

// ===== وظائف مزامنة المتغيرات العامة =====

/**
 * مزامنة المتغيرات المحلية مع المتغيرات العامة
 */
function syncGlobalVariables() {
    window.currentPage = currentPage;
    window.itemsPerPage = itemsPerPage;
    window.totalPages = totalPages;
    window.editingIndex = editingIndex;
    window.currentEmployeeIndex = currentEmployeeIndex;
    window.isDarkMode = isDarkMode;
    window.notificationQueue = notificationQueue;
    window.notificationTimer = notificationTimer;
    window.searchTimeout = searchTimeout;
}

/**
 * تحديث المتغيرات المحلية من المتغيرات العامة
 */
function updateLocalVariables() {
    currentPage = window.currentPage;
    itemsPerPage = window.itemsPerPage;
    totalPages = window.totalPages;
    editingIndex = window.editingIndex;
    currentEmployeeIndex = window.currentEmployeeIndex;
    isDarkMode = window.isDarkMode;
    notificationQueue = window.notificationQueue;
    notificationTimer = window.notificationTimer;
    searchTimeout = window.searchTimeout;
}

// ===== Polyfills للمتصفحات القديمة =====
if (!Array.prototype.includes) {
    Array.prototype.includes = function(searchElement) {
        return this.indexOf(searchElement) !== -1;
    };
}

if (!Array.prototype.find) {
    Array.prototype.find = function(predicate) {
        for (let i = 0; i < this.length; i++) {
            if (predicate(this[i], i, this)) {
                return this[i];
            }
        }
        return undefined;
    };
}

if (!Array.prototype.findIndex) {
    Array.prototype.findIndex = function(predicate) {
        for (let i = 0; i < this.length; i++) {
            if (predicate(this[i], i, this)) {
                return i;
            }
        }
        return -1;
    };
}

if (!Object.assign) {
    Object.assign = function(target) {
        for (let i = 1; i < arguments.length; i++) {
            let source = arguments[i];
            for (let key in source) {
                if (source.hasOwnProperty(key)) {
                    target[key] = source[key];
                }
            }
        }
        return target;
    };
}

// ===== وظائف مساعدة عامة =====

/**
 * وظيفة debounce لتأخير تنفيذ الوظائف
 */
function debounce(func, delay) {
    return function(...args) {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => func.apply(this, args), delay);
    };
}

/**
 * حساب سنوات الخدمة
 */
function calculateYearsOfService(hireDate) {
    const hire = new Date(hireDate);
    const now = new Date();
    const years = Math.floor((now - hire) / (365.25 * 24 * 60 * 60 * 1000));
    return years;
}

/**
 * تحديد حالة الموظف بناءً على رصيد الإجازات
 */
function getEmployeeStatus(employee) {
    const totalAvailable = employee.annualLeave + employee.carriedOverLeave;
    const remainingTotal = totalAvailable - employee.usedAnnual;
    if (remainingTotal <= 0) return 'نفد الرصيد';
    if (remainingTotal <= 5) return 'رصيد منخفض';
    return 'طبيعي';
}

/**
 * تنظيف وتعقيم المدخلات
 */
function sanitizeInput(input) {
    if (typeof input !== 'string') return input;
    
    return input
        .replace(/[<>]/g, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+=/gi, '')
        .replace(/&/g, '&amp;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;')
        .trim();
}

/**
 * تعقيم بيانات الموظف
 */
function sanitizeEmployee(employee) {
    return {
        name: sanitizeInput(employee.name),
        id: sanitizeInput(employee.id),
        department: sanitizeInput(employee.department),
        hireDate: employee.hireDate,
        annualLeave: parseInt(employee.annualLeave) || 0,
        carriedOverLeave: parseInt(employee.carriedOverLeave) || 0,
        sickLeave: parseInt(employee.sickLeave) || 0,
        emergencyLeave: parseInt(employee.emergencyLeave) || 0,
        usedAnnual: parseFloat(employee.usedAnnual) || 0,
        usedSick: parseFloat(employee.usedSick) || 0,
        usedEmergency: parseFloat(employee.usedEmergency) || 0,
        leaveHistory: employee.leaveHistory ? employee.leaveHistory.map(record => ({
            id: record.id,
            type: sanitizeInput(record.type),
            typeName: sanitizeInput(record.typeName),
            days: parseFloat(record.days) || 0,
            startDate: record.startDate,
            endDate: record.endDate,
            reason: sanitizeInput(record.reason),
            addedDate: record.addedDate
        })) : []
    };
}

// ===== وظائف التحقق من صحة البيانات =====

/**
 * التحقق من صحة المدخلات الرقمية
 */
function validateNumberInput(input, min, max) {
    const value = parseFloat(input.value);
    
    input.value = input.value.replace(/[^0-9.-]/g, '');
    
    if (isNaN(value)) {
        input.style.borderColor = '#e74c3c';
        return false;
    }
    
    if (value < min || value > max) {
        input.style.borderColor = '#f39c12';
        input.title = `القيمة يجب أن تكون بين ${min} و ${max}`;
        return false;
    }
    
    input.style.borderColor = '#27ae60';
    input.title = '';
    return true;
}

/**
 * التحقق من صحة التواريخ
 */
function validateDateInput(dateInput, allowFuture = false) {
    const inputDate = new Date(dateInput.value);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (isNaN(inputDate.getTime())) {
        dateInput.style.borderColor = '#e74c3c';
        return false;
    }
    
    if (!allowFuture && inputDate > today) {
        dateInput.style.borderColor = '#f39c12';
        dateInput.title = 'التاريخ لا يمكن أن يكون في المستقبل';
        return false;
    }
    
    const fiftyYearsAgo = new Date();
    fiftyYearsAgo.setFullYear(fiftyYearsAgo.getFullYear() - 50);
    
    if (inputDate < fiftyYearsAgo) {
        dateInput.style.borderColor = '#f39c12';
        dateInput.title = 'التاريخ قديم جداً';
        return false;
    }
    
    dateInput.style.borderColor = '#27ae60';
    dateInput.title = '';
    return true;
}

/**
 * التحقق من صحة النصوص
 */
function validateTextInput(input, pattern, errorMessage) {
    const value = input.value.trim();
    
    if (!value) {
        input.style.borderColor = '#e74c3c';
        return false;
    }
    
    if (pattern && !pattern.test(value)) {
        input.style.borderColor = '#f39c12';
        input.title = errorMessage;
        return false;
    }
    
    input.style.borderColor = '#27ae60';
    input.title = '';
    return true;
}

// ===== وظائف التشفير والأمان =====

/**
 * تشفير بسيط للبيانات مع دعم النصوص العربية
 */
function simpleEncrypt(text) {
    try {
        // تحويل النص إلى UTF-8 bytes ثم إلى base64
        const utf8Bytes = new TextEncoder().encode(text);
        const shifted = Array.from(utf8Bytes).map(byte => byte + 3);
        const binaryString = String.fromCharCode.apply(null, shifted);
        return btoa(binaryString);
    } catch (error) {
        console.warn('فشل في التشفير، استخدام التشفير البديل:', error);
        // تشفير بديل بدون btoa
        return text.split('').map(char =>
            (char.charCodeAt(0) + 3).toString(16).padStart(4, '0')
        ).join('');
    }
}

/**
 * فك تشفير البيانات مع دعم النصوص العربية
 */
function simpleDecrypt(encryptedText) {
    try {
        // محاولة فك التشفير بالطريقة الجديدة
        const binaryString = atob(encryptedText);
        const shifted = Array.from(binaryString).map(char => char.charCodeAt(0) - 3);
        const utf8Bytes = new Uint8Array(shifted);
        return new TextDecoder().decode(utf8Bytes);
    } catch (error) {
        try {
            // محاولة فك التشفير بالطريقة البديلة
            if (encryptedText.length % 4 === 0 && /^[0-9a-f]+$/i.test(encryptedText)) {
                const chars = [];
                for (let i = 0; i < encryptedText.length; i += 4) {
                    const hex = encryptedText.substr(i, 4);
                    const charCode = parseInt(hex, 16) - 3;
                    chars.push(String.fromCharCode(charCode));
                }
                return chars.join('');
            }

            // محاولة فك التشفير بالطريقة القديمة
            const decoded = atob(encryptedText);
            return decoded.split('').map(char =>
                String.fromCharCode(char.charCodeAt(0) - 3)
            ).join('');
        } catch (fallbackError) {
            console.warn('فشل في فك التشفير:', fallbackError);
            return null;
        }
    }
}

/**
 * ضغط البيانات
 */
function compressData(data) {
    return JSON.stringify(data).replace(/\s+/g, ' ');
}

/**
 * إنشاء checksum للبيانات
 */
function generateChecksum(data) {
    const str = JSON.stringify(data);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
    }
    return hash.toString();
}

// ===== وظائف التصفح والفلترة =====

/**
 * حساب التصفح
 */
function calculatePagination() {
    const totalItems = window.filteredEmployees.length || window.employees.length;
    totalPages = Math.ceil(totalItems / itemsPerPage);
    if (currentPage > totalPages) currentPage = 1;
}

/**
 * الحصول على البيانات المصفحة
 */
function getPaginatedData() {
    const data = window.filteredEmployees.length > 0 || hasActiveFilters() ? window.filteredEmployees : window.employees;
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return data.slice(startIndex, endIndex);
}

/**
 * التحقق من وجود فلاتر نشطة
 */
function hasActiveFilters() {
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const departmentFilter = document.getElementById('departmentFilter');
    
    return (searchInput && searchInput.value.trim() !== '') ||
           (statusFilter && statusFilter.value !== '') ||
           (departmentFilter && departmentFilter.value !== '');
}

/**
 * تغيير الصفحة
 */
function changePage(page) {
    if (page < 1 || page > totalPages) return;
    currentPage = page;
    updateTable();
}

// ===== وظائف تحميل وحفظ البيانات =====

/**
 * تنظيف النسخ الاحتياطية القديمة
 */
function cleanOldBackups() {
    const keys = Object.keys(localStorage);
    const backupKeys = keys.filter(key => key.startsWith('backup_'));
    
    if (backupKeys.length > 7) {
        backupKeys.sort().slice(0, -7).forEach(key => {
            localStorage.removeItem(key);
        });
    }
}

/**
 * تحميل البيانات من النسخة الاحتياطية
 */
function loadFromBackup() {
    try {
        const keys = Object.keys(localStorage);
        const backupKeys = keys.filter(key => key.startsWith('backup_')).sort().reverse();
        
        for (const backupKey of backupKeys) {
            try {
                const backupData = localStorage.getItem(backupKey);
                const decryptedData = simpleDecrypt(backupData);
                if (decryptedData) {
                    const data = JSON.parse(decryptedData);
                    if (data.employees && Array.isArray(data.employees)) {
                        window.employees = data.employees;
                        showNotification(`تم استرداد البيانات من النسخة الاحتياطية: ${backupKey}`, 'info', 5000);
                        return true;
                    }
                }
            } catch (error) {
                continue;
            }
        }
        return false;
    } catch (error) {
        console.error('Error loading from backup:', error);
        return false;
    }
}

// ===== وظائف التصدير =====

/**
 * تحميل ملف
 */
function downloadFile(content, filename, contentType) {
    const blob = new Blob([content], { type: contentType });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
}

// ===== وظائف مساعدة إضافية =====

/**
 * حساب سنوات الخدمة
 */
function calculateYearsOfService(hireDate) {
    try {
        const hire = new Date(hireDate);
        const now = new Date();
        const diffTime = Math.abs(now - hire);
        const diffYears = Math.floor(diffTime / (1000 * 60 * 60 * 24 * 365.25));
        return diffYears;
    } catch (error) {
        return 0;
    }
}

/**
 * التحقق من توفر localStorage
 */
function isLocalStorageAvailable() {
    try {
        const test = 'test';
        localStorage.setItem(test, test);
        localStorage.removeItem(test);
        return true;
    } catch (e) {
        return false;
    }
}

// ===== وظائف البحث المحسن مع Debouncing وذاكرة التخزين المؤقت =====

/**
 * إنشاء مفتاح ذاكرة التخزين المؤقت للبحث
 */
function createSearchCacheKey(searchTerm, department, status) {
    return `${searchTerm.toLowerCase()}_${department}_${status}`;
}

/**
 * البحث المحسن مع ذاكرة التخزين المؤقت
 */
function enhancedSearch(searchTerm = '', department = '', status = '') {
    const cacheKey = createSearchCacheKey(searchTerm, department, status);

    // التحقق من ذاكرة التخزين المؤقت
    if (window.searchCache.has(cacheKey)) {
        const cachedResult = window.searchCache.get(cacheKey);
        window.filteredEmployees = cachedResult;
        updateTable();
        showSearchResults(cachedResult.length, window.employees.length, searchTerm);
        return;
    }

    // تنفيذ البحث
    const results = performAdvancedSearch(searchTerm, department, status);

    // حفظ النتيجة في ذاكرة التخزين المؤقت
    window.searchCache.set(cacheKey, results);

    // تنظيف ذاكرة التخزين المؤقت إذا أصبحت كبيرة
    if (window.searchCache.size > 50) {
        const firstKey = window.searchCache.keys().next().value;
        window.searchCache.delete(firstKey);
    }

    window.filteredEmployees = results;
    updateTable();
    showSearchResults(results.length, window.employees.length, searchTerm);
}

/**
 * تنفيذ البحث المتقدم
 */
function performAdvancedSearch(searchTerm, department, status) {
    if (!window.employees || window.employees.length === 0) {
        return [];
    }

    return window.employees.filter(emp => {
        // البحث في النص
        const matchesSearch = !searchTerm ||
            emp.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            emp.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
            emp.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (emp.position && emp.position.toLowerCase().includes(searchTerm.toLowerCase()));

        // فلترة حسب القسم
        const matchesDepartment = !department || emp.department === department;

        // فلترة حسب الحالة
        let matchesStatus = true;
        if (status) {
            const empStatus = getEmployeeStatus(emp);
            matchesStatus = empStatus.text === status;
        }

        return matchesSearch && matchesDepartment && matchesStatus;
    });
}

/**
 * البحث السريع مع Debouncing
 */
const debouncedSearch = debounce((searchTerm, department = '', status = '') => {
    enhancedSearch(searchTerm, department, status);

    // إضافة إلى تاريخ البحث
    if (searchTerm && searchTerm.length > 2) {
        addToSearchHistory(searchTerm);
    }
}, 300);

/**
 * إضافة مصطلح البحث إلى التاريخ
 */
function addToSearchHistory(searchTerm) {
    const term = searchTerm.trim().toLowerCase();
    if (!term || window.searchHistory.includes(term)) return;

    window.searchHistory.unshift(term);

    // الاحتفاظ بآخر 10 عمليات بحث فقط
    if (window.searchHistory.length > 10) {
        window.searchHistory = window.searchHistory.slice(0, 10);
    }

    // حفظ تاريخ البحث
    try {
        localStorage.setItem('searchHistory', JSON.stringify(window.searchHistory));
    } catch (error) {
        console.warn('فشل في حفظ تاريخ البحث:', error);
    }
}

/**
 * تحميل تاريخ البحث
 */
function loadSearchHistory() {
    try {
        const saved = localStorage.getItem('searchHistory');
        if (saved) {
            window.searchHistory = JSON.parse(saved);
        }
    } catch (error) {
        console.warn('فشل في تحميل تاريخ البحث:', error);
        window.searchHistory = [];
    }
}

/**
 * مسح ذاكرة التخزين المؤقت للبحث
 */
function clearSearchCache() {
    window.searchCache.clear();
    showNotification('تم مسح ذاكرة التخزين المؤقت للبحث', 'info', 3000);
}

/**
 * عرض نتائج البحث
 */
function showSearchResults(foundCount, totalCount, searchTerm) {
    if (searchTerm && searchTerm.trim()) {
        const message = foundCount > 0 ?
            `🔍 تم العثور على ${foundCount} من أصل ${totalCount} موظف` :
            `🔍 لم يتم العثور على نتائج للبحث: "${searchTerm}"`;

        const type = foundCount > 0 ? 'info' : 'warning';
        showNotification(message, type, 4000);
    }
}

/**
 * البحث الذكي مع اقتراحات
 */
function smartSearch(searchTerm) {
    const suggestions = [];

    if (searchTerm.length >= 2) {
        // البحث في الأسماء
        const nameMatches = window.employees.filter(emp =>
            emp.name.toLowerCase().includes(searchTerm.toLowerCase())
        ).slice(0, 5);

        // البحث في الأقسام
        const departments = [...new Set(window.employees.map(emp => emp.department))]
            .filter(dept => dept.toLowerCase().includes(searchTerm.toLowerCase()))
            .slice(0, 3);

        // إضافة الاقتراحات
        nameMatches.forEach(emp => {
            suggestions.push({
                type: 'employee',
                text: emp.name,
                subtitle: `${emp.id} - ${emp.department}`,
                action: () => enhancedSearch(emp.name)
            });
        });

        departments.forEach(dept => {
            suggestions.push({
                type: 'department',
                text: dept,
                subtitle: 'قسم',
                action: () => enhancedSearch('', dept)
            });
        });
    }

    return suggestions;
}

/**
 * تحديث مؤشرات الأداء للبحث
 */
function updateSearchPerformanceIndicators() {
    const searchStats = {
        cacheSize: window.searchCache.size,
        historySize: window.searchHistory.length,
        totalEmployees: window.employees.length,
        filteredEmployees: window.filteredEmployees.length
    };

    // عرض الإحصائيات في وحدة التحكم للمطورين
    console.log('📊 إحصائيات البحث:', searchStats);

    return searchStats;
}
