/**
 * نظام إدارة إجازات الموظفين المتطور
 * ملف الميزات المتقدمة
 * يحتوي على وظائف الإجازات والتقارير والرسوم البيانية والبيانات التجريبية
 */

// ===== نظام الإجازات المحسن =====

/**
 * أنواع الإجازات المتاحة مع التفاصيل
 */
const LEAVE_TYPES = {
    annual: {
        name: 'إجازة سنوية',
        icon: '🏖️',
        color: '#3498db',
        maxDays: 365,
        requiresApproval: true,
        allowPartial: true,
        description: 'الإجازة السنوية العادية'
    },
    sick: {
        name: 'إجازة مرضية',
        icon: '🏥',
        color: '#e74c3c',
        maxDays: 90,
        requiresApproval: false,
        allowPartial: true,
        description: 'إجازة للحالات المرضية'
    },
    emergency: {
        name: 'إجازة طارئة',
        icon: '🚨',
        color: '#f39c12',
        maxDays: 30,
        requiresApproval: true,
        allowPartial: false,
        description: 'إجازة للحالات الطارئة'
    },
    maternity: {
        name: 'إجازة أمومة',
        icon: '👶',
        color: '#e91e63',
        maxDays: 180,
        requiresApproval: true,
        allowPartial: false,
        description: 'إجازة الأمومة والولادة'
    },
    paternity: {
        name: 'إجازة أبوة',
        icon: '👨‍👶',
        color: '#9c27b0',
        maxDays: 14,
        requiresApproval: true,
        allowPartial: false,
        description: 'إجازة الأبوة'
    },
    hajj: {
        name: 'إجازة حج',
        icon: '🕋',
        color: '#4caf50',
        maxDays: 30,
        requiresApproval: true,
        allowPartial: false,
        description: 'إجازة أداء فريضة الحج'
    },
    study: {
        name: 'إجازة دراسية',
        icon: '📚',
        color: '#ff9800',
        maxDays: 365,
        requiresApproval: true,
        allowPartial: true,
        description: 'إجازة للدراسة والتدريب'
    },
    unpaid: {
        name: 'إجازة بدون راتب',
        icon: '💼',
        color: '#607d8b',
        maxDays: 365,
        requiresApproval: true,
        allowPartial: false,
        description: 'إجازة بدون راتب'
    },
    marriage: {
        name: 'إجازة زواج',
        icon: '💒',
        color: '#ff5722',
        maxDays: 7,
        requiresApproval: true,
        allowPartial: false,
        description: 'إجازة الزواج'
    },
    bereavement: {
        name: 'إجازة وفاة',
        icon: '🖤',
        color: '#424242',
        maxDays: 7,
        requiresApproval: false,
        allowPartial: false,
        description: 'إجازة الوفاة والعزاء'
    },
    compensation: {
        name: 'إجازة تعويضية',
        icon: '⏰',
        color: '#795548',
        maxDays: 30,
        requiresApproval: true,
        allowPartial: true,
        description: 'إجازة تعويضية للعمل الإضافي'
    }
};

/**
 * حالات الموافقة على الإجازات
 */
const APPROVAL_STATUS = {
    pending: { name: 'في انتظار الموافقة', icon: '⏳', color: '#f39c12' },
    approved: { name: 'موافق عليها', icon: '✅', color: '#27ae60' },
    rejected: { name: 'مرفوضة', icon: '❌', color: '#e74c3c' },
    cancelled: { name: 'ملغاة', icon: '🚫', color: '#95a5a6' }
};

/**
 * أنواع الإجازات الجزئية
 */
const PARTIAL_LEAVE_TYPES = {
    fullDay: { name: 'يوم كامل', hours: 8, description: 'إجازة يوم كامل' },
    halfDay: { name: 'نصف يوم', hours: 4, description: 'إجازة نصف يوم' },
    custom: { name: 'ساعات محددة', hours: 0, description: 'عدد ساعات مخصص' }
};

// ===== وظائف إدارة الإجازات =====

/**
 * فتح إدارة الإجازات لموظف معين
 */
function openLeaveManagement(index) {
    currentEmployeeIndex = index;
    showSection('leaves');
    setTimeout(() => {
        updateLeaveEmployeeSelect();
        document.getElementById('leaveEmployeeSelect').value = index;
        selectEmployeeForLeave();
    }, 300);
}

/**
 * تحديث قائمة اختيار الموظف للإجازات
 */
function updateLeaveEmployeeSelect() {
    const select = document.getElementById('leaveEmployeeSelect');
    if (!select) return;

    select.innerHTML = '<option value="">-- اختر موظف --</option>';
    window.employees.forEach((emp, index) => {
        const option = document.createElement('option');
        option.value = index;
        option.textContent = `${emp.name} (${emp.id}) - ${emp.department}`;
        select.appendChild(option);
    });

    // تحديث قائمة أنواع الإجازات
    updateLeaveTypeSelect();
}

/**
 * تحديث قائمة أنواع الإجازات
 */
function updateLeaveTypeSelect() {
    const select = document.getElementById('leaveType');
    if (!select) return;

    select.innerHTML = '<option value="">-- اختر نوع الإجازة --</option>';

    Object.keys(LEAVE_TYPES).forEach(key => {
        const leaveType = LEAVE_TYPES[key];
        const option = document.createElement('option');
        option.value = key;
        option.textContent = `${leaveType.icon} ${leaveType.name}`;
        option.style.color = leaveType.color;
        select.appendChild(option);
    });
}

/**
 * تحديث معلومات نوع الإجازة المختار
 */
function updateLeaveTypeInfo() {
    const leaveTypeSelect = document.getElementById('leaveType');
    const leaveTypeInfo = document.getElementById('leaveTypeInfo');
    const approvalSection = document.getElementById('approvalSection');

    if (!leaveTypeSelect.value) {
        leaveTypeInfo.style.display = 'none';
        approvalSection.style.display = 'none';
        validateLeaveInput();
        return;
    }

    const leaveType = LEAVE_TYPES[leaveTypeSelect.value];
    if (!leaveType) return;

    // عرض معلومات نوع الإجازة
    document.getElementById('leaveTypeIcon').textContent = leaveType.icon;
    document.getElementById('leaveTypeName').textContent = leaveType.name;
    document.getElementById('leaveTypeDescription').textContent = leaveType.description;
    document.getElementById('leaveTypeMaxDays').textContent = leaveType.maxDays;

    document.getElementById('leaveTypeApproval').innerHTML =
        leaveType.requiresApproval ?
        '<span style="color: #f39c12;">🔒 تحتاج موافقة</span>' :
        '<span style="color: #27ae60;">✅ لا تحتاج موافقة</span>';

    document.getElementById('leaveTypePartial').innerHTML =
        leaveType.allowPartial ?
        '<span style="color: #3498db;">⏰ تدعم الإجازة الجزئية</span>' :
        '<span style="color: #95a5a6;">📅 أيام كاملة فقط</span>';

    leaveTypeInfo.style.borderLeftColor = leaveType.color;
    leaveTypeInfo.style.display = 'block';

    // عرض قسم الموافقات إذا كان مطلوباً
    if (leaveType.requiresApproval) {
        approvalSection.style.display = 'block';
    } else {
        approvalSection.style.display = 'none';
    }

    // تحديث الحد الأقصى للأيام
    const leaveDaysInput = document.getElementById('leaveDays');
    leaveDaysInput.max = leaveType.maxDays;

    // تحديث خيارات الإجازة الجزئية
    updatePartialLeaveOptions();

    // تحديث الرصيد المتاح
    updateAvailableBalance();

    validateLeaveInput();
}

/**
 * تحديث خيارات الإجازة الجزئية
 */
function updatePartialLeaveOptions() {
    const leaveTypeSelect = document.getElementById('leaveType');
    const partialLeaveSelect = document.getElementById('partialLeaveType');

    if (!leaveTypeSelect.value) return;

    const leaveType = LEAVE_TYPES[leaveTypeSelect.value];

    if (!leaveType.allowPartial) {
        partialLeaveSelect.value = 'fullDay';
        partialLeaveSelect.disabled = true;
        partialLeaveSelect.style.opacity = '0.6';
    } else {
        partialLeaveSelect.disabled = false;
        partialLeaveSelect.style.opacity = '1';
    }

    updatePartialLeaveInfo();
}

/**
 * تحديث معلومات الإجازة الجزئية
 */
function updatePartialLeaveInfo() {
    const partialType = document.getElementById('partialLeaveType').value;
    const daysGroup = document.getElementById('daysGroup');
    const hoursGroup = document.getElementById('hoursGroup');

    if (partialType === 'custom') {
        daysGroup.style.display = 'none';
        hoursGroup.style.display = 'block';
    } else {
        daysGroup.style.display = 'block';
        hoursGroup.style.display = 'none';

        // تحديث قيمة الأيام بناءً على النوع
        const leaveDaysInput = document.getElementById('leaveDays');
        if (partialType === 'halfDay') {
            leaveDaysInput.value = '0.5';
            leaveDaysInput.step = '0.5';
        } else {
            leaveDaysInput.step = '1';
        }
    }

    validateLeaveInput();
}

/**
 * تحويل الساعات إلى أيام
 */
function convertHoursToDays() {
    const hoursInput = document.getElementById('leaveHours');
    const daysInput = document.getElementById('leaveDays');

    if (hoursInput.value) {
        const hours = parseFloat(hoursInput.value);
        const days = hours / 8; // 8 ساعات = يوم واحد
        daysInput.value = days.toFixed(2);
    }

    validateLeaveInput();
}

/**
 * تحديث الرصيد المتاح
 */
function updateAvailableBalance() {
    const employeeSelect = document.getElementById('leaveEmployeeSelect');
    const leaveTypeSelect = document.getElementById('leaveType');
    const balanceInput = document.getElementById('availableBalance');

    if (!employeeSelect.value || !leaveTypeSelect.value) {
        balanceInput.value = '';
        return;
    }

    const employee = window.employees[employeeSelect.value];
    const leaveTypeKey = leaveTypeSelect.value;

    let balance = 0;
    switch(leaveTypeKey) {
        case 'annual':
            balance = (employee.annualLeave + employee.carriedOverLeave) - employee.usedAnnual;
            break;
        case 'sick':
            balance = employee.sickLeave - employee.usedSick;
            break;
        case 'emergency':
            balance = employee.emergencyLeave - employee.usedEmergency;
            break;
        default:
            balance = LEAVE_TYPES[leaveTypeKey]?.maxDays || 0;
    }

    balanceInput.value = `${balance} يوم متاح`;
    balanceInput.style.color = balance > 0 ? '#27ae60' : '#e74c3c';
}

/**
 * اختيار موظف لإدارة الإجازات
 */
function selectEmployeeForLeave() {
    const select = document.getElementById('leaveEmployeeSelect');
    const selectedIndex = parseInt(select.value);

    if (isNaN(selectedIndex) || selectedIndex < 0 || selectedIndex >= window.employees.length) {
        document.getElementById('selectedEmployeeInfo').style.display = 'none';
        document.getElementById('leaveManagementSection').style.display = 'none';
        return;
    }

    currentEmployeeIndex = selectedIndex;
    const employee = window.employees[selectedIndex];

    updateSelectedEmployeeInfo(employee);
    updateLeaveHistory();

    document.getElementById('selectedEmployeeInfo').style.display = 'block';
    document.getElementById('leaveManagementSection').style.display = 'block';
}

/**
 * تحديث معلومات الموظف المختار
 */
function updateSelectedEmployeeInfo(employee) {
    const infoDiv = document.getElementById('selectedEmployeeInfo');
    const totalAvailable = employee.annualLeave + employee.carriedOverLeave;
    const remainingTotal = totalAvailable - employee.usedAnnual;

    infoDiv.innerHTML = `
        <h4 style="margin: 0 0 15px 0; color: #2c3e50;">📊 معلومات الرصيد - ${employee.name}</h4>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <div>
                <strong>الرصيد السنوي:</strong> ${employee.annualLeave} يوم<br>
                <strong>الرصيد المرحل:</strong> <span style="color: #27ae60; font-weight: 600;">${employee.carriedOverLeave} يوم</span><br>
                <strong>إجمالي المتاح:</strong> <strong>${totalAvailable} يوم</strong>
            </div>
            <div>
                <strong>المستخدم:</strong> ${employee.usedAnnual} يوم<br>
                <strong>المتبقي:</strong> <strong style="color: ${remainingTotal <= 5 ? '#e74c3c' : '#27ae60'}">${remainingTotal} يوم</strong><br>
                <strong>الحالة:</strong> <span style="color: ${getEmployeeStatus(employee) === 'طبيعي' ? '#27ae60' : '#e74c3c'}">${getEmployeeStatus(employee)}</span>
            </div>
        </div>
    `;
}

/**
 * التحقق من صحة مدخلات الإجازة
 */
function validateLeaveInput() {
    const employeeSelect = document.getElementById('leaveEmployeeSelect');
    const leaveTypeSelect = document.getElementById('leaveType');
    const leaveDaysInput = document.getElementById('leaveDays');
    const leaveHoursInput = document.getElementById('leaveHours');
    const startDateInput = document.getElementById('leaveStartDate');
    const reasonInput = document.getElementById('leaveReason');
    const partialType = document.getElementById('partialLeaveType').value;

    const addBtn = document.getElementById('addLeaveBtn');
    const previewBtn = document.getElementById('previewBtn');

    let isValid = true;

    // التحقق من الحقول المطلوبة
    if (!employeeSelect.value || !leaveTypeSelect.value || !startDateInput.value || !reasonInput.value.trim()) {
        isValid = false;
    }

    // التحقق من الأيام أو الساعات
    if (partialType === 'custom') {
        if (!leaveHoursInput.value || parseFloat(leaveHoursInput.value) <= 0) {
            isValid = false;
        }
    } else {
        if (!leaveDaysInput.value || parseFloat(leaveDaysInput.value) <= 0) {
            isValid = false;
        }
    }

    // تفعيل/تعطيل الأزرار
    addBtn.disabled = !isValid;
    previewBtn.disabled = !isValid;

    if (isValid) {
        addBtn.style.opacity = '1';
        previewBtn.style.opacity = '1';
    } else {
        addBtn.style.opacity = '0.6';
        previewBtn.style.opacity = '0.6';
    }
}

/**
 * حساب تاريخ النهاية تلقائياً
 */
function calculateEndDate() {
    const startDate = document.getElementById('leaveStartDate').value;
    const leaveDays = parseFloat(document.getElementById('leaveDays').value);

    if (startDate && leaveDays) {
        const start = new Date(startDate);
        const end = new Date(start);
        end.setDate(start.getDate() + leaveDays - 1);

        document.getElementById('leaveEndDate').value = end.toISOString().split('T')[0];
    }
}

/**
 * حساب الأيام من التواريخ
 */
function calculateDaysFromDates() {
    const startDate = document.getElementById('leaveStartDate').value;
    const endDate = document.getElementById('leaveEndDate').value;

    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);

        if (end >= start) {
            const diffTime = end - start;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
            document.getElementById('leaveDays').value = diffDays;
        }
    }

    validateLeaveInput();
}

/**
 * فحص أيام نهاية الأسبوع
 */
function checkWeekends() {
    const startDate = document.getElementById('leaveStartDate').value;
    const endDate = document.getElementById('leaveEndDate').value;
    const weekendInfo = document.getElementById('weekendInfo');

    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        let hasWeekends = false;

        for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
            if (d.getDay() === 5 || d.getDay() === 6) { // الجمعة والسبت
                hasWeekends = true;
                break;
            }
        }

        weekendInfo.style.display = hasWeekends ? 'block' : 'none';
    } else {
        weekendInfo.style.display = 'none';
    }
}

/**
 * معاينة طلب الإجازة
 */
function previewLeaveRequest() {
    const employeeSelect = document.getElementById('leaveEmployeeSelect');
    const leaveTypeSelect = document.getElementById('leaveType');
    const partialType = document.getElementById('partialLeaveType').value;
    const leaveDays = document.getElementById('leaveDays').value;
    const leaveHours = document.getElementById('leaveHours').value;
    const startDate = document.getElementById('leaveStartDate').value;
    const endDate = document.getElementById('leaveEndDate').value;
    const reason = document.getElementById('leaveReason').value;
    const directManager = document.getElementById('directManager').value;
    const approvalNotes = document.getElementById('approvalNotes').value;

    const employee = window.employees[employeeSelect.value];
    const leaveType = LEAVE_TYPES[leaveTypeSelect.value];

    let duration;
    if (partialType === 'custom') {
        duration = `${leaveHours} ساعة (${(parseFloat(leaveHours) / 8).toFixed(2)} يوم)`;
    } else {
        duration = `${leaveDays} يوم`;
    }

    const previewContent = `
        <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #ddd;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h4 style="color: #2c3e50; margin: 0;">طلب إجازة</h4>
                <small style="color: #7f8c8d;">معاينة الطلب قبل الإرسال</small>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                <div>
                    <strong>اسم الموظف:</strong><br>
                    ${employee.name}
                </div>
                <div>
                    <strong>الرقم الوظيفي:</strong><br>
                    ${employee.id}
                </div>
                <div>
                    <strong>القسم:</strong><br>
                    ${employee.department}
                </div>
                <div>
                    <strong>نوع الإجازة:</strong><br>
                    ${leaveType.icon} ${leaveType.name}
                </div>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                <div>
                    <strong>تاريخ البداية:</strong><br>
                    ${new Date(startDate).toLocaleDateString('ar-SA')}
                </div>
                <div>
                    <strong>تاريخ النهاية:</strong><br>
                    ${endDate ? new Date(endDate).toLocaleDateString('ar-SA') : 'غير محدد'}
                </div>
                <div>
                    <strong>المدة:</strong><br>
                    ${duration}
                </div>
                <div>
                    <strong>نوع الإجازة:</strong><br>
                    ${PARTIAL_LEAVE_TYPES[partialType].name}
                </div>
            </div>

            <div style="margin-bottom: 20px;">
                <strong>سبب الإجازة:</strong><br>
                <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 5px;">
                    ${reason}
                </div>
            </div>

            ${leaveType.requiresApproval ? `
                <div style="background: #e8f5e8; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
                    <strong style="color: #27ae60;">معلومات الموافقة:</strong><br>
                    <div style="margin-top: 10px;">
                        <strong>المدير المباشر:</strong> ${directManager || 'غير محدد'}<br>
                        ${approvalNotes ? `<strong>ملاحظات:</strong> ${approvalNotes}` : ''}
                    </div>
                </div>
            ` : ''}

            <div style="text-align: center; margin-top: 20px;">
                <small style="color: #7f8c8d;">
                    تاريخ الطلب: ${new Date().toLocaleDateString('ar-SA')}
                </small>
            </div>
        </div>
    `;

    document.getElementById('previewContent').innerHTML = previewContent;
    document.getElementById('leavePreview').style.display = 'block';
}

/**
 * مسح نموذج الإجازة
 */
function clearLeaveForm() {
    document.getElementById('leaveType').value = '';
    document.getElementById('partialLeaveType').value = 'fullDay';
    document.getElementById('leaveDays').value = '';
    document.getElementById('leaveHours').value = '';
    document.getElementById('leaveStartDate').value = '';
    document.getElementById('leaveEndDate').value = '';
    document.getElementById('leaveReason').value = '';
    document.getElementById('directManager').value = '';
    document.getElementById('approvalNotes').value = '';
    document.getElementById('availableBalance').value = '';

    document.getElementById('leaveTypeInfo').style.display = 'none';
    document.getElementById('approvalSection').style.display = 'none';
    document.getElementById('weekendInfo').style.display = 'none';
    document.getElementById('leavePreview').style.display = 'none';
    document.getElementById('daysGroup').style.display = 'block';
    document.getElementById('hoursGroup').style.display = 'none';

    validateLeaveInput();
}

/**
 * إضافة سجل إجازة محسن
 */
function addAdvancedLeaveRecord() {
    const employeeSelect = document.getElementById('leaveEmployeeSelect');
    const leaveTypeSelect = document.getElementById('leaveType');
    const partialType = document.getElementById('partialLeaveType').value;
    const leaveDaysInput = document.getElementById('leaveDays');
    const leaveHoursInput = document.getElementById('leaveHours');
    const startDateInput = document.getElementById('leaveStartDate');
    const endDateInput = document.getElementById('leaveEndDate');
    const reasonInput = document.getElementById('leaveReason');
    const directManagerInput = document.getElementById('directManager');
    const approvalNotesInput = document.getElementById('approvalNotes');

    if (!employeeSelect.value) {
        showNotification('❌ يرجى اختيار موظف', 'warning', 5000);
        return;
    }

    const employee = window.employees[employeeSelect.value];
    const leaveTypeKey = leaveTypeSelect.value;
    const leaveType = LEAVE_TYPES[leaveTypeKey];

    if (!leaveType) {
        showNotification('❌ يرجى اختيار نوع الإجازة', 'warning', 5000);
        return;
    }

    // حساب الأيام
    let totalDays;
    if (partialType === 'custom') {
        const hours = parseFloat(leaveHoursInput.value);
        if (!hours || hours <= 0) {
            showNotification('❌ يرجى إدخال عدد ساعات صحيح', 'warning', 5000);
            return;
        }
        totalDays = hours / 8;
    } else {
        totalDays = parseFloat(leaveDaysInput.value);
        if (!totalDays || totalDays <= 0) {
            showNotification('❌ يرجى إدخال عدد أيام صحيح', 'warning', 5000);
            return;
        }
    }

    const startDate = startDateInput.value;
    const endDate = endDateInput.value;
    const reason = reasonInput.value.trim();

    if (!startDate || !reason) {
        showNotification('❌ يرجى ملء جميع الحقول المطلوبة', 'warning', 5000);
        return;
    }

    // التحقق من الرصيد المتاح
    let currentUsed = 0;
    let maxAllowed = 0;

    switch(leaveTypeKey) {
        case 'annual':
            currentUsed = employee.usedAnnual || 0;
            maxAllowed = (employee.annualLeave || 30) + (employee.carriedOverLeave || 0);
            break;
        case 'sick':
            currentUsed = employee.usedSick || 0;
            maxAllowed = employee.sickLeave || 15;
            break;
        case 'emergency':
            currentUsed = employee.usedEmergency || 0;
            maxAllowed = employee.emergencyLeave || 5;
            break;
        default:
            maxAllowed = leaveType.maxDays;
    }

    if (currentUsed + totalDays > maxAllowed) {
        const available = maxAllowed - currentUsed;
        showNotification(`❌ الرصيد المتاح لـ${leaveType.name} غير كافي. المتاح: ${available.toFixed(2)} يوم`, 'warning', 8000);
        return;
    }

    // إنشاء سجل الإجازة
    const leaveRecord = {
        id: Date.now(),
        type: leaveTypeKey,
        typeName: leaveType.name,
        icon: leaveType.icon,
        days: totalDays,
        originalDays: partialType === 'custom' ? parseFloat(leaveHoursInput.value) : totalDays,
        partialType: partialType,
        startDate: startDate,
        endDate: endDate || startDate,
        reason: reason,
        directManager: directManagerInput.value.trim(),
        approvalNotes: approvalNotesInput.value.trim(),
        status: leaveType.requiresApproval ? 'pending' : 'approved',
        addedDate: new Date().toISOString(),
        addedBy: 'النظام'
    };

    // إضافة السجل للموظف
    if (!employee.leaveHistory) {
        employee.leaveHistory = [];
    }
    employee.leaveHistory.push(leaveRecord);

    // تحديث الرصيد المستخدم
    switch(leaveTypeKey) {
        case 'annual':
            employee.usedAnnual = (employee.usedAnnual || 0) + totalDays;
            break;
        case 'sick':
            employee.usedSick = (employee.usedSick || 0) + totalDays;
            break;
        case 'emergency':
            employee.usedEmergency = (employee.usedEmergency || 0) + totalDays;
            break;
    }

    // تحديث الواجهة
    updateTable();
    updateStats();
    updateQuickEmployeeView();
    updateCharts();
    updateSelectedEmployeeInfo(employee);
    updateLeaveHistory();
    updateAvailableBalance();
    saveData();

    // رسالة النجاح
    let successMessage = `✅ تم إضافة ${leaveType.name} بنجاح`;
    if (partialType === 'custom') {
        successMessage += `\n📊 المدة: ${leaveHoursInput.value} ساعة (${totalDays.toFixed(2)} يوم)`;
    } else {
        successMessage += `\n📊 المدة: ${totalDays} يوم`;
    }
    successMessage += `\n👤 الموظف: ${employee.name}`;

    if (leaveType.requiresApproval) {
        successMessage += `\n⏳ الحالة: في انتظار الموافقة`;
    }

    showNotification(successMessage, 'success', 8000);

    // مسح النموذج
    clearLeaveForm();
}

/**
 * إضافة سجل إجازة (الوظيفة القديمة للتوافق)
 */
function addLeaveRecord() {
    // استخدام الوظيفة الجديدة
    addAdvancedLeaveRecord();
}

/**
 * تحديث سجل الإجازات المحسن
 */
function updateLeaveHistory() {
    const employeeSelect = document.getElementById('leaveEmployeeSelect');
    const historyContent = document.getElementById('leaveHistoryContent');

    if (!employeeSelect.value) {
        historyContent.innerHTML = '<p style="text-align: center; color: #7f8c8d; padding: 40px;">يرجى اختيار موظف لعرض سجل الإجازات</p>';
        return;
    }

    const employee = window.employees[employeeSelect.value];

    if (!employee.leaveHistory || employee.leaveHistory.length === 0) {
        historyContent.innerHTML = '<p style="text-align: center; color: #7f8c8d; padding: 40px;">لا توجد إجازات مسجلة لهذا الموظف</p>';
        return;
    }

    const sortedHistory = [...employee.leaveHistory].sort((a, b) => new Date(b.addedDate) - new Date(a.addedDate));

    historyContent.innerHTML = sortedHistory.map(record => {
        const statusInfo = APPROVAL_STATUS[record.status] || APPROVAL_STATUS.approved;
        const leaveTypeInfo = LEAVE_TYPES[record.type] || { icon: '📋', color: '#3498db' };

        let durationText = `${record.days} يوم`;
        if (record.partialType === 'custom') {
            durationText = `${record.originalDays} ساعة (${record.days.toFixed(2)} يوم)`;
        } else if (record.partialType === 'halfDay') {
            durationText = `نصف يوم (${record.days} يوم)`;
        }

        return `
            <div class="card-with-status" style="padding: 15px; margin: 10px 0; border: 1px solid #e9ecef; border-radius: 8px; background: white; border-left: 4px solid ${leaveTypeInfo.color}; --status-color: ${leaveTypeInfo.color};">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="font-size: 20px;">${record.icon || leaveTypeInfo.icon}</span>
                        <strong style="color: #2c3e50;">${record.typeName}</strong>
                        <div class="status-indicator ${statusInfo.class}" style="font-size: 11px; padding: 3px 8px;">
                            <span class="status-icon"></span>
                            <span style="margin-left: 3px;">${statusInfo.icon}</span>
                            <span>${statusInfo.name}</span>
                        </div>
                    </div>
                    <div style="display: flex; gap: 5px;">
                        <button class="btn btn-small btn-info" onclick="editLeaveRecord(${record.id})" title="تعديل الإجازة">
                            <span style="margin-left: 3px;">✏️</span>تعديل
                        </button>
                        <button class="btn btn-small btn-danger" onclick="deleteLeaveRecord(${record.id})" title="حذف الإجازة">
                            <span style="margin-left: 3px;">🗑️</span>حذف
                        </button>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-bottom: 10px; font-size: 14px;">
                    <div>
                        <strong>📅 تاريخ البداية:</strong><br>
                        ${new Date(record.startDate).toLocaleDateString('ar-SA')}
                    </div>
                    <div>
                        <strong>📅 تاريخ النهاية:</strong><br>
                        ${new Date(record.endDate).toLocaleDateString('ar-SA')}
                    </div>
                    <div>
                        <strong>⏱️ المدة:</strong><br>
                        ${durationText}
                    </div>
                    <div>
                        <strong>📋 نوع الإجازة:</strong><br>
                        ${PARTIAL_LEAVE_TYPES[record.partialType]?.name || 'يوم كامل'}
                    </div>
                </div>

                <div style="margin-bottom: 10px;">
                    <strong>📝 السبب:</strong><br>
                    <div style="background: #f8f9fa; padding: 8px; border-radius: 4px; margin-top: 5px;">
                        ${record.reason}
                    </div>
                </div>

                ${record.directManager ? `
                    <div style="margin-bottom: 10px;">
                        <strong>👨‍💼 المدير المباشر:</strong> ${record.directManager}
                    </div>
                ` : ''}

                ${record.approvalNotes ? `
                    <div style="margin-bottom: 10px;">
                        <strong>📋 ملاحظات الموافقة:</strong><br>
                        <div style="background: #e8f5e8; padding: 8px; border-radius: 4px; margin-top: 5px;">
                            ${record.approvalNotes}
                        </div>
                    </div>
                ` : ''}

                <div style="display: flex; justify-content: between; align-items: center; font-size: 12px; color: #95a5a6; border-top: 1px solid #f1f1f1; padding-top: 10px; margin-top: 10px;">
                    <span>تم الإضافة: ${new Date(record.addedDate).toLocaleDateString('ar-SA')} بواسطة ${record.addedBy || 'النظام'}</span>
                    ${record.lastModified ? `<span>آخر تعديل: ${new Date(record.lastModified).toLocaleDateString('ar-SA')}</span>` : ''}
                </div>
            </div>
        `;
    }).join('');
}

/**
 * تعديل سجل إجازة
 */
function editLeaveRecord(recordId) {
    const employeeSelect = document.getElementById('leaveEmployeeSelect');
    if (!employeeSelect.value) return;

    const employee = window.employees[employeeSelect.value];
    const record = employee.leaveHistory.find(r => r.id === recordId);

    if (!record) {
        showNotification('❌ لم يتم العثور على سجل الإجازة', 'error', 5000);
        return;
    }

    // ملء النموذج ببيانات الإجازة الحالية
    document.getElementById('leaveType').value = record.type;
    document.getElementById('partialLeaveType').value = record.partialType || 'fullDay';
    document.getElementById('leaveDays').value = record.days;
    document.getElementById('leaveStartDate').value = record.startDate;
    document.getElementById('leaveEndDate').value = record.endDate;
    document.getElementById('leaveReason').value = record.reason;
    document.getElementById('directManager').value = record.directManager || '';
    document.getElementById('approvalNotes').value = record.approvalNotes || '';

    if (record.partialType === 'custom') {
        document.getElementById('leaveHours').value = record.originalDays;
    }

    // تحديث الواجهة
    updateLeaveTypeInfo();
    updatePartialLeaveInfo();
    updateAvailableBalance();
    validateLeaveInput();

    // تغيير نص الزر للتعديل
    const addBtn = document.getElementById('addLeaveBtn');
    addBtn.textContent = '✏️ تحديث الإجازة';
    addBtn.onclick = () => updateLeaveRecord(recordId);

    // إضافة زر إلغاء
    const cancelBtn = document.createElement('button');
    cancelBtn.className = 'btn btn-secondary';
    cancelBtn.textContent = '❌ إلغاء التعديل';
    cancelBtn.style.marginLeft = '10px';
    cancelBtn.onclick = cancelEditLeave;

    addBtn.parentNode.insertBefore(cancelBtn, addBtn.nextSibling);

    // التمرير للنموذج
    document.getElementById('leaveManagementSection').scrollIntoView({ behavior: 'smooth' });

    showNotification('📝 تم تحميل بيانات الإجازة للتعديل', 'info', 5000);
}

/**
 * تحديث سجل إجازة
 */
function updateLeaveRecord(recordId) {
    const employeeSelect = document.getElementById('leaveEmployeeSelect');
    if (!employeeSelect.value) return;

    const employee = window.employees[employeeSelect.value];
    const recordIndex = employee.leaveHistory.findIndex(r => r.id === recordId);

    if (recordIndex === -1) {
        showNotification('❌ لم يتم العثور على سجل الإجازة', 'error', 5000);
        return;
    }

    const oldRecord = employee.leaveHistory[recordIndex];

    // استرداد الرصيد القديم
    switch(oldRecord.type) {
        case 'annual':
            employee.usedAnnual = (employee.usedAnnual || 0) - oldRecord.days;
            break;
        case 'sick':
            employee.usedSick = (employee.usedSick || 0) - oldRecord.days;
            break;
        case 'emergency':
            employee.usedEmergency = (employee.usedEmergency || 0) - oldRecord.days;
            break;
    }

    // حذف السجل القديم
    employee.leaveHistory.splice(recordIndex, 1);

    // إضافة السجل الجديد
    addAdvancedLeaveRecord();

    // إضافة سجل التعديل
    const editLog = {
        recordId: recordId,
        oldData: oldRecord,
        editDate: new Date().toISOString(),
        editBy: 'النظام'
    };

    if (!employee.leaveEditHistory) {
        employee.leaveEditHistory = [];
    }
    employee.leaveEditHistory.push(editLog);

    // إعادة تعيين النموذج
    cancelEditLeave();

    showNotification('✅ تم تحديث الإجازة بنجاح', 'success', 5000);
}

/**
 * إلغاء تعديل الإجازة
 */
function cancelEditLeave() {
    const addBtn = document.getElementById('addLeaveBtn');
    addBtn.textContent = '➕ إضافة الإجازة';
    addBtn.onclick = addAdvancedLeaveRecord;

    // إزالة زر الإلغاء
    const cancelBtn = addBtn.parentNode.querySelector('.btn-secondary');
    if (cancelBtn) {
        cancelBtn.remove();
    }

    // مسح النموذج
    clearLeaveForm();
}

/**
 * حذف سجل إجازة محسن
 */
function deleteLeaveRecord(recordId) {
    const employeeSelect = document.getElementById('leaveEmployeeSelect');
    if (!employeeSelect.value) return;

    const employee = window.employees[employeeSelect.value];
    const recordIndex = employee.leaveHistory.findIndex(record => record.id === recordId);

    if (recordIndex === -1) {
        showNotification('❌ لم يتم العثور على سجل الإجازة', 'error', 5000);
        return;
    }

    const record = employee.leaveHistory[recordIndex];

    const confirmMessage = `هل أنت متأكد من حذف الإجازة التالية؟\n\n` +
                          `النوع: ${record.typeName}\n` +
                          `المدة: ${record.days} يوم\n` +
                          `التاريخ: من ${new Date(record.startDate).toLocaleDateString('ar-SA')} إلى ${new Date(record.endDate).toLocaleDateString('ar-SA')}\n` +
                          `السبب: ${record.reason}`;

    if (confirm(confirmMessage)) {
        // استرداد الرصيد
        switch(record.type) {
            case 'annual':
                employee.usedAnnual = (employee.usedAnnual || 0) - record.days;
                break;
            case 'sick':
                employee.usedSick = (employee.usedSick || 0) - record.days;
                break;
            case 'emergency':
                employee.usedEmergency = (employee.usedEmergency || 0) - record.days;
                break;
        }

        // حذف السجل
        employee.leaveHistory.splice(recordIndex, 1);

        // إضافة سجل الحذف
        if (!employee.leaveDeleteHistory) {
            employee.leaveDeleteHistory = [];
        }
        employee.leaveDeleteHistory.push({
            deletedRecord: record,
            deleteDate: new Date().toISOString(),
            deleteBy: 'النظام'
        });

        // تحديث الواجهة
        updateTable();
        updateStats();
        updateQuickEmployeeView();
        updateCharts();
        updateSelectedEmployeeInfo(employee);
        updateLeaveHistory();
        updateAvailableBalance();
        saveData();

        showNotification(`✅ تم حذف إجازة ${record.typeName} بنجاح`, 'success', 5000);
    }
}

// ===== وظائف التصدير والتقارير =====

/**
 * تصدير إلى CSV
 */
function exportToCSV() {
    if (window.employees.length === 0) {
        showAlert('لا توجد بيانات للتصدير', 'warning');
        return;
    }

    const headers = [
        'اسم الموظف', 'الرقم الوظيفي', 'القسم', 'تاريخ التوظيف', 'سنوات الخدمة',
        'الرصيد السنوي', 'الرصيد المرحل', 'إجمالي المتاح', 'المستخدم السنوي', 'المتبقي السنوي',
        'الرصيد المرضي', 'المستخدم المرضي', 'المتبقي المرضي',
        'الرصيد الطارئ', 'المستخدم الطارئ', 'المتبقي الطارئ', 'الحالة'
    ];

    const csvContent = [
        headers.join(','),
        ...window.employees.map(emp => {
            const yearsOfService = calculateYearsOfService(emp.hireDate);
            const totalAvailable = emp.annualLeave + emp.carriedOverLeave;
            const remainingTotal = totalAvailable - emp.usedAnnual;
            const status = getEmployeeStatus(emp);
            return [
                emp.name, emp.id, emp.department, emp.hireDate, yearsOfService,
                emp.annualLeave, emp.carriedOverLeave, totalAvailable, emp.usedAnnual, remainingTotal,
                emp.sickLeave, emp.usedSick, emp.sickLeave - emp.usedSick,
                emp.emergencyLeave, emp.usedEmergency, emp.emergencyLeave - emp.usedEmergency,
                status
            ].join(',');
        })
    ].join('\n');

    downloadFile(csvContent, 'employee_leaves.csv', 'text/csv;charset=utf-8;');
    showAlert('تم تصدير البيانات بصيغة CSV بنجاح', 'success');
}

/**
 * تصدير إلى JSON
 */
function exportToJSON() {
    const data = saveData();
    downloadFile(data, `employee_leaves_backup_${new Date().toISOString().split('T')[0]}.json`, 'application/json');
    showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
}

/**
 * إنشاء تقرير مفصل
 */
function generateDetailedReport() {
    if (window.employees.length === 0) {
        showNotification('❌ لا توجد بيانات لإنشاء التقرير', 'warning', 5000);
        return;
    }

    const departmentStats = {};
    window.employees.forEach(emp => {
        if (!departmentStats[emp.department]) {
            departmentStats[emp.department] = {
                count: 0,
                totalCarriedOver: 0,
                totalUsed: 0,
                totalRemaining: 0
            };
        }
        departmentStats[emp.department].count++;
        departmentStats[emp.department].totalCarriedOver += emp.carriedOverLeave;
        departmentStats[emp.department].totalUsed += emp.usedAnnual + emp.usedSick + emp.usedEmergency;
        departmentStats[emp.department].totalRemaining += (emp.annualLeave + emp.carriedOverLeave - emp.usedAnnual) + (emp.sickLeave - emp.usedSick) + (emp.emergencyLeave - emp.usedEmergency);
    });

    showReportDisplay();
    document.getElementById('detailedReportContent').innerHTML = `
        <div class="detailed-report">
            <h3 style="text-align: center; color: #3498db; margin-bottom: 20px;">
                📊 التقرير الشامل حسب الأقسام
            </h3>

            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f8f9fa;">
                            <th style="padding: 12px; border: 1px solid #dee2e6;">القسم</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">عدد الموظفين</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">إجمالي الرصيد المرحل</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">إجمالي المستخدم</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">إجمالي المتبقي</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${Object.entries(departmentStats).map(([dept, stats]) => `
                            <tr>
                                <td style="padding: 12px; border: 1px solid #dee2e6;">${dept}</td>
                                <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${stats.count}</td>
                                <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${stats.totalCarriedOver}</td>
                                <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${stats.totalUsed}</td>
                                <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${stats.totalRemaining}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        </div>
    `;
}

// ===== وظائف مساعدة للتقارير المتقدمة =====

/**
 * عرض خيارات تقارير الأقسام
 */
function showDepartmentReportOptions() {
    const employees = window.employees || [];
    const departments = [...new Set(employees.map(emp => emp.department))];

    if (departments.length === 0) {
        showNotification('❌ لا توجد أقسام متاحة. يرجى إضافة موظفين أولاً.', 'warning', 5000);
        return;
    }

    const optionsHTML = departments.map(dept =>
        `<button class="btn btn-primary" onclick="generateDepartmentReport('${dept}'); closeDepartmentOptions();" style="margin: 5px;">
            🏢 تقرير قسم ${dept}
        </button>`
    ).join('');

    const dialogHTML = `
        <div class="modal-overlay" onclick="closeDepartmentOptions()">
            <div class="modal-content" onclick="event.stopPropagation()" style="max-width: 600px;">
                <div class="modal-header">
                    <h3>🏢 اختيار قسم للتقرير</h3>
                    <button class="close-btn" onclick="closeDepartmentOptions()">×</button>
                </div>
                <div class="modal-body">
                    <div style="display: flex; flex-wrap: wrap; gap: 10px; justify-content: center;">
                        <button class="btn btn-secondary" onclick="generateDepartmentReport('all'); closeDepartmentOptions();" style="margin: 5px;">
                            📊 تقرير جميع الأقسام
                        </button>
                        ${optionsHTML}
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', dialogHTML);
}

/**
 * إغلاق خيارات الأقسام
 */
function closeDepartmentOptions() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) modal.remove();
}

/**
 * عرض خيارات التقارير الفردية
 */
function showEmployeeReportOptions() {
    const employees = window.employees || [];

    if (employees.length === 0) {
        showNotification('❌ لا توجد موظفين متاحين. يرجى إضافة موظفين أولاً.', 'warning', 5000);
        return;
    }

    const employeeOptions = employees.map((emp, index) =>
        `<div class="employee-option" onclick="generateEmployeeReport(${index}); closeEmployeeOptions();" style="
            padding: 10px;
            margin: 5px;
            background: #f8f9fa;
            border-radius: 6px;
            cursor: pointer;
            border: 1px solid #e9ecef;
            transition: all 0.3s;
        " onmouseover="this.style.background='#e3f2fd'" onmouseout="this.style.background='#f8f9fa'">
            <strong>${emp.name}</strong> (${emp.id})<br>
            <small style="color: #666;">${emp.department}</small>
        </div>`
    ).join('');

    const dialogHTML = `
        <div class="modal-overlay" onclick="closeEmployeeOptions()">
            <div class="modal-content" onclick="event.stopPropagation()" style="max-width: 700px; max-height: 80vh;">
                <div class="modal-header">
                    <h3>👤 اختيار موظف للتقرير</h3>
                    <button class="close-btn" onclick="closeEmployeeOptions()">×</button>
                </div>
                <div class="modal-body" style="max-height: 50vh; overflow-y: auto;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px;">
                        ${employeeOptions}
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', dialogHTML);
}

/**
 * إغلاق خيارات الموظفين
 */
function closeEmployeeOptions() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) modal.remove();
}

/**
 * إنشاء تقرير قسم محدد
 */
function generateDepartmentReport(department) {
    const employees = window.employees || [];

    let filteredEmployees;
    let reportTitle;

    if (department === 'all') {
        filteredEmployees = employees;
        reportTitle = 'تقرير جميع الأقسام';
    } else {
        filteredEmployees = employees.filter(emp => emp.department === department);
        reportTitle = `تقرير قسم ${department}`;
    }

    if (filteredEmployees.length === 0) {
        showNotification('❌ لا توجد بيانات للقسم المحدد', 'warning', 5000);
        return;
    }

    // حساب إحصائيات القسم
    let deptStats = {
        totalEmployees: filteredEmployees.length,
        totalCarriedOver: 0,
        totalUsed: 0,
        totalRemaining: 0,
        averageUsage: 0
    };

    filteredEmployees.forEach(emp => {
        deptStats.totalCarriedOver += emp.carriedOverLeave || 0;
        const totalUsed = (emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0);
        deptStats.totalUsed += totalUsed;

        const totalAvailable = emp.annualLeave + (emp.carriedOverLeave || 0);
        const remaining = totalAvailable - (emp.usedAnnual || 0);
        deptStats.totalRemaining += remaining;
    });

    deptStats.averageUsage = (deptStats.totalUsed / deptStats.totalEmployees).toFixed(1);

    showReportDisplay();
    document.getElementById('detailedReportContent').innerHTML = `
        <div class="department-report">
            <h3 style="text-align: center; color: #3498db; margin-bottom: 20px;">
                🏢 ${reportTitle}
            </h3>

            <!-- إحصائيات القسم -->
            <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
                <div class="stat-card" style="background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;">
                    <h4 style="color: #27ae60; margin: 0;">عدد الموظفين</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #27ae60;">${deptStats.totalEmployees}</div>
                </div>
                <div class="stat-card" style="background: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center;">
                    <h4 style="color: #3498db; margin: 0;">الرصيد المرحل</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #3498db;">${deptStats.totalCarriedOver}</div>
                </div>
                <div class="stat-card" style="background: #fff3e0; padding: 15px; border-radius: 8px; text-align: center;">
                    <h4 style="color: #f39c12; margin: 0;">المستخدم</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #f39c12;">${deptStats.totalUsed}</div>
                </div>
                <div class="stat-card" style="background: #f3e5f5; padding: 15px; border-radius: 8px; text-align: center;">
                    <h4 style="color: #9c27b0; margin: 0;">متوسط الاستخدام</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #9c27b0;">${deptStats.averageUsage}</div>
                </div>
            </div>

            <!-- جدول الموظفين -->
            <h4 style="color: #2c3e50; margin-bottom: 15px;">👥 تفاصيل الموظفين</h4>
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f8f9fa;">
                            <th style="padding: 12px; border: 1px solid #dee2e6;">اسم الموظف</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">الرقم الوظيفي</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">تاريخ التوظيف</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">الرصيد المرحل</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">المستخدم</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">المتبقي</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredEmployees.map(emp => {
                            const totalUsed = (emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0);
                            const totalAvailable = emp.annualLeave + (emp.carriedOverLeave || 0);
                            const remaining = totalAvailable - (emp.usedAnnual || 0);
                            const status = getLeaveStatus(emp);

                            return `
                                <tr>
                                    <td style="padding: 12px; border: 1px solid #dee2e6;">${emp.name}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${emp.id}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${emp.hireDate}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${emp.carriedOverLeave || 0}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${totalUsed}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${remaining}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">
                                        <span style="color: ${status.color};">${status.status}</span>
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        </div>
    `;
}

/**
 * إنشاء تقرير موظف محدد
 */
function generateEmployeeReport(employeeIndex) {
    const employee = window.employees[employeeIndex];

    if (!employee) {
        showNotification('❌ لم يتم العثور على الموظف', 'warning', 5000);
        return;
    }

    const totalUsed = (employee.usedAnnual || 0) + (employee.usedSick || 0) + (employee.usedEmergency || 0);
    const totalAvailable = employee.annualLeave + (employee.carriedOverLeave || 0);
    const remaining = totalAvailable - (employee.usedAnnual || 0);
    const status = getLeaveStatus(employee);
    const yearsOfService = calculateYearsOfService(employee.hireDate);

    showReportDisplay();
    document.getElementById('detailedReportContent').innerHTML = `
        <div class="employee-report">
            <h3 style="text-align: center; color: #3498db; margin-bottom: 20px;">
                👤 تقرير الموظف: ${employee.name}
            </h3>

            <!-- معلومات الموظف الأساسية -->
            <div class="employee-info-section" style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">📋 المعلومات الأساسية</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                    <div><strong>الاسم:</strong> ${employee.name}</div>
                    <div><strong>الرقم الوظيفي:</strong> ${employee.id}</div>
                    <div><strong>القسم:</strong> ${employee.department}</div>
                    <div><strong>تاريخ التوظيف:</strong> ${employee.hireDate}</div>
                    <div><strong>سنوات الخدمة:</strong> ${yearsOfService} سنة</div>
                    <div><strong>حالة الرصيد:</strong> <span style="color: ${status.color};">${status.status}</span></div>
                </div>
            </div>

            <!-- أرصدة الإجازات -->
            <div class="leave-balances" style="margin-bottom: 20px;">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">⚖️ أرصدة الإجازات</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <div class="balance-card" style="background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;">
                        <h5 style="color: #27ae60; margin: 0 0 10px 0;">الإجازة السنوية</h5>
                        <div style="font-size: 18px; font-weight: bold; color: #27ae60;">
                            ${employee.annualLeave + (employee.carriedOverLeave || 0)} متاح
                        </div>
                        <div style="font-size: 14px; color: #666;">
                            (${employee.annualLeave} + ${employee.carriedOverLeave || 0} مرحل)
                        </div>
                        <div style="font-size: 16px; margin-top: 5px;">
                            مستخدم: ${employee.usedAnnual || 0} | متبقي: ${totalAvailable - (employee.usedAnnual || 0)}
                        </div>
                    </div>

                    <div class="balance-card" style="background: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center;">
                        <h5 style="color: #3498db; margin: 0 0 10px 0;">الإجازة المرضية</h5>
                        <div style="font-size: 18px; font-weight: bold; color: #3498db;">
                            ${employee.sickLeave} متاح
                        </div>
                        <div style="font-size: 16px; margin-top: 5px;">
                            مستخدم: ${employee.usedSick || 0} | متبقي: ${employee.sickLeave - (employee.usedSick || 0)}
                        </div>
                    </div>

                    <div class="balance-card" style="background: #fff3e0; padding: 15px; border-radius: 8px; text-align: center;">
                        <h5 style="color: #f39c12; margin: 0 0 10px 0;">الإجازة الطارئة</h5>
                        <div style="font-size: 18px; font-weight: bold; color: #f39c12;">
                            ${employee.emergencyLeave} متاح
                        </div>
                        <div style="font-size: 16px; margin-top: 5px;">
                            مستخدم: ${employee.usedEmergency || 0} | متبقي: ${employee.emergencyLeave - (employee.usedEmergency || 0)}
                        </div>
                    </div>
                </div>
            </div>

            <!-- سجل الإجازات -->
            <div class="leave-history">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">📅 سجل الإجازات</h4>
                ${employee.leaveHistory && employee.leaveHistory.length > 0 ? `
                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    <th style="padding: 12px; border: 1px solid #dee2e6;">نوع الإجازة</th>
                                    <th style="padding: 12px; border: 1px solid #dee2e6;">تاريخ البداية</th>
                                    <th style="padding: 12px; border: 1px solid #dee2e6;">تاريخ النهاية</th>
                                    <th style="padding: 12px; border: 1px solid #dee2e6;">عدد الأيام</th>
                                    <th style="padding: 12px; border: 1px solid #dee2e6;">السبب</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${employee.leaveHistory.map(leave => `
                                    <tr>
                                        <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${leave.type}</td>
                                        <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${leave.startDate}</td>
                                        <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${leave.endDate}</td>
                                        <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${leave.days}</td>
                                        <td style="padding: 12px; border: 1px solid #dee2e6;">${leave.reason || 'غير محدد'}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                ` : `
                    <div style="text-align: center; padding: 40px; color: #7f8c8d;">
                        <span style="font-size: 48px;">📝</span>
                        <h4>لا يوجد سجل إجازات</h4>
                        <p>لم يتم تسجيل أي إجازات لهذا الموظف بعد</p>
                    </div>
                `}
            </div>
        </div>
    `;
}

// ===== وظائف التقارير الجديدة =====

/**
 * إنشاء التقرير الشهري
 */
function generateMonthlyReport() {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    const monthName = new Intl.DateTimeFormat('ar-SA', { month: 'long' }).format(currentDate);

    if (window.employees.length === 0) {
        showNotification('❌ لا توجد بيانات موظفين لإنشاء التقرير الشهري', 'warning', 5000);
        return;
    }

    // حساب الإحصائيات الشهرية
    let monthlyStats = {
        totalEmployees: window.employees.length,
        newHires: 0,
        totalLeavesTaken: 0,
        mostActiveLeaveType: { type: '', count: 0 },
        departmentStats: {}
    };

    // تحليل البيانات
    window.employees.forEach(emp => {
        const hireDate = new Date(emp.hireDate);
        if (hireDate.getMonth() === currentMonth && hireDate.getFullYear() === currentYear) {
            monthlyStats.newHires++;
        }

        // إحصائيات الأقسام
        if (!monthlyStats.departmentStats[emp.department]) {
            monthlyStats.departmentStats[emp.department] = {
                employees: 0,
                totalUsed: 0
            };
        }
        monthlyStats.departmentStats[emp.department].employees++;

        const totalUsed = (emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0);
        monthlyStats.departmentStats[emp.department].totalUsed += totalUsed;
        monthlyStats.totalLeavesTaken += totalUsed;
    });

    showReportDisplay();
    document.getElementById('detailedReportContent').innerHTML = `
        <div class="monthly-report">
            <h3 style="text-align: center; color: #3498db; margin-bottom: 20px;">
                📅 التقرير الشهري - ${monthName} ${currentYear}
            </h3>

            <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
                <div class="stat-card" style="background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;">
                    <h4 style="color: #27ae60; margin: 0;">إجمالي الموظفين</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #27ae60;">${monthlyStats.totalEmployees}</div>
                </div>
                <div class="stat-card" style="background: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center;">
                    <h4 style="color: #3498db; margin: 0;">موظفين جدد</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #3498db;">${monthlyStats.newHires}</div>
                </div>
                <div class="stat-card" style="background: #fff3e0; padding: 15px; border-radius: 8px; text-align: center;">
                    <h4 style="color: #f39c12; margin: 0;">إجمالي الإجازات المستخدمة</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #f39c12;">${monthlyStats.totalLeavesTaken}</div>
                </div>
            </div>

            <h4 style="color: #2c3e50; margin-bottom: 15px;">📊 إحصائيات الأقسام</h4>
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f8f9fa;">
                            <th style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">القسم</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">عدد الموظفين</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">الإجازات المستخدمة</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">متوسط الاستخدام</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${Object.entries(monthlyStats.departmentStats).map(([dept, stats]) => `
                            <tr>
                                <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${dept}</td>
                                <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${stats.employees}</td>
                                <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${stats.totalUsed}</td>
                                <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${(stats.totalUsed / stats.employees).toFixed(1)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        </div>
    `;
}

/**
 * تقرير أرصدة الإجازات
 */
function generateLeaveBalanceReport() {
    if (window.employees.length === 0) {
        showNotification('❌ لا توجد بيانات موظفين لإنشاء تقرير الأرصدة', 'warning', 5000);
        return;
    }

    showReportDisplay();
    document.getElementById('detailedReportContent').innerHTML = `
        <div class="balance-report">
            <h3 style="text-align: center; color: #3498db; margin-bottom: 20px;">
                ⚖️ تقرير أرصدة الإجازات
            </h3>

            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f8f9fa;">
                            <th style="padding: 12px; border: 1px solid #dee2e6;">اسم الموظف</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">القسم</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">الرصيد السنوي</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">المرحل</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">المستخدم</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">المتبقي</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${window.employees.map(emp => {
                            const totalAvailable = emp.annualLeave + (emp.carriedOverLeave || 0);
                            const totalUsed = (emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0);
                            const remaining = totalAvailable - (emp.usedAnnual || 0);
                            const status = getLeaveStatus(emp);

                            return `
                                <tr>
                                    <td style="padding: 12px; border: 1px solid #dee2e6;">${emp.name}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${emp.department}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${emp.annualLeave}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${emp.carriedOverLeave || 0}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${totalUsed}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${remaining}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">
                                        <span style="color: ${status.color};">${status.status}</span>
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        </div>
    `;
}

/**
 * تقرير الأرصدة المنخفضة
 */
function generateLowBalanceReport() {
    if (window.employees.length === 0) {
        showNotification('❌ لا توجد بيانات موظفين لإنشاء التقرير', 'warning', 5000);
        return;
    }

    const lowBalanceEmployees = window.employees.filter(emp => {
        const totalAvailable = emp.annualLeave + (emp.carriedOverLeave || 0);
        const remaining = totalAvailable - (emp.usedAnnual || 0);
        return remaining <= 5; // أقل من أو يساوي 5 أيام
    });

    showReportDisplay();
    document.getElementById('detailedReportContent').innerHTML = `
        <div class="low-balance-report">
            <h3 style="text-align: center; color: #e74c3c; margin-bottom: 20px;">
                ⚠️ تقرير الأرصدة المنخفضة
            </h3>

            ${lowBalanceEmployees.length === 0 ? `
                <div style="text-align: center; padding: 40px; color: #27ae60;">
                    <span style="font-size: 48px;">✅</span>
                    <h4>ممتاز! لا توجد أرصدة منخفضة</h4>
                    <p>جميع الموظفين لديهم أرصدة إجازات كافية</p>
                </div>
            ` : `
                <div class="alert alert-warning" style="margin-bottom: 20px;">
                    <strong>تنبيه:</strong> يوجد ${lowBalanceEmployees.length} موظف لديهم أرصدة إجازات منخفضة (5 أيام أو أقل)
                </div>

                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 12px; border: 1px solid #dee2e6;">اسم الموظف</th>
                                <th style="padding: 12px; border: 1px solid #dee2e6;">القسم</th>
                                <th style="padding: 12px; border: 1px solid #dee2e6;">الرصيد المتبقي</th>
                                <th style="padding: 12px; border: 1px solid #dee2e6;">المستخدم</th>
                                <th style="padding: 12px; border: 1px solid #dee2e6;">مستوى التحذير</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${lowBalanceEmployees.map(emp => {
                                const totalAvailable = emp.annualLeave + (emp.carriedOverLeave || 0);
                                const remaining = totalAvailable - (emp.usedAnnual || 0);
                                const totalUsed = (emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0);

                                let warningLevel = '';
                                let warningColor = '';
                                if (remaining <= 0) {
                                    warningLevel = 'نفد الرصيد';
                                    warningColor = '#e74c3c';
                                } else if (remaining <= 2) {
                                    warningLevel = 'حرج جداً';
                                    warningColor = '#e74c3c';
                                } else if (remaining <= 5) {
                                    warningLevel = 'منخفض';
                                    warningColor = '#f39c12';
                                }

                                return `
                                    <tr>
                                        <td style="padding: 12px; border: 1px solid #dee2e6;">${emp.name}</td>
                                        <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${emp.department}</td>
                                        <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center; color: ${warningColor}; font-weight: bold;">${remaining}</td>
                                        <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${totalUsed}</td>
                                        <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">
                                            <span style="color: ${warningColor}; font-weight: bold;">${warningLevel}</span>
                                        </td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            `}
        </div>
    `;
}

/**
 * إظهار منطقة عرض التقارير
 */
function showReportDisplay() {
    const reportCard = document.getElementById('reportDisplayCard');
    if (reportCard) {
        reportCard.style.display = 'block';
        reportCard.scrollIntoView({ behavior: 'smooth' });
    }
}

/**
 * إخفاء منطقة عرض التقارير
 */
function hideReportDisplay() {
    const reportCard = document.getElementById('reportDisplayCard');
    if (reportCard) {
        reportCard.style.display = 'none';
    }
}


