/**
 * نظام إدارة إجازات الموظفين المتطور
 * ملف الميزات المتقدمة
 * يحتوي على وظائف الإجازات والتقارير والرسوم البيانية والبيانات التجريبية
 */

// ===== وظائف إدارة الإجازات =====

/**
 * فتح إدارة الإجازات لموظف معين
 */
function openLeaveManagement(index) {
    currentEmployeeIndex = index;
    showSection('leaves');
    setTimeout(() => {
        updateLeaveEmployeeSelect();
        document.getElementById('leaveEmployeeSelect').value = index;
        selectEmployeeForLeave();
    }, 300);
}

/**
 * تحديث قائمة اختيار الموظف للإجازات
 */
function updateLeaveEmployeeSelect() {
    const select = document.getElementById('leaveEmployeeSelect');
    if (!select) return;

    select.innerHTML = '<option value="">-- اختر موظف --</option>';
    window.employees.forEach((emp, index) => {
        const option = document.createElement('option');
        option.value = index;
        option.textContent = `${emp.name} (${emp.id}) - ${emp.department}`;
        select.appendChild(option);
    });
}

/**
 * اختيار موظف لإدارة الإجازات
 */
function selectEmployeeForLeave() {
    const select = document.getElementById('leaveEmployeeSelect');
    const selectedIndex = parseInt(select.value);

    if (isNaN(selectedIndex) || selectedIndex < 0 || selectedIndex >= window.employees.length) {
        document.getElementById('selectedEmployeeInfo').style.display = 'none';
        document.getElementById('leaveManagementSection').style.display = 'none';
        return;
    }

    currentEmployeeIndex = selectedIndex;
    const employee = window.employees[selectedIndex];

    updateSelectedEmployeeInfo(employee);
    updateLeaveHistory();

    document.getElementById('selectedEmployeeInfo').style.display = 'block';
    document.getElementById('leaveManagementSection').style.display = 'block';
}

/**
 * تحديث معلومات الموظف المختار
 */
function updateSelectedEmployeeInfo(employee) {
    const infoDiv = document.getElementById('selectedEmployeeInfo');
    const totalAvailable = employee.annualLeave + employee.carriedOverLeave;
    const remainingTotal = totalAvailable - employee.usedAnnual;

    infoDiv.innerHTML = `
        <h4 style="margin: 0 0 15px 0; color: #2c3e50;">📊 معلومات الرصيد - ${employee.name}</h4>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <div>
                <strong>الرصيد السنوي:</strong> ${employee.annualLeave} يوم<br>
                <strong>الرصيد المرحل:</strong> <span style="color: #27ae60; font-weight: 600;">${employee.carriedOverLeave} يوم</span><br>
                <strong>إجمالي المتاح:</strong> <strong>${totalAvailable} يوم</strong>
            </div>
            <div>
                <strong>المستخدم:</strong> ${employee.usedAnnual} يوم<br>
                <strong>المتبقي:</strong> <strong style="color: ${remainingTotal <= 5 ? '#e74c3c' : '#27ae60'}">${remainingTotal} يوم</strong><br>
                <strong>الحالة:</strong> <span style="color: ${getEmployeeStatus(employee) === 'طبيعي' ? '#27ae60' : '#e74c3c'}">${getEmployeeStatus(employee)}</span>
            </div>
        </div>
    `;
}

/**
 * حساب أيام الإجازة تلقائياً
 */
function calculateLeaveDays() {
    const startDate = document.getElementById('leaveStartDate').value;
    const endDate = document.getElementById('leaveEndDate').value;

    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        const diffTime = Math.abs(end - start);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;

        document.getElementById('leaveDays').value = diffDays;
    }
}

/**
 * إضافة سجل إجازة
 */
function addLeaveRecord() {
    if (currentEmployeeIndex === -1) return;

    const employee = window.employees[currentEmployeeIndex];
    const leaveType = sanitizeInput(document.getElementById('leaveType').value);
    const leaveDays = parseFloat(document.getElementById('leaveDays').value);
    const startDate = document.getElementById('leaveStartDate').value;
    const endDate = document.getElementById('leaveEndDate').value;
    const reason = sanitizeInput(document.getElementById('leaveReason').value.trim());

    if (!leaveType || !leaveDays || !startDate || !endDate || !reason) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }

    if (leaveDays <= 0) {
        showAlert('عدد أيام الإجازة يجب أن يكون أكبر من صفر', 'warning');
        return;
    }

    let currentUsed, maxAllowed, leaveTypeName;
    switch(leaveType) {
        case 'annual':
            currentUsed = employee.usedAnnual;
            maxAllowed = employee.annualLeave + employee.carriedOverLeave;
            leaveTypeName = 'إجازة سنوية';
            break;
        case 'sick':
            currentUsed = employee.usedSick;
            maxAllowed = employee.sickLeave;
            leaveTypeName = 'إجازة مرضية';
            break;
        case 'emergency':
            currentUsed = employee.usedEmergency;
            maxAllowed = employee.emergencyLeave;
            leaveTypeName = 'إجازة طارئة';
            break;
    }

    if (currentUsed + leaveDays > maxAllowed) {
        showAlert(`الرصيد المتاح لـ${leaveTypeName} غير كافي. المتاح: ${maxAllowed - currentUsed} يوم`, 'warning');
        return;
    }

    const leaveRecord = {
        id: Date.now(),
        type: leaveType,
        typeName: leaveTypeName,
        days: leaveDays,
        startDate: startDate,
        endDate: endDate,
        reason: reason,
        addedDate: new Date().toISOString()
    };

    if (!employee.leaveHistory) {
        employee.leaveHistory = [];
    }
    employee.leaveHistory.push(leaveRecord);

    switch(leaveType) {
        case 'annual':
            employee.usedAnnual += leaveDays;
            break;
        case 'sick':
            employee.usedSick += leaveDays;
            break;
        case 'emergency':
            employee.usedEmergency += leaveDays;
            break;
    }

    updateTable();
    updateStats();
    updateSelectedEmployeeInfo(employee);
    updateLeaveHistory();
    saveData();

    showAlert(`تم إضافة ${leaveTypeName} لمدة ${leaveDays} يوم للموظف ${employee.name}`, 'success');

    // مسح النموذج
    document.getElementById('leaveDays').value = '';
    document.getElementById('leaveStartDate').value = '';
    document.getElementById('leaveEndDate').value = '';
    document.getElementById('leaveReason').value = '';
}

/**
 * تحديث سجل الإجازات
 */
function updateLeaveHistory() {
    if (currentEmployeeIndex === -1) return;

    const employee = window.employees[currentEmployeeIndex];
    const historyContent = document.getElementById('leaveHistoryContent');

    if (!employee.leaveHistory || employee.leaveHistory.length === 0) {
        historyContent.innerHTML = '<p style="text-align: center; color: #7f8c8d; padding: 40px;">لا توجد إجازات مسجلة لهذا الموظف</p>';
        return;
    }

    const sortedHistory = [...employee.leaveHistory].sort((a, b) => new Date(b.addedDate) - new Date(a.addedDate));

    historyContent.innerHTML = sortedHistory.map(record => `
        <div style="padding: 15px; margin: 10px 0; border: 1px solid #e9ecef; border-radius: 8px; background: white;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <strong style="color: #2c3e50;">${record.typeName}</strong>
                <button class="btn btn-small btn-danger" onclick="deleteLeaveRecord(${record.id})" title="حذف الإجازة">🗑️</button>
            </div>
            <div style="font-size: 14px; color: #7f8c8d;">
                📅 من ${new Date(record.startDate).toLocaleDateString('ar-SA')} إلى ${new Date(record.endDate).toLocaleDateString('ar-SA')}<br>
                ⏱️ المدة: ${record.days} يوم<br>
                📝 السبب: ${record.reason}<br>
                <small style="color: #95a5a6;">تم الإضافة: ${new Date(record.addedDate).toLocaleDateString('ar-SA')}</small>
            </div>
        </div>
    `).join('');
}

/**
 * حذف سجل إجازة
 */
function deleteLeaveRecord(recordId) {
    if (currentEmployeeIndex === -1) return;

    const employee = window.employees[currentEmployeeIndex];
    const recordIndex = employee.leaveHistory.findIndex(record => record.id === recordId);

    if (recordIndex === -1) return;

    const record = employee.leaveHistory[recordIndex];

    if (confirm(`هل أنت متأكد من حذف إجازة ${record.typeName} لمدة ${record.days} يوم؟`)) {
        switch(record.type) {
            case 'annual':
                employee.usedAnnual -= record.days;
                break;
            case 'sick':
                employee.usedSick -= record.days;
                break;
            case 'emergency':
                employee.usedEmergency -= record.days;
                break;
        }

        employee.leaveHistory.splice(recordIndex, 1);

        updateTable();
        updateStats();
        updateSelectedEmployeeInfo(employee);
        updateLeaveHistory();
        saveData();

        showAlert(`تم حذف إجازة ${record.typeName} بنجاح`, 'success');
    }
}

// ===== وظائف التصدير والتقارير =====

/**
 * تصدير إلى CSV
 */
function exportToCSV() {
    if (window.employees.length === 0) {
        showAlert('لا توجد بيانات للتصدير', 'warning');
        return;
    }

    const headers = [
        'اسم الموظف', 'الرقم الوظيفي', 'القسم', 'تاريخ التوظيف', 'سنوات الخدمة',
        'الرصيد السنوي', 'الرصيد المرحل', 'إجمالي المتاح', 'المستخدم السنوي', 'المتبقي السنوي',
        'الرصيد المرضي', 'المستخدم المرضي', 'المتبقي المرضي',
        'الرصيد الطارئ', 'المستخدم الطارئ', 'المتبقي الطارئ', 'الحالة'
    ];

    const csvContent = [
        headers.join(','),
        ...window.employees.map(emp => {
            const yearsOfService = calculateYearsOfService(emp.hireDate);
            const totalAvailable = emp.annualLeave + emp.carriedOverLeave;
            const remainingTotal = totalAvailable - emp.usedAnnual;
            const status = getEmployeeStatus(emp);
            return [
                emp.name, emp.id, emp.department, emp.hireDate, yearsOfService,
                emp.annualLeave, emp.carriedOverLeave, totalAvailable, emp.usedAnnual, remainingTotal,
                emp.sickLeave, emp.usedSick, emp.sickLeave - emp.usedSick,
                emp.emergencyLeave, emp.usedEmergency, emp.emergencyLeave - emp.usedEmergency,
                status
            ].join(',');
        })
    ].join('\n');

    downloadFile(csvContent, 'employee_leaves.csv', 'text/csv;charset=utf-8;');
    showAlert('تم تصدير البيانات بصيغة CSV بنجاح', 'success');
}

/**
 * تصدير إلى JSON
 */
function exportToJSON() {
    const data = saveData();
    downloadFile(data, `employee_leaves_backup_${new Date().toISOString().split('T')[0]}.json`, 'application/json');
    showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
}

/**
 * إنشاء تقرير مفصل
 */
function generateDetailedReport() {
    const reportContent = document.getElementById('detailedReportContent');

    if (window.employees.length === 0) {
        reportContent.innerHTML = '<p style="text-align: center; color: #7f8c8d; padding: 40px;">لا توجد بيانات لإنشاء التقرير</p>';
        return;
    }

    const departmentStats = {};
    window.employees.forEach(emp => {
        if (!departmentStats[emp.department]) {
            departmentStats[emp.department] = {
                count: 0,
                totalCarriedOver: 0,
                totalUsed: 0,
                totalRemaining: 0
            };
        }
        departmentStats[emp.department].count++;
        departmentStats[emp.department].totalCarriedOver += emp.carriedOverLeave;
        departmentStats[emp.department].totalUsed += emp.usedAnnual + emp.usedSick + emp.usedEmergency;
        departmentStats[emp.department].totalRemaining += (emp.annualLeave + emp.carriedOverLeave - emp.usedAnnual) + (emp.sickLeave - emp.usedSick) + (emp.emergencyLeave - emp.usedEmergency);
    });

    reportContent.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">📊 تقرير مفصل حسب الأقسام</h3>
            </div>
            <div class="card-body">
                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 12px; border: 1px solid #dee2e6;">القسم</th>
                                <th style="padding: 12px; border: 1px solid #dee2e6;">عدد الموظفين</th>
                                <th style="padding: 12px; border: 1px solid #dee2e6;">إجمالي الرصيد المرحل</th>
                                <th style="padding: 12px; border: 1px solid #dee2e6;">إجمالي المستخدم</th>
                                <th style="padding: 12px; border: 1px solid #dee2e6;">إجمالي المتبقي</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${Object.entries(departmentStats).map(([dept, stats]) => `
                                <tr>
                                    <td style="padding: 12px; border: 1px solid #dee2e6;">${dept}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${stats.count}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${stats.totalCarriedOver}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${stats.totalUsed}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${stats.totalRemaining}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;
}

// ===== وظائف مساعدة للتقارير المتقدمة =====

/**
 * عرض خيارات تقارير الأقسام
 */
function showDepartmentReportOptions() {
    const employees = window.employees || [];
    const departments = [...new Set(employees.map(emp => emp.department))];

    if (departments.length === 0) {
        showNotification('❌ لا توجد أقسام متاحة. يرجى إضافة موظفين أولاً.', 'warning', 5000);
        return;
    }

    const optionsHTML = departments.map(dept =>
        `<button class="btn btn-primary" onclick="generateDepartmentReport('${dept}'); closeDepartmentOptions();" style="margin: 5px;">
            🏢 تقرير قسم ${dept}
        </button>`
    ).join('');

    const dialogHTML = `
        <div class="modal-overlay" onclick="closeDepartmentOptions()">
            <div class="modal-content" onclick="event.stopPropagation()" style="max-width: 600px;">
                <div class="modal-header">
                    <h3>🏢 اختيار قسم للتقرير</h3>
                    <button class="close-btn" onclick="closeDepartmentOptions()">×</button>
                </div>
                <div class="modal-body">
                    <div style="display: flex; flex-wrap: wrap; gap: 10px; justify-content: center;">
                        <button class="btn btn-secondary" onclick="generateDepartmentReport('all'); closeDepartmentOptions();" style="margin: 5px;">
                            📊 تقرير جميع الأقسام
                        </button>
                        ${optionsHTML}
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', dialogHTML);
}

/**
 * إغلاق خيارات الأقسام
 */
function closeDepartmentOptions() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) modal.remove();
}

/**
 * عرض خيارات التقارير الفردية
 */
function showEmployeeReportOptions() {
    const employees = window.employees || [];

    if (employees.length === 0) {
        showNotification('❌ لا توجد موظفين متاحين. يرجى إضافة موظفين أولاً.', 'warning', 5000);
        return;
    }

    const employeeOptions = employees.map((emp, index) =>
        `<div class="employee-option" onclick="generateEmployeeReport(${index}); closeEmployeeOptions();" style="
            padding: 10px;
            margin: 5px;
            background: #f8f9fa;
            border-radius: 6px;
            cursor: pointer;
            border: 1px solid #e9ecef;
            transition: all 0.3s;
        " onmouseover="this.style.background='#e3f2fd'" onmouseout="this.style.background='#f8f9fa'">
            <strong>${emp.name}</strong> (${emp.id})<br>
            <small style="color: #666;">${emp.department}</small>
        </div>`
    ).join('');

    const dialogHTML = `
        <div class="modal-overlay" onclick="closeEmployeeOptions()">
            <div class="modal-content" onclick="event.stopPropagation()" style="max-width: 700px; max-height: 80vh;">
                <div class="modal-header">
                    <h3>👤 اختيار موظف للتقرير</h3>
                    <button class="close-btn" onclick="closeEmployeeOptions()">×</button>
                </div>
                <div class="modal-body" style="max-height: 50vh; overflow-y: auto;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px;">
                        ${employeeOptions}
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', dialogHTML);
}

/**
 * إغلاق خيارات الموظفين
 */
function closeEmployeeOptions() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) modal.remove();
}

console.log('🔧 تم تحميل ملف الميزات بنجاح');
