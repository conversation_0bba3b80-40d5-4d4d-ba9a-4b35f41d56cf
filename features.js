/**
 * نظام إدارة إجازات الموظفين المتطور
 * ملف الميزات المتقدمة
 * يحتوي على وظائف الإجازات والتقارير والرسوم البيانية والبيانات التجريبية
 */

// ===== وظائف إدارة الإجازات =====

/**
 * فتح إدارة الإجازات لموظف معين
 */
function openLeaveManagement(index) {
    currentEmployeeIndex = index;
    showSection('leaves');
    setTimeout(() => {
        updateLeaveEmployeeSelect();
        document.getElementById('leaveEmployeeSelect').value = index;
        selectEmployeeForLeave();
    }, 300);
}

/**
 * تحديث قائمة اختيار الموظف للإجازات
 */
function updateLeaveEmployeeSelect() {
    const select = document.getElementById('leaveEmployeeSelect');
    if (!select) return;

    select.innerHTML = '<option value="">-- اختر موظف --</option>';
    window.employees.forEach((emp, index) => {
        const option = document.createElement('option');
        option.value = index;
        option.textContent = `${emp.name} (${emp.id}) - ${emp.department}`;
        select.appendChild(option);
    });
}

/**
 * اختيار موظف لإدارة الإجازات
 */
function selectEmployeeForLeave() {
    const select = document.getElementById('leaveEmployeeSelect');
    const selectedIndex = parseInt(select.value);

    if (isNaN(selectedIndex) || selectedIndex < 0 || selectedIndex >= window.employees.length) {
        document.getElementById('selectedEmployeeInfo').style.display = 'none';
        document.getElementById('leaveManagementSection').style.display = 'none';
        return;
    }

    currentEmployeeIndex = selectedIndex;
    const employee = window.employees[selectedIndex];

    updateSelectedEmployeeInfo(employee);
    updateLeaveHistory();

    document.getElementById('selectedEmployeeInfo').style.display = 'block';
    document.getElementById('leaveManagementSection').style.display = 'block';
}

/**
 * تحديث معلومات الموظف المختار
 */
function updateSelectedEmployeeInfo(employee) {
    const infoDiv = document.getElementById('selectedEmployeeInfo');
    const totalAvailable = employee.annualLeave + employee.carriedOverLeave;
    const remainingTotal = totalAvailable - employee.usedAnnual;

    infoDiv.innerHTML = `
        <h4 style="margin: 0 0 15px 0; color: #2c3e50;">📊 معلومات الرصيد - ${employee.name}</h4>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <div>
                <strong>الرصيد السنوي:</strong> ${employee.annualLeave} يوم<br>
                <strong>الرصيد المرحل:</strong> <span style="color: #27ae60; font-weight: 600;">${employee.carriedOverLeave} يوم</span><br>
                <strong>إجمالي المتاح:</strong> <strong>${totalAvailable} يوم</strong>
            </div>
            <div>
                <strong>المستخدم:</strong> ${employee.usedAnnual} يوم<br>
                <strong>المتبقي:</strong> <strong style="color: ${remainingTotal <= 5 ? '#e74c3c' : '#27ae60'}">${remainingTotal} يوم</strong><br>
                <strong>الحالة:</strong> <span style="color: ${getEmployeeStatus(employee) === 'طبيعي' ? '#27ae60' : '#e74c3c'}">${getEmployeeStatus(employee)}</span>
            </div>
        </div>
    `;
}

/**
 * حساب أيام الإجازة تلقائياً
 */
function calculateLeaveDays() {
    const startDate = document.getElementById('leaveStartDate').value;
    const endDate = document.getElementById('leaveEndDate').value;

    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        const diffTime = Math.abs(end - start);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;

        document.getElementById('leaveDays').value = diffDays;
    }
}

/**
 * إضافة سجل إجازة
 */
function addLeaveRecord() {
    if (currentEmployeeIndex === -1) return;

    const employee = window.employees[currentEmployeeIndex];
    const leaveType = sanitizeInput(document.getElementById('leaveType').value);
    const leaveDays = parseFloat(document.getElementById('leaveDays').value);
    const startDate = document.getElementById('leaveStartDate').value;
    const endDate = document.getElementById('leaveEndDate').value;
    const reason = sanitizeInput(document.getElementById('leaveReason').value.trim());

    if (!leaveType || !leaveDays || !startDate || !endDate || !reason) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }

    if (leaveDays <= 0) {
        showAlert('عدد أيام الإجازة يجب أن يكون أكبر من صفر', 'warning');
        return;
    }

    let currentUsed, maxAllowed, leaveTypeName;
    switch(leaveType) {
        case 'annual':
            currentUsed = employee.usedAnnual;
            maxAllowed = employee.annualLeave + employee.carriedOverLeave;
            leaveTypeName = 'إجازة سنوية';
            break;
        case 'sick':
            currentUsed = employee.usedSick;
            maxAllowed = employee.sickLeave;
            leaveTypeName = 'إجازة مرضية';
            break;
        case 'emergency':
            currentUsed = employee.usedEmergency;
            maxAllowed = employee.emergencyLeave;
            leaveTypeName = 'إجازة طارئة';
            break;
    }

    if (currentUsed + leaveDays > maxAllowed) {
        showAlert(`الرصيد المتاح لـ${leaveTypeName} غير كافي. المتاح: ${maxAllowed - currentUsed} يوم`, 'warning');
        return;
    }

    const leaveRecord = {
        id: Date.now(),
        type: leaveType,
        typeName: leaveTypeName,
        days: leaveDays,
        startDate: startDate,
        endDate: endDate,
        reason: reason,
        addedDate: new Date().toISOString()
    };

    if (!employee.leaveHistory) {
        employee.leaveHistory = [];
    }
    employee.leaveHistory.push(leaveRecord);

    switch(leaveType) {
        case 'annual':
            employee.usedAnnual += leaveDays;
            break;
        case 'sick':
            employee.usedSick += leaveDays;
            break;
        case 'emergency':
            employee.usedEmergency += leaveDays;
            break;
    }

    updateTable();
    updateStats();
    updateSelectedEmployeeInfo(employee);
    updateLeaveHistory();
    saveData();

    showAlert(`تم إضافة ${leaveTypeName} لمدة ${leaveDays} يوم للموظف ${employee.name}`, 'success');

    // مسح النموذج
    document.getElementById('leaveDays').value = '';
    document.getElementById('leaveStartDate').value = '';
    document.getElementById('leaveEndDate').value = '';
    document.getElementById('leaveReason').value = '';
}

/**
 * تحديث سجل الإجازات
 */
function updateLeaveHistory() {
    if (currentEmployeeIndex === -1) return;

    const employee = window.employees[currentEmployeeIndex];
    const historyContent = document.getElementById('leaveHistoryContent');

    if (!employee.leaveHistory || employee.leaveHistory.length === 0) {
        historyContent.innerHTML = '<p style="text-align: center; color: #7f8c8d; padding: 40px;">لا توجد إجازات مسجلة لهذا الموظف</p>';
        return;
    }

    const sortedHistory = [...employee.leaveHistory].sort((a, b) => new Date(b.addedDate) - new Date(a.addedDate));

    historyContent.innerHTML = sortedHistory.map(record => `
        <div style="padding: 15px; margin: 10px 0; border: 1px solid #e9ecef; border-radius: 8px; background: white;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <strong style="color: #2c3e50;">${record.typeName}</strong>
                <button class="btn btn-small btn-danger" onclick="deleteLeaveRecord(${record.id})" title="حذف الإجازة">🗑️</button>
            </div>
            <div style="font-size: 14px; color: #7f8c8d;">
                📅 من ${new Date(record.startDate).toLocaleDateString('ar-SA')} إلى ${new Date(record.endDate).toLocaleDateString('ar-SA')}<br>
                ⏱️ المدة: ${record.days} يوم<br>
                📝 السبب: ${record.reason}<br>
                <small style="color: #95a5a6;">تم الإضافة: ${new Date(record.addedDate).toLocaleDateString('ar-SA')}</small>
            </div>
        </div>
    `).join('');
}

/**
 * حذف سجل إجازة
 */
function deleteLeaveRecord(recordId) {
    if (currentEmployeeIndex === -1) return;

    const employee = window.employees[currentEmployeeIndex];
    const recordIndex = employee.leaveHistory.findIndex(record => record.id === recordId);

    if (recordIndex === -1) return;

    const record = employee.leaveHistory[recordIndex];

    if (confirm(`هل أنت متأكد من حذف إجازة ${record.typeName} لمدة ${record.days} يوم؟`)) {
        switch(record.type) {
            case 'annual':
                employee.usedAnnual -= record.days;
                break;
            case 'sick':
                employee.usedSick -= record.days;
                break;
            case 'emergency':
                employee.usedEmergency -= record.days;
                break;
        }

        employee.leaveHistory.splice(recordIndex, 1);

        updateTable();
        updateStats();
        updateSelectedEmployeeInfo(employee);
        updateLeaveHistory();
        saveData();

        showAlert(`تم حذف إجازة ${record.typeName} بنجاح`, 'success');
    }
}

// ===== وظائف التصدير والتقارير =====

/**
 * تصدير إلى CSV
 */
function exportToCSV() {
    if (window.employees.length === 0) {
        showAlert('لا توجد بيانات للتصدير', 'warning');
        return;
    }

    const headers = [
        'اسم الموظف', 'الرقم الوظيفي', 'القسم', 'تاريخ التوظيف', 'سنوات الخدمة',
        'الرصيد السنوي', 'الرصيد المرحل', 'إجمالي المتاح', 'المستخدم السنوي', 'المتبقي السنوي',
        'الرصيد المرضي', 'المستخدم المرضي', 'المتبقي المرضي',
        'الرصيد الطارئ', 'المستخدم الطارئ', 'المتبقي الطارئ', 'الحالة'
    ];

    const csvContent = [
        headers.join(','),
        ...window.employees.map(emp => {
            const yearsOfService = calculateYearsOfService(emp.hireDate);
            const totalAvailable = emp.annualLeave + emp.carriedOverLeave;
            const remainingTotal = totalAvailable - emp.usedAnnual;
            const status = getEmployeeStatus(emp);
            return [
                emp.name, emp.id, emp.department, emp.hireDate, yearsOfService,
                emp.annualLeave, emp.carriedOverLeave, totalAvailable, emp.usedAnnual, remainingTotal,
                emp.sickLeave, emp.usedSick, emp.sickLeave - emp.usedSick,
                emp.emergencyLeave, emp.usedEmergency, emp.emergencyLeave - emp.usedEmergency,
                status
            ].join(',');
        })
    ].join('\n');

    downloadFile(csvContent, 'employee_leaves.csv', 'text/csv;charset=utf-8;');
    showAlert('تم تصدير البيانات بصيغة CSV بنجاح', 'success');
}

/**
 * تصدير إلى JSON
 */
function exportToJSON() {
    const data = saveData();
    downloadFile(data, `employee_leaves_backup_${new Date().toISOString().split('T')[0]}.json`, 'application/json');
    showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
}

/**
 * إنشاء تقرير مفصل
 */
function generateDetailedReport() {
    if (window.employees.length === 0) {
        showNotification('❌ لا توجد بيانات لإنشاء التقرير', 'warning', 5000);
        return;
    }

    const departmentStats = {};
    window.employees.forEach(emp => {
        if (!departmentStats[emp.department]) {
            departmentStats[emp.department] = {
                count: 0,
                totalCarriedOver: 0,
                totalUsed: 0,
                totalRemaining: 0
            };
        }
        departmentStats[emp.department].count++;
        departmentStats[emp.department].totalCarriedOver += emp.carriedOverLeave;
        departmentStats[emp.department].totalUsed += emp.usedAnnual + emp.usedSick + emp.usedEmergency;
        departmentStats[emp.department].totalRemaining += (emp.annualLeave + emp.carriedOverLeave - emp.usedAnnual) + (emp.sickLeave - emp.usedSick) + (emp.emergencyLeave - emp.usedEmergency);
    });

    showReportDisplay();
    document.getElementById('detailedReportContent').innerHTML = `
        <div class="detailed-report">
            <h3 style="text-align: center; color: #3498db; margin-bottom: 20px;">
                📊 التقرير الشامل حسب الأقسام
            </h3>

            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f8f9fa;">
                            <th style="padding: 12px; border: 1px solid #dee2e6;">القسم</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">عدد الموظفين</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">إجمالي الرصيد المرحل</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">إجمالي المستخدم</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">إجمالي المتبقي</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${Object.entries(departmentStats).map(([dept, stats]) => `
                            <tr>
                                <td style="padding: 12px; border: 1px solid #dee2e6;">${dept}</td>
                                <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${stats.count}</td>
                                <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${stats.totalCarriedOver}</td>
                                <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${stats.totalUsed}</td>
                                <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${stats.totalRemaining}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        </div>
    `;
}

// ===== وظائف مساعدة للتقارير المتقدمة =====

/**
 * عرض خيارات تقارير الأقسام
 */
function showDepartmentReportOptions() {
    const employees = window.employees || [];
    const departments = [...new Set(employees.map(emp => emp.department))];

    if (departments.length === 0) {
        showNotification('❌ لا توجد أقسام متاحة. يرجى إضافة موظفين أولاً.', 'warning', 5000);
        return;
    }

    const optionsHTML = departments.map(dept =>
        `<button class="btn btn-primary" onclick="generateDepartmentReport('${dept}'); closeDepartmentOptions();" style="margin: 5px;">
            🏢 تقرير قسم ${dept}
        </button>`
    ).join('');

    const dialogHTML = `
        <div class="modal-overlay" onclick="closeDepartmentOptions()">
            <div class="modal-content" onclick="event.stopPropagation()" style="max-width: 600px;">
                <div class="modal-header">
                    <h3>🏢 اختيار قسم للتقرير</h3>
                    <button class="close-btn" onclick="closeDepartmentOptions()">×</button>
                </div>
                <div class="modal-body">
                    <div style="display: flex; flex-wrap: wrap; gap: 10px; justify-content: center;">
                        <button class="btn btn-secondary" onclick="generateDepartmentReport('all'); closeDepartmentOptions();" style="margin: 5px;">
                            📊 تقرير جميع الأقسام
                        </button>
                        ${optionsHTML}
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', dialogHTML);
}

/**
 * إغلاق خيارات الأقسام
 */
function closeDepartmentOptions() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) modal.remove();
}

/**
 * عرض خيارات التقارير الفردية
 */
function showEmployeeReportOptions() {
    const employees = window.employees || [];

    if (employees.length === 0) {
        showNotification('❌ لا توجد موظفين متاحين. يرجى إضافة موظفين أولاً.', 'warning', 5000);
        return;
    }

    const employeeOptions = employees.map((emp, index) =>
        `<div class="employee-option" onclick="generateEmployeeReport(${index}); closeEmployeeOptions();" style="
            padding: 10px;
            margin: 5px;
            background: #f8f9fa;
            border-radius: 6px;
            cursor: pointer;
            border: 1px solid #e9ecef;
            transition: all 0.3s;
        " onmouseover="this.style.background='#e3f2fd'" onmouseout="this.style.background='#f8f9fa'">
            <strong>${emp.name}</strong> (${emp.id})<br>
            <small style="color: #666;">${emp.department}</small>
        </div>`
    ).join('');

    const dialogHTML = `
        <div class="modal-overlay" onclick="closeEmployeeOptions()">
            <div class="modal-content" onclick="event.stopPropagation()" style="max-width: 700px; max-height: 80vh;">
                <div class="modal-header">
                    <h3>👤 اختيار موظف للتقرير</h3>
                    <button class="close-btn" onclick="closeEmployeeOptions()">×</button>
                </div>
                <div class="modal-body" style="max-height: 50vh; overflow-y: auto;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px;">
                        ${employeeOptions}
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', dialogHTML);
}

/**
 * إغلاق خيارات الموظفين
 */
function closeEmployeeOptions() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) modal.remove();
}

/**
 * إنشاء تقرير قسم محدد
 */
function generateDepartmentReport(department) {
    const employees = window.employees || [];

    let filteredEmployees;
    let reportTitle;

    if (department === 'all') {
        filteredEmployees = employees;
        reportTitle = 'تقرير جميع الأقسام';
    } else {
        filteredEmployees = employees.filter(emp => emp.department === department);
        reportTitle = `تقرير قسم ${department}`;
    }

    if (filteredEmployees.length === 0) {
        showNotification('❌ لا توجد بيانات للقسم المحدد', 'warning', 5000);
        return;
    }

    // حساب إحصائيات القسم
    let deptStats = {
        totalEmployees: filteredEmployees.length,
        totalCarriedOver: 0,
        totalUsed: 0,
        totalRemaining: 0,
        averageUsage: 0
    };

    filteredEmployees.forEach(emp => {
        deptStats.totalCarriedOver += emp.carriedOverLeave || 0;
        const totalUsed = (emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0);
        deptStats.totalUsed += totalUsed;

        const totalAvailable = emp.annualLeave + (emp.carriedOverLeave || 0);
        const remaining = totalAvailable - (emp.usedAnnual || 0);
        deptStats.totalRemaining += remaining;
    });

    deptStats.averageUsage = (deptStats.totalUsed / deptStats.totalEmployees).toFixed(1);

    showReportDisplay();
    document.getElementById('detailedReportContent').innerHTML = `
        <div class="department-report">
            <h3 style="text-align: center; color: #3498db; margin-bottom: 20px;">
                🏢 ${reportTitle}
            </h3>

            <!-- إحصائيات القسم -->
            <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
                <div class="stat-card" style="background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;">
                    <h4 style="color: #27ae60; margin: 0;">عدد الموظفين</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #27ae60;">${deptStats.totalEmployees}</div>
                </div>
                <div class="stat-card" style="background: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center;">
                    <h4 style="color: #3498db; margin: 0;">الرصيد المرحل</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #3498db;">${deptStats.totalCarriedOver}</div>
                </div>
                <div class="stat-card" style="background: #fff3e0; padding: 15px; border-radius: 8px; text-align: center;">
                    <h4 style="color: #f39c12; margin: 0;">المستخدم</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #f39c12;">${deptStats.totalUsed}</div>
                </div>
                <div class="stat-card" style="background: #f3e5f5; padding: 15px; border-radius: 8px; text-align: center;">
                    <h4 style="color: #9c27b0; margin: 0;">متوسط الاستخدام</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #9c27b0;">${deptStats.averageUsage}</div>
                </div>
            </div>

            <!-- جدول الموظفين -->
            <h4 style="color: #2c3e50; margin-bottom: 15px;">👥 تفاصيل الموظفين</h4>
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f8f9fa;">
                            <th style="padding: 12px; border: 1px solid #dee2e6;">اسم الموظف</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">الرقم الوظيفي</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">تاريخ التوظيف</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">الرصيد المرحل</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">المستخدم</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">المتبقي</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredEmployees.map(emp => {
                            const totalUsed = (emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0);
                            const totalAvailable = emp.annualLeave + (emp.carriedOverLeave || 0);
                            const remaining = totalAvailable - (emp.usedAnnual || 0);
                            const status = getLeaveStatus(emp);

                            return `
                                <tr>
                                    <td style="padding: 12px; border: 1px solid #dee2e6;">${emp.name}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${emp.id}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${emp.hireDate}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${emp.carriedOverLeave || 0}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${totalUsed}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${remaining}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">
                                        <span style="color: ${status.color};">${status.status}</span>
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        </div>
    `;
}

/**
 * إنشاء تقرير موظف محدد
 */
function generateEmployeeReport(employeeIndex) {
    const employee = window.employees[employeeIndex];

    if (!employee) {
        showNotification('❌ لم يتم العثور على الموظف', 'warning', 5000);
        return;
    }

    const totalUsed = (employee.usedAnnual || 0) + (employee.usedSick || 0) + (employee.usedEmergency || 0);
    const totalAvailable = employee.annualLeave + (employee.carriedOverLeave || 0);
    const remaining = totalAvailable - (employee.usedAnnual || 0);
    const status = getLeaveStatus(employee);
    const yearsOfService = calculateYearsOfService(employee.hireDate);

    showReportDisplay();
    document.getElementById('detailedReportContent').innerHTML = `
        <div class="employee-report">
            <h3 style="text-align: center; color: #3498db; margin-bottom: 20px;">
                👤 تقرير الموظف: ${employee.name}
            </h3>

            <!-- معلومات الموظف الأساسية -->
            <div class="employee-info-section" style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">📋 المعلومات الأساسية</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                    <div><strong>الاسم:</strong> ${employee.name}</div>
                    <div><strong>الرقم الوظيفي:</strong> ${employee.id}</div>
                    <div><strong>القسم:</strong> ${employee.department}</div>
                    <div><strong>تاريخ التوظيف:</strong> ${employee.hireDate}</div>
                    <div><strong>سنوات الخدمة:</strong> ${yearsOfService} سنة</div>
                    <div><strong>حالة الرصيد:</strong> <span style="color: ${status.color};">${status.status}</span></div>
                </div>
            </div>

            <!-- أرصدة الإجازات -->
            <div class="leave-balances" style="margin-bottom: 20px;">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">⚖️ أرصدة الإجازات</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <div class="balance-card" style="background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;">
                        <h5 style="color: #27ae60; margin: 0 0 10px 0;">الإجازة السنوية</h5>
                        <div style="font-size: 18px; font-weight: bold; color: #27ae60;">
                            ${employee.annualLeave + (employee.carriedOverLeave || 0)} متاح
                        </div>
                        <div style="font-size: 14px; color: #666;">
                            (${employee.annualLeave} + ${employee.carriedOverLeave || 0} مرحل)
                        </div>
                        <div style="font-size: 16px; margin-top: 5px;">
                            مستخدم: ${employee.usedAnnual || 0} | متبقي: ${totalAvailable - (employee.usedAnnual || 0)}
                        </div>
                    </div>

                    <div class="balance-card" style="background: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center;">
                        <h5 style="color: #3498db; margin: 0 0 10px 0;">الإجازة المرضية</h5>
                        <div style="font-size: 18px; font-weight: bold; color: #3498db;">
                            ${employee.sickLeave} متاح
                        </div>
                        <div style="font-size: 16px; margin-top: 5px;">
                            مستخدم: ${employee.usedSick || 0} | متبقي: ${employee.sickLeave - (employee.usedSick || 0)}
                        </div>
                    </div>

                    <div class="balance-card" style="background: #fff3e0; padding: 15px; border-radius: 8px; text-align: center;">
                        <h5 style="color: #f39c12; margin: 0 0 10px 0;">الإجازة الطارئة</h5>
                        <div style="font-size: 18px; font-weight: bold; color: #f39c12;">
                            ${employee.emergencyLeave} متاح
                        </div>
                        <div style="font-size: 16px; margin-top: 5px;">
                            مستخدم: ${employee.usedEmergency || 0} | متبقي: ${employee.emergencyLeave - (employee.usedEmergency || 0)}
                        </div>
                    </div>
                </div>
            </div>

            <!-- سجل الإجازات -->
            <div class="leave-history">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">📅 سجل الإجازات</h4>
                ${employee.leaveHistory && employee.leaveHistory.length > 0 ? `
                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    <th style="padding: 12px; border: 1px solid #dee2e6;">نوع الإجازة</th>
                                    <th style="padding: 12px; border: 1px solid #dee2e6;">تاريخ البداية</th>
                                    <th style="padding: 12px; border: 1px solid #dee2e6;">تاريخ النهاية</th>
                                    <th style="padding: 12px; border: 1px solid #dee2e6;">عدد الأيام</th>
                                    <th style="padding: 12px; border: 1px solid #dee2e6;">السبب</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${employee.leaveHistory.map(leave => `
                                    <tr>
                                        <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${leave.type}</td>
                                        <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${leave.startDate}</td>
                                        <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${leave.endDate}</td>
                                        <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${leave.days}</td>
                                        <td style="padding: 12px; border: 1px solid #dee2e6;">${leave.reason || 'غير محدد'}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                ` : `
                    <div style="text-align: center; padding: 40px; color: #7f8c8d;">
                        <span style="font-size: 48px;">📝</span>
                        <h4>لا يوجد سجل إجازات</h4>
                        <p>لم يتم تسجيل أي إجازات لهذا الموظف بعد</p>
                    </div>
                `}
            </div>
        </div>
    `;
}

// ===== وظائف التقارير الجديدة =====

/**
 * إنشاء التقرير الشهري
 */
function generateMonthlyReport() {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    const monthName = new Intl.DateTimeFormat('ar-SA', { month: 'long' }).format(currentDate);

    if (window.employees.length === 0) {
        showNotification('❌ لا توجد بيانات موظفين لإنشاء التقرير الشهري', 'warning', 5000);
        return;
    }

    // حساب الإحصائيات الشهرية
    let monthlyStats = {
        totalEmployees: window.employees.length,
        newHires: 0,
        totalLeavesTaken: 0,
        mostActiveLeaveType: { type: '', count: 0 },
        departmentStats: {}
    };

    // تحليل البيانات
    window.employees.forEach(emp => {
        const hireDate = new Date(emp.hireDate);
        if (hireDate.getMonth() === currentMonth && hireDate.getFullYear() === currentYear) {
            monthlyStats.newHires++;
        }

        // إحصائيات الأقسام
        if (!monthlyStats.departmentStats[emp.department]) {
            monthlyStats.departmentStats[emp.department] = {
                employees: 0,
                totalUsed: 0
            };
        }
        monthlyStats.departmentStats[emp.department].employees++;

        const totalUsed = (emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0);
        monthlyStats.departmentStats[emp.department].totalUsed += totalUsed;
        monthlyStats.totalLeavesTaken += totalUsed;
    });

    showReportDisplay();
    document.getElementById('detailedReportContent').innerHTML = `
        <div class="monthly-report">
            <h3 style="text-align: center; color: #3498db; margin-bottom: 20px;">
                📅 التقرير الشهري - ${monthName} ${currentYear}
            </h3>

            <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
                <div class="stat-card" style="background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;">
                    <h4 style="color: #27ae60; margin: 0;">إجمالي الموظفين</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #27ae60;">${monthlyStats.totalEmployees}</div>
                </div>
                <div class="stat-card" style="background: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center;">
                    <h4 style="color: #3498db; margin: 0;">موظفين جدد</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #3498db;">${monthlyStats.newHires}</div>
                </div>
                <div class="stat-card" style="background: #fff3e0; padding: 15px; border-radius: 8px; text-align: center;">
                    <h4 style="color: #f39c12; margin: 0;">إجمالي الإجازات المستخدمة</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #f39c12;">${monthlyStats.totalLeavesTaken}</div>
                </div>
            </div>

            <h4 style="color: #2c3e50; margin-bottom: 15px;">📊 إحصائيات الأقسام</h4>
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f8f9fa;">
                            <th style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">القسم</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">عدد الموظفين</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">الإجازات المستخدمة</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">متوسط الاستخدام</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${Object.entries(monthlyStats.departmentStats).map(([dept, stats]) => `
                            <tr>
                                <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${dept}</td>
                                <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${stats.employees}</td>
                                <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${stats.totalUsed}</td>
                                <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${(stats.totalUsed / stats.employees).toFixed(1)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        </div>
    `;
}

/**
 * تقرير أرصدة الإجازات
 */
function generateLeaveBalanceReport() {
    if (window.employees.length === 0) {
        showNotification('❌ لا توجد بيانات موظفين لإنشاء تقرير الأرصدة', 'warning', 5000);
        return;
    }

    showReportDisplay();
    document.getElementById('detailedReportContent').innerHTML = `
        <div class="balance-report">
            <h3 style="text-align: center; color: #3498db; margin-bottom: 20px;">
                ⚖️ تقرير أرصدة الإجازات
            </h3>

            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f8f9fa;">
                            <th style="padding: 12px; border: 1px solid #dee2e6;">اسم الموظف</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">القسم</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">الرصيد السنوي</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">المرحل</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">المستخدم</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">المتبقي</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${window.employees.map(emp => {
                            const totalAvailable = emp.annualLeave + (emp.carriedOverLeave || 0);
                            const totalUsed = (emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0);
                            const remaining = totalAvailable - (emp.usedAnnual || 0);
                            const status = getLeaveStatus(emp);

                            return `
                                <tr>
                                    <td style="padding: 12px; border: 1px solid #dee2e6;">${emp.name}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${emp.department}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${emp.annualLeave}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${emp.carriedOverLeave || 0}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${totalUsed}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${remaining}</td>
                                    <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">
                                        <span style="color: ${status.color};">${status.status}</span>
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        </div>
    `;
}

/**
 * تقرير الأرصدة المنخفضة
 */
function generateLowBalanceReport() {
    if (window.employees.length === 0) {
        showNotification('❌ لا توجد بيانات موظفين لإنشاء التقرير', 'warning', 5000);
        return;
    }

    const lowBalanceEmployees = window.employees.filter(emp => {
        const totalAvailable = emp.annualLeave + (emp.carriedOverLeave || 0);
        const remaining = totalAvailable - (emp.usedAnnual || 0);
        return remaining <= 5; // أقل من أو يساوي 5 أيام
    });

    showReportDisplay();
    document.getElementById('detailedReportContent').innerHTML = `
        <div class="low-balance-report">
            <h3 style="text-align: center; color: #e74c3c; margin-bottom: 20px;">
                ⚠️ تقرير الأرصدة المنخفضة
            </h3>

            ${lowBalanceEmployees.length === 0 ? `
                <div style="text-align: center; padding: 40px; color: #27ae60;">
                    <span style="font-size: 48px;">✅</span>
                    <h4>ممتاز! لا توجد أرصدة منخفضة</h4>
                    <p>جميع الموظفين لديهم أرصدة إجازات كافية</p>
                </div>
            ` : `
                <div class="alert alert-warning" style="margin-bottom: 20px;">
                    <strong>تنبيه:</strong> يوجد ${lowBalanceEmployees.length} موظف لديهم أرصدة إجازات منخفضة (5 أيام أو أقل)
                </div>

                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 12px; border: 1px solid #dee2e6;">اسم الموظف</th>
                                <th style="padding: 12px; border: 1px solid #dee2e6;">القسم</th>
                                <th style="padding: 12px; border: 1px solid #dee2e6;">الرصيد المتبقي</th>
                                <th style="padding: 12px; border: 1px solid #dee2e6;">المستخدم</th>
                                <th style="padding: 12px; border: 1px solid #dee2e6;">مستوى التحذير</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${lowBalanceEmployees.map(emp => {
                                const totalAvailable = emp.annualLeave + (emp.carriedOverLeave || 0);
                                const remaining = totalAvailable - (emp.usedAnnual || 0);
                                const totalUsed = (emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0);

                                let warningLevel = '';
                                let warningColor = '';
                                if (remaining <= 0) {
                                    warningLevel = 'نفد الرصيد';
                                    warningColor = '#e74c3c';
                                } else if (remaining <= 2) {
                                    warningLevel = 'حرج جداً';
                                    warningColor = '#e74c3c';
                                } else if (remaining <= 5) {
                                    warningLevel = 'منخفض';
                                    warningColor = '#f39c12';
                                }

                                return `
                                    <tr>
                                        <td style="padding: 12px; border: 1px solid #dee2e6;">${emp.name}</td>
                                        <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${emp.department}</td>
                                        <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center; color: ${warningColor}; font-weight: bold;">${remaining}</td>
                                        <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${totalUsed}</td>
                                        <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">
                                            <span style="color: ${warningColor}; font-weight: bold;">${warningLevel}</span>
                                        </td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            `}
        </div>
    `;
}

/**
 * إظهار منطقة عرض التقارير
 */
function showReportDisplay() {
    const reportCard = document.getElementById('reportDisplayCard');
    if (reportCard) {
        reportCard.style.display = 'block';
        reportCard.scrollIntoView({ behavior: 'smooth' });
    }
}

/**
 * إخفاء منطقة عرض التقارير
 */
function hideReportDisplay() {
    const reportCard = document.getElementById('reportDisplayCard');
    if (reportCard) {
        reportCard.style.display = 'none';
    }
}


