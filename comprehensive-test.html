<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل - نظام إدارة إجازات الموظفين</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            margin: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-section h2 {
            color: #3498db;
            margin-top: 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #ddd;
        }
        .test-item.running {
            border-left-color: #f39c12;
            background: #fff3cd;
        }
        .test-item.passed {
            border-left-color: #27ae60;
            background: #d4edda;
        }
        .test-item.failed {
            border-left-color: #e74c3c;
            background: #f8d7da;
        }
        .test-item.warning {
            border-left-color: #f39c12;
            background: #fff3cd;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn.danger {
            background: #e74c3c;
        }
        .btn.success {
            background: #27ae60;
        }
        .btn.warning {
            background: #f39c12;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #27ae60);
            width: 0%;
            transition: width 0.3s ease;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
        }
        .test-log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .responsive-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .device-frame {
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 10px;
            background: white;
            text-align: center;
        }
        .device-screen {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            min-height: 200px;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: #2c3e50;">🧪 اختبار شامل - نظام إدارة إجازات الموظفين</h1>
        
        <!-- إحصائيات الاختبار -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalTests">0</div>
                <div>إجمالي الاختبارات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="passedTests" style="color: #27ae60;">0</div>
                <div>نجح</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="failedTests" style="color: #e74c3c;">0</div>
                <div>فشل</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="warningTests" style="color: #f39c12;">0</div>
                <div>تحذيرات</div>
            </div>
        </div>

        <!-- شريط التقدم -->
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <!-- أزرار التحكم -->
        <div style="text-align: center; margin: 20px 0;">
            <button class="btn success" onclick="runAllTests()">🚀 تشغيل جميع الاختبارات</button>
            <button class="btn" onclick="runBasicTests()">🔧 الوظائف الأساسية</button>
            <button class="btn" onclick="runReportsTests()">📊 التقارير</button>
            <button class="btn" onclick="runSettingsTests()">⚙️ الإعدادات</button>
            <button class="btn" onclick="runDataTests()">💾 البيانات</button>
            <button class="btn" onclick="runResponsiveTests()">📱 التصميم المتجاوب</button>
            <button class="btn" onclick="runPerformanceTests()">⚡ الأداء</button>
            <button class="btn danger" onclick="clearResults()">🗑️ مسح النتائج</button>
        </div>

        <!-- سجل الاختبار -->
        <div class="test-log" id="testLog">
            جاهز لبدء الاختبارات...
        </div>

        <!-- 1. اختبار الوظائف الأساسية -->
        <div class="test-section">
            <h2>🔧 اختبار الوظائف الأساسية</h2>
            <div id="basicTests">
                <!-- نتائج الاختبارات ستظهر هنا -->
            </div>
        </div>

        <!-- 2. اختبار التقارير -->
        <div class="test-section">
            <h2>📊 اختبار التقارير المحسنة</h2>
            <div id="reportsTests">
                <!-- نتائج الاختبارات ستظهر هنا -->
            </div>
        </div>

        <!-- 3. اختبار الإعدادات -->
        <div class="test-section">
            <h2>⚙️ اختبار الإعدادات الجديدة</h2>
            <div id="settingsTests">
                <!-- نتائج الاختبارات ستظهر هنا -->
            </div>
        </div>

        <!-- 4. اختبار البيانات -->
        <div class="test-section">
            <h2>💾 اختبار حفظ واسترداد البيانات</h2>
            <div id="dataTests">
                <!-- نتائج الاختبارات ستظهر هنا -->
            </div>
        </div>

        <!-- 5. اختبار التصميم المتجاوب -->
        <div class="test-section">
            <h2>📱 اختبار التصميم المتجاوب</h2>
            <div class="responsive-test">
                <div class="device-frame">
                    <h4>💻 شاشة كبيرة (>768px)</h4>
                    <div class="device-screen" id="desktopTest">
                        <iframe src="index.html" width="100%" height="200" style="border: none; transform: scale(0.3); transform-origin: top left; width: 333%;"></iframe>
                    </div>
                </div>
                <div class="device-frame">
                    <h4>📱 جهاز لوحي (768px-480px)</h4>
                    <div class="device-screen" id="tabletTest">
                        <iframe src="index.html" width="100%" height="200" style="border: none; transform: scale(0.4); transform-origin: top left; width: 250%;"></iframe>
                    </div>
                </div>
                <div class="device-frame">
                    <h4>📱 هاتف ذكي (<480px)</h4>
                    <div class="device-screen" id="mobileTest">
                        <iframe src="index.html" width="100%" height="200" style="border: none; transform: scale(0.5); transform-origin: top left; width: 200%;"></iframe>
                    </div>
                </div>
            </div>
            <div id="responsiveTests">
                <!-- نتائج الاختبارات ستظهر هنا -->
            </div>
        </div>

        <!-- 6. اختبار الأداء -->
        <div class="test-section">
            <h2>⚡ اختبار الأداء والاستقرار</h2>
            <div id="performanceTests">
                <!-- نتائج الاختبارات ستظهر هنا -->
            </div>
        </div>

        <!-- 7. اختبار التوافق -->
        <div class="test-section">
            <h2>🌐 اختبار التوافق</h2>
            <div id="compatibilityTests">
                <!-- نتائج الاختبارات ستظهر هنا -->
            </div>
        </div>
    </div>

    <!-- تحميل ملفات النظام للاختبار -->
    <script src="utils.js"></script>
    <script src="core.js"></script>
    <script src="sections.js"></script>
    <script src="functions.js"></script>
    <script src="features.js"></script>
    <script src="extras.js"></script>
    <script src="reports-advanced.js"></script>
    <script src="settings-functions.js"></script>
    <script src="app.js"></script>

    <script>
        // متغيرات الاختبار
        let testStats = {
            total: 0,
            passed: 0,
            failed: 0,
            warnings: 0
        };

        let currentTestIndex = 0;
        let totalTestsToRun = 0;

        // وظائف مساعدة للاختبار
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const typeIcon = {
                'info': 'ℹ️',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️'
            };
            
            logElement.innerHTML += `[${timestamp}] ${typeIcon[type]} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStats() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('passedTests').textContent = testStats.passed;
            document.getElementById('failedTests').textContent = testStats.failed;
            document.getElementById('warningTests').textContent = testStats.warnings;
            
            const progress = totalTestsToRun > 0 ? (currentTestIndex / totalTestsToRun) * 100 : 0;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        function addTestResult(container, testName, result, details = '') {
            const testItem = document.createElement('div');
            testItem.className = `test-item ${result}`;
            testItem.innerHTML = `
                <strong>${testName}</strong>
                ${details ? `<br><small>${details}</small>` : ''}
            `;
            document.getElementById(container).appendChild(testItem);
            
            testStats.total++;
            if (result === 'passed') testStats.passed++;
            else if (result === 'failed') testStats.failed++;
            else if (result === 'warning') testStats.warnings++;
            
            currentTestIndex++;
            updateStats();
        }

        function clearResults() {
            const containers = ['basicTests', 'reportsTests', 'settingsTests', 'dataTests', 'responsiveTests', 'performanceTests', 'compatibilityTests'];
            containers.forEach(container => {
                document.getElementById(container).innerHTML = '';
            });
            
            testStats = { total: 0, passed: 0, failed: 0, warnings: 0 };
            currentTestIndex = 0;
            totalTestsToRun = 0;
            updateStats();
            
            document.getElementById('testLog').innerHTML = 'تم مسح النتائج. جاهز لبدء اختبارات جديدة...\n';
            log('تم مسح جميع نتائج الاختبارات', 'info');
        }

        // تشغيل جميع الاختبارات
        async function runAllTests() {
            clearResults();
            log('بدء تشغيل جميع الاختبارات الشاملة...', 'info');
            
            totalTestsToRun = 50; // تقدير عدد الاختبارات
            
            await runBasicTests();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await runReportsTests();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await runSettingsTests();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await runDataTests();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await runResponsiveTests();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await runPerformanceTests();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await runCompatibilityTests();
            
            log(`اكتملت جميع الاختبارات! النتائج: ${testStats.passed} نجح، ${testStats.failed} فشل، ${testStats.warnings} تحذير`, 'success');
        }

        // 1. اختبار الوظائف الأساسية
        async function runBasicTests() {
            log('بدء اختبار الوظائف الأساسية...', 'info');

            // اختبار تحميل البيانات التجريبية
            try {
                if (typeof loadTestData === 'function') {
                    window.employees = [];
                    loadTestData();
                    if (window.employees.length > 0) {
                        addTestResult('basicTests', 'تحميل البيانات التجريبية', 'passed', `تم تحميل ${window.employees.length} موظف`);
                    } else {
                        addTestResult('basicTests', 'تحميل البيانات التجريبية', 'failed', 'لم يتم تحميل أي بيانات');
                    }
                } else {
                    addTestResult('basicTests', 'تحميل البيانات التجريبية', 'failed', 'وظيفة loadTestData غير متاحة');
                }
            } catch (error) {
                addTestResult('basicTests', 'تحميل البيانات التجريبية', 'failed', error.message);
            }

            // اختبار إضافة موظف جديد
            try {
                const initialCount = window.employees.length;
                const testEmployee = {
                    name: 'موظف اختبار',
                    id: 'TEST999',
                    department: 'قسم الاختبار',
                    hireDate: '2024-01-01',
                    annualLeave: 30,
                    carriedOverLeave: 5,
                    sickLeave: 15,
                    emergencyLeave: 5,
                    usedAnnual: 0,
                    usedSick: 0,
                    usedEmergency: 0,
                    leaveHistory: []
                };

                window.employees.push(testEmployee);

                if (window.employees.length === initialCount + 1) {
                    addTestResult('basicTests', 'إضافة موظف جديد', 'passed', 'تم إضافة الموظف بنجاح');
                } else {
                    addTestResult('basicTests', 'إضافة موظف جديد', 'failed', 'فشل في إضافة الموظف');
                }
            } catch (error) {
                addTestResult('basicTests', 'إضافة موظف جديد', 'failed', error.message);
            }

            // اختبار تسجيل إجازة
            try {
                if (window.employees.length > 0) {
                    const employee = window.employees[0];
                    const initialUsed = employee.usedAnnual || 0;

                    // محاكاة تسجيل إجازة
                    employee.usedAnnual = (employee.usedAnnual || 0) + 3;

                    if (employee.usedAnnual === initialUsed + 3) {
                        addTestResult('basicTests', 'تسجيل إجازة سنوية', 'passed', 'تم تسجيل 3 أيام إجازة');
                    } else {
                        addTestResult('basicTests', 'تسجيل إجازة سنوية', 'failed', 'فشل في تسجيل الإجازة');
                    }
                } else {
                    addTestResult('basicTests', 'تسجيل إجازة سنوية', 'failed', 'لا توجد بيانات موظفين');
                }
            } catch (error) {
                addTestResult('basicTests', 'تسجيل إجازة سنوية', 'failed', error.message);
            }

            // اختبار حساب الأرصدة
            try {
                if (window.employees.length > 0 && typeof getLeaveStatus === 'function') {
                    const employee = window.employees[0];
                    const status = getLeaveStatus(employee);

                    if (status && status.status && status.color) {
                        addTestResult('basicTests', 'حساب حالة الرصيد', 'passed', `الحالة: ${status.status}`);
                    } else {
                        addTestResult('basicTests', 'حساب حالة الرصيد', 'failed', 'فشل في حساب الحالة');
                    }
                } else {
                    addTestResult('basicTests', 'حساب حالة الرصيد', 'warning', 'وظيفة getLeaveStatus غير متاحة');
                }
            } catch (error) {
                addTestResult('basicTests', 'حساب حالة الرصيد', 'failed', error.message);
            }

            // اختبار البحث والفلترة
            try {
                if (typeof filterEmployees === 'function') {
                    const searchTerm = 'أحمد';
                    filterEmployees(searchTerm);
                    addTestResult('basicTests', 'البحث والفلترة', 'passed', `البحث عن: ${searchTerm}`);
                } else {
                    addTestResult('basicTests', 'البحث والفلترة', 'warning', 'وظيفة filterEmployees غير متاحة');
                }
            } catch (error) {
                addTestResult('basicTests', 'البحث والفلترة', 'failed', error.message);
            }

            log('اكتمل اختبار الوظائف الأساسية', 'success');
        }

        // 2. اختبار التقارير
        async function runReportsTests() {
            log('بدء اختبار التقارير المحسنة...', 'info');

            // اختبار التقرير الشامل
            try {
                if (typeof generateDetailedReport === 'function') {
                    generateDetailedReport();
                    addTestResult('reportsTests', 'التقرير الشامل', 'passed', 'تم إنشاء التقرير بنجاح');
                } else {
                    addTestResult('reportsTests', 'التقرير الشامل', 'failed', 'وظيفة generateDetailedReport غير متاحة');
                }
            } catch (error) {
                addTestResult('reportsTests', 'التقرير الشامل', 'failed', error.message);
            }

            // اختبار التقرير الشهري
            try {
                if (typeof generateMonthlyReport === 'function') {
                    generateMonthlyReport();
                    addTestResult('reportsTests', 'التقرير الشهري', 'passed', 'تم إنشاء التقرير الشهري');
                } else {
                    addTestResult('reportsTests', 'التقرير الشهري', 'failed', 'وظيفة generateMonthlyReport غير متاحة');
                }
            } catch (error) {
                addTestResult('reportsTests', 'التقرير الشهري', 'failed', error.message);
            }

            // اختبار تقرير الأرصدة
            try {
                if (typeof generateLeaveBalanceReport === 'function') {
                    generateLeaveBalanceReport();
                    addTestResult('reportsTests', 'تقرير الأرصدة', 'passed', 'تم إنشاء تقرير الأرصدة');
                } else {
                    addTestResult('reportsTests', 'تقرير الأرصدة', 'failed', 'وظيفة generateLeaveBalanceReport غير متاحة');
                }
            } catch (error) {
                addTestResult('reportsTests', 'تقرير الأرصدة', 'failed', error.message);
            }

            // اختبار تقرير الأرصدة المنخفضة
            try {
                if (typeof generateLowBalanceReport === 'function') {
                    generateLowBalanceReport();
                    addTestResult('reportsTests', 'تقرير الأرصدة المنخفضة', 'passed', 'تم إنشاء تقرير الأرصدة المنخفضة');
                } else {
                    addTestResult('reportsTests', 'تقرير الأرصدة المنخفضة', 'failed', 'وظيفة generateLowBalanceReport غير متاحة');
                }
            } catch (error) {
                addTestResult('reportsTests', 'تقرير الأرصدة المنخفضة', 'failed', error.message);
            }

            // اختبار تقارير الأقسام
            try {
                if (typeof generateDepartmentReport === 'function') {
                    generateDepartmentReport('all');
                    addTestResult('reportsTests', 'تقارير الأقسام', 'passed', 'تم إنشاء تقرير جميع الأقسام');
                } else {
                    addTestResult('reportsTests', 'تقارير الأقسام', 'failed', 'وظيفة generateDepartmentReport غير متاحة');
                }
            } catch (error) {
                addTestResult('reportsTests', 'تقارير الأقسام', 'failed', error.message);
            }

            // اختبار التقارير الفردية
            try {
                if (typeof generateEmployeeReport === 'function' && window.employees.length > 0) {
                    generateEmployeeReport(0);
                    addTestResult('reportsTests', 'التقارير الفردية', 'passed', 'تم إنشاء تقرير فردي');
                } else {
                    addTestResult('reportsTests', 'التقارير الفردية', 'warning', 'وظيفة generateEmployeeReport غير متاحة أو لا توجد بيانات');
                }
            } catch (error) {
                addTestResult('reportsTests', 'التقارير الفردية', 'failed', error.message);
            }

            // اختبار تصدير CSV
            try {
                if (typeof exportToCSV === 'function') {
                    // محاكاة التصدير (بدون تحميل فعلي)
                    addTestResult('reportsTests', 'تصدير CSV', 'passed', 'وظيفة التصدير متاحة');
                } else {
                    addTestResult('reportsTests', 'تصدير CSV', 'failed', 'وظيفة exportToCSV غير متاحة');
                }
            } catch (error) {
                addTestResult('reportsTests', 'تصدير CSV', 'failed', error.message);
            }

            // اختبار منطقة عرض التقارير
            try {
                if (typeof showReportDisplay === 'function' && typeof hideReportDisplay === 'function') {
                    showReportDisplay();
                    setTimeout(() => {
                        hideReportDisplay();
                    }, 1000);
                    addTestResult('reportsTests', 'منطقة عرض التقارير', 'passed', 'إظهار وإخفاء منطقة العرض');
                } else {
                    addTestResult('reportsTests', 'منطقة عرض التقارير', 'warning', 'وظائف العرض غير متاحة');
                }
            } catch (error) {
                addTestResult('reportsTests', 'منطقة عرض التقارير', 'failed', error.message);
            }

            log('اكتمل اختبار التقارير المحسنة', 'success');
        }

        // 3. اختبار الإعدادات
        async function runSettingsTests() {
            log('بدء اختبار الإعدادات الجديدة...', 'info');

            // اختبار تغيير حجم الخط
            try {
                if (typeof changeFontSize === 'function') {
                    changeFontSize('large');
                    setTimeout(() => changeFontSize('medium'), 500);
                    addTestResult('settingsTests', 'تغيير حجم الخط', 'passed', 'تم تغيير حجم الخط بنجاح');
                } else {
                    addTestResult('settingsTests', 'تغيير حجم الخط', 'failed', 'وظيفة changeFontSize غير متاحة');
                }
            } catch (error) {
                addTestResult('settingsTests', 'تغيير حجم الخط', 'failed', error.message);
            }

            // اختبار تغيير عدد العناصر في الصفحة
            try {
                if (typeof changeItemsPerPage === 'function') {
                    changeItemsPerPage(20);
                    addTestResult('settingsTests', 'تغيير عدد العناصر', 'passed', 'تم تغيير عدد العناصر إلى 20');
                } else {
                    addTestResult('settingsTests', 'تغيير عدد العناصر', 'failed', 'وظيفة changeItemsPerPage غير متاحة');
                }
            } catch (error) {
                addTestResult('settingsTests', 'تغيير عدد العناصر', 'failed', error.message);
            }

            // اختبار إعدادات الإجازات الافتراضية
            try {
                if (typeof updateDefaultAnnualLeave === 'function') {
                    updateDefaultAnnualLeave(35);
                    addTestResult('settingsTests', 'إعدادات الإجازات الافتراضية', 'passed', 'تم تحديث الرصيد السنوي إلى 35');
                } else {
                    addTestResult('settingsTests', 'إعدادات الإجازات الافتراضية', 'failed', 'وظيفة updateDefaultAnnualLeave غير متاحة');
                }
            } catch (error) {
                addTestResult('settingsTests', 'إعدادات الإجازات الافتراضية', 'failed', error.message);
            }

            // اختبار إعدادات التنبيهات
            try {
                if (typeof toggleLowBalanceAlert === 'function') {
                    toggleLowBalanceAlert(true);
                    addTestResult('settingsTests', 'إعدادات التنبيهات', 'passed', 'تم تفعيل تنبيهات الرصيد المنخفض');
                } else {
                    addTestResult('settingsTests', 'إعدادات التنبيهات', 'failed', 'وظيفة toggleLowBalanceAlert غير متاحة');
                }
            } catch (error) {
                addTestResult('settingsTests', 'إعدادات التنبيهات', 'failed', error.message);
            }

            // اختبار معلومات النظام
            try {
                if (typeof showSystemInfo === 'function') {
                    showSystemInfo();
                    setTimeout(() => {
                        if (typeof closeSystemInfo === 'function') {
                            closeSystemInfo();
                        }
                    }, 1000);
                    addTestResult('settingsTests', 'معلومات النظام', 'passed', 'تم عرض معلومات النظام');
                } else {
                    addTestResult('settingsTests', 'معلومات النظام', 'failed', 'وظيفة showSystemInfo غير متاحة');
                }
            } catch (error) {
                addTestResult('settingsTests', 'معلومات النظام', 'failed', error.message);
            }

            // اختبار حفظ واسترداد الإعدادات
            try {
                if (typeof saveSystemSettings === 'function' && typeof loadSystemSettings === 'function') {
                    saveSystemSettings();
                    loadSystemSettings();
                    addTestResult('settingsTests', 'حفظ واسترداد الإعدادات', 'passed', 'تم حفظ واسترداد الإعدادات');
                } else {
                    addTestResult('settingsTests', 'حفظ واسترداد الإعدادات', 'warning', 'وظائف الحفظ/الاسترداد غير متاحة');
                }
            } catch (error) {
                addTestResult('settingsTests', 'حفظ واسترداد الإعدادات', 'failed', error.message);
            }

            log('اكتمل اختبار الإعدادات الجديدة', 'success');
        }

        // 4. اختبار البيانات
        async function runDataTests() {
            log('بدء اختبار حفظ واسترداد البيانات...', 'info');

            // اختبار التشفير مع النصوص العربية
            try {
                if (typeof simpleEncrypt === 'function' && typeof simpleDecrypt === 'function') {
                    const testText = 'اختبار النص العربي مع أرقام 123 ورموز !@#';
                    const encrypted = simpleEncrypt(testText);
                    const decrypted = simpleDecrypt(encrypted);

                    if (decrypted === testText) {
                        addTestResult('dataTests', 'التشفير مع النصوص العربية', 'passed', 'التشفير وفك التشفير يعمل بنجاح');
                    } else {
                        addTestResult('dataTests', 'التشفير مع النصوص العربية', 'failed', 'فشل في التشفير/فك التشفير');
                    }
                } else {
                    addTestResult('dataTests', 'التشفير مع النصوص العربية', 'failed', 'وظائف التشفير غير متاحة');
                }
            } catch (error) {
                addTestResult('dataTests', 'التشفير مع النصوص العربية', 'failed', error.message);
            }

            // اختبار حفظ البيانات
            try {
                if (typeof saveData === 'function') {
                    const result = saveData();
                    if (result) {
                        addTestResult('dataTests', 'حفظ البيانات', 'passed', 'تم حفظ البيانات بنجاح');
                    } else {
                        addTestResult('dataTests', 'حفظ البيانات', 'warning', 'فشل في حفظ البيانات ولكن تم التعامل مع الخطأ');
                    }
                } else {
                    addTestResult('dataTests', 'حفظ البيانات', 'failed', 'وظيفة saveData غير متاحة');
                }
            } catch (error) {
                addTestResult('dataTests', 'حفظ البيانات', 'failed', error.message);
            }

            // اختبار تحميل البيانات
            try {
                if (typeof loadDataFromStorage === 'function') {
                    const result = loadDataFromStorage();
                    if (result) {
                        addTestResult('dataTests', 'تحميل البيانات', 'passed', 'تم تحميل البيانات بنجاح');
                    } else {
                        addTestResult('dataTests', 'تحميل البيانات', 'warning', 'لم يتم العثور على بيانات محفوظة');
                    }
                } else {
                    addTestResult('dataTests', 'تحميل البيانات', 'failed', 'وظيفة loadDataFromStorage غير متاحة');
                }
            } catch (error) {
                addTestResult('dataTests', 'تحميل البيانات', 'failed', error.message);
            }

            // اختبار تصدير JSON
            try {
                if (typeof exportToJSON === 'function') {
                    // محاكاة التصدير
                    addTestResult('dataTests', 'تصدير JSON', 'passed', 'وظيفة تصدير JSON متاحة');
                } else {
                    addTestResult('dataTests', 'تصدير JSON', 'failed', 'وظيفة exportToJSON غير متاحة');
                }
            } catch (error) {
                addTestResult('dataTests', 'تصدير JSON', 'failed', error.message);
            }

            // اختبار النسخ الاحتياطية
            try {
                if (typeof cleanOldBackups === 'function') {
                    cleanOldBackups();
                    addTestResult('dataTests', 'إدارة النسخ الاحتياطية', 'passed', 'تم تنظيف النسخ الاحتياطية القديمة');
                } else {
                    addTestResult('dataTests', 'إدارة النسخ الاحتياطية', 'warning', 'وظيفة cleanOldBackups غير متاحة');
                }
            } catch (error) {
                addTestResult('dataTests', 'إدارة النسخ الاحتياطية', 'failed', error.message);
            }

            log('اكتمل اختبار حفظ واسترداد البيانات', 'success');
        }

        // 5. اختبار التصميم المتجاوب
        async function runResponsiveTests() {
            log('بدء اختبار التصميم المتجاوب...', 'info');

            // اختبار الشاشات الكبيرة
            try {
                const isLargeScreen = window.innerWidth > 768;
                addTestResult('responsiveTests', 'الشاشات الكبيرة (>768px)',
                    isLargeScreen ? 'passed' : 'warning',
                    `عرض الشاشة الحالي: ${window.innerWidth}px`);
            } catch (error) {
                addTestResult('responsiveTests', 'الشاشات الكبيرة', 'failed', error.message);
            }

            // اختبار CSS Grid والتخطيط المتجاوب
            try {
                const testElement = document.createElement('div');
                testElement.style.display = 'grid';
                testElement.style.gridTemplateColumns = 'repeat(auto-fit, minmax(250px, 1fr))';
                document.body.appendChild(testElement);

                const computedStyle = window.getComputedStyle(testElement);
                const isGridSupported = computedStyle.display === 'grid';

                document.body.removeChild(testElement);

                addTestResult('responsiveTests', 'دعم CSS Grid',
                    isGridSupported ? 'passed' : 'failed',
                    isGridSupported ? 'CSS Grid مدعوم' : 'CSS Grid غير مدعوم');
            } catch (error) {
                addTestResult('responsiveTests', 'دعم CSS Grid', 'failed', error.message);
            }

            // اختبار الوضع الليلي
            try {
                if (typeof toggleTheme === 'function') {
                    const body = document.body;
                    const initialTheme = body.classList.contains('dark-mode');

                    toggleTheme();
                    const afterToggle = body.classList.contains('dark-mode');

                    if (initialTheme !== afterToggle) {
                        addTestResult('responsiveTests', 'الوضع الليلي', 'passed', 'تم تبديل الوضع الليلي بنجاح');
                    } else {
                        addTestResult('responsiveTests', 'الوضع الليلي', 'warning', 'لم يتم تبديل الوضع');
                    }
                } else {
                    addTestResult('responsiveTests', 'الوضع الليلي', 'failed', 'وظيفة toggleTheme غير متاحة');
                }
            } catch (error) {
                addTestResult('responsiveTests', 'الوضع الليلي', 'failed', error.message);
            }

            // اختبار دعم اللمس
            try {
                const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
                addTestResult('responsiveTests', 'دعم اللمس',
                    isTouchDevice ? 'passed' : 'warning',
                    isTouchDevice ? 'جهاز يدعم اللمس' : 'جهاز لا يدعم اللمس');
            } catch (error) {
                addTestResult('responsiveTests', 'دعم اللمس', 'failed', error.message);
            }

            // اختبار التنسيق العربي (RTL)
            try {
                const body = document.body;
                const direction = window.getComputedStyle(body).direction;
                addTestResult('responsiveTests', 'التنسيق العربي (RTL)',
                    direction === 'rtl' ? 'passed' : 'failed',
                    `اتجاه النص: ${direction}`);
            } catch (error) {
                addTestResult('responsiveTests', 'التنسيق العربي (RTL)', 'failed', error.message);
            }

            log('اكتمل اختبار التصميم المتجاوب', 'success');
        }

        // 6. اختبار الأداء
        async function runPerformanceTests() {
            log('بدء اختبار الأداء والاستقرار...', 'info');

            // اختبار سرعة التحميل
            try {
                const startTime = performance.now();

                // محاكاة تحميل البيانات
                if (typeof loadTestData === 'function') {
                    loadTestData();
                }

                const endTime = performance.now();
                const loadTime = endTime - startTime;

                addTestResult('performanceTests', 'سرعة تحميل البيانات',
                    loadTime < 100 ? 'passed' : loadTime < 500 ? 'warning' : 'failed',
                    `وقت التحميل: ${loadTime.toFixed(2)} مللي ثانية`);
            } catch (error) {
                addTestResult('performanceTests', 'سرعة تحميل البيانات', 'failed', error.message);
            }

            // اختبار استهلاك الذاكرة
            try {
                if (performance.memory) {
                    const memoryInfo = performance.memory;
                    const usedMemory = memoryInfo.usedJSHeapSize / 1024 / 1024; // MB

                    addTestResult('performanceTests', 'استهلاك الذاكرة',
                        usedMemory < 50 ? 'passed' : usedMemory < 100 ? 'warning' : 'failed',
                        `الذاكرة المستخدمة: ${usedMemory.toFixed(2)} MB`);
                } else {
                    addTestResult('performanceTests', 'استهلاك الذاكرة', 'warning', 'معلومات الذاكرة غير متاحة');
                }
            } catch (error) {
                addTestResult('performanceTests', 'استهلاك الذاكرة', 'failed', error.message);
            }

            // اختبار التعامل مع البيانات الكبيرة
            try {
                const startTime = performance.now();
                const largeDataSet = [];

                // إنشاء 1000 موظف وهمي
                for (let i = 0; i < 1000; i++) {
                    largeDataSet.push({
                        name: `موظف ${i}`,
                        id: `EMP${i.toString().padStart(4, '0')}`,
                        department: `قسم ${i % 10}`,
                        hireDate: '2020-01-01',
                        annualLeave: 30,
                        carriedOverLeave: 0,
                        sickLeave: 15,
                        emergencyLeave: 5,
                        usedAnnual: 0,
                        usedSick: 0,
                        usedEmergency: 0,
                        leaveHistory: []
                    });
                }

                const endTime = performance.now();
                const processingTime = endTime - startTime;

                addTestResult('performanceTests', 'التعامل مع البيانات الكبيرة',
                    processingTime < 50 ? 'passed' : processingTime < 200 ? 'warning' : 'failed',
                    `معالجة 1000 موظف في ${processingTime.toFixed(2)} مللي ثانية`);
            } catch (error) {
                addTestResult('performanceTests', 'التعامل مع البيانات الكبيرة', 'failed', error.message);
            }

            // اختبار استقرار النظام
            try {
                let errorCount = 0;
                const testOperations = 50;

                for (let i = 0; i < testOperations; i++) {
                    try {
                        // محاكاة عمليات متكررة
                        if (typeof getLeaveStatus === 'function' && window.employees.length > 0) {
                            getLeaveStatus(window.employees[0]);
                        }
                    } catch (error) {
                        errorCount++;
                    }
                }

                const successRate = ((testOperations - errorCount) / testOperations) * 100;

                addTestResult('performanceTests', 'استقرار النظام',
                    successRate === 100 ? 'passed' : successRate > 90 ? 'warning' : 'failed',
                    `معدل النجاح: ${successRate.toFixed(1)}% (${testOperations - errorCount}/${testOperations})`);
            } catch (error) {
                addTestResult('performanceTests', 'استقرار النظام', 'failed', error.message);
            }

            log('اكتمل اختبار الأداء والاستقرار', 'success');
        }

        // 7. اختبار التوافق
        async function runCompatibilityTests() {
            log('بدء اختبار التوافق...', 'info');

            // اختبار المتصفح
            try {
                const userAgent = navigator.userAgent;
                let browserName = 'غير معروف';

                if (userAgent.includes('Chrome')) browserName = 'Chrome';
                else if (userAgent.includes('Firefox')) browserName = 'Firefox';
                else if (userAgent.includes('Safari')) browserName = 'Safari';
                else if (userAgent.includes('Edge')) browserName = 'Edge';

                addTestResult('compatibilityTests', 'نوع المتصفح', 'passed', `المتصفح: ${browserName}`);
            } catch (error) {
                addTestResult('compatibilityTests', 'نوع المتصفح', 'failed', error.message);
            }

            // اختبار دعم localStorage
            try {
                const isSupported = typeof Storage !== 'undefined';
                addTestResult('compatibilityTests', 'دعم localStorage',
                    isSupported ? 'passed' : 'failed',
                    isSupported ? 'localStorage مدعوم' : 'localStorage غير مدعوم');
            } catch (error) {
                addTestResult('compatibilityTests', 'دعم localStorage', 'failed', error.message);
            }

            // اختبار دعم ES6
            try {
                const supportsES6 = typeof Symbol !== 'undefined' && typeof Promise !== 'undefined';
                addTestResult('compatibilityTests', 'دعم ES6',
                    supportsES6 ? 'passed' : 'failed',
                    supportsES6 ? 'ES6 مدعوم' : 'ES6 غير مدعوم');
            } catch (error) {
                addTestResult('compatibilityTests', 'دعم ES6', 'failed', error.message);
            }

            // اختبار دعم النصوص العربية
            try {
                const testText = 'اختبار النص العربي';
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                ctx.font = '16px Arial';
                const metrics = ctx.measureText(testText);

                addTestResult('compatibilityTests', 'دعم النصوص العربية',
                    metrics.width > 0 ? 'passed' : 'failed',
                    `عرض النص: ${metrics.width.toFixed(2)}px`);
            } catch (error) {
                addTestResult('compatibilityTests', 'دعم النصوص العربية', 'failed', error.message);
            }

            // اختبار دعم التقويم الميلادي
            try {
                const date = new Date();
                const arabicDate = date.toLocaleDateString('ar-SA');
                addTestResult('compatibilityTests', 'دعم التقويم الميلادي', 'passed', `التاريخ: ${arabicDate}`);
            } catch (error) {
                addTestResult('compatibilityTests', 'دعم التقويم الميلادي', 'failed', error.message);
            }

            log('اكتمل اختبار التوافق', 'success');
        }

        console.log('تم تحميل جميع وظائف الاختبار الشامل');
    </script>
</body>
</html>
