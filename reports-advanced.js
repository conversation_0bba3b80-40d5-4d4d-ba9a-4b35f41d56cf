/**
 * نظام إدارة إجازات الموظفين المتطور
 * ملف التقارير المتقدمة
 * يحتوي على وظائف تصدير PDF و Excel المتقدمة مع التصميم الاحترافي
 */

// ===== إعدادات التقارير =====

const REPORT_CONFIG = {
    company: {
        name: 'شركة التقنية المتقدمة',
        nameEn: 'Advanced Technology Company',
        logo: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiByeD0iMTAiIGZpbGw9IiMzNDk4ZGIiLz4KPHRleHQgeD0iNTAiIHk9IjU1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7YtNix2YPYqTwvdGV4dD4KPC9zdmc+',
        address: 'الرياض، المملكة العربية السعودية',
        phone: '+966 11 123 4567',
        email: '<EMAIL>'
    },
    colors: {
        primary: '#3498db',
        secondary: '#2c3e50',
        success: '#27ae60',
        warning: '#f39c12',
        danger: '#e74c3c',
        light: '#ecf0f1',
        dark: '#34495e'
    },
    fonts: {
        arabic: 'Arial, sans-serif',
        english: 'Arial, sans-serif'
    }
};

// ===== وظائف مساعدة للتقارير =====

/**
 * تحضير بيانات التقرير
 */
function prepareReportData(filterOptions = {}) {
    const employees = window.employees || [];
    let filteredData = [...employees];
    
    // تطبيق الفلاتر
    if (filterOptions.department && filterOptions.department !== 'all') {
        filteredData = filteredData.filter(emp => emp.department === filterOptions.department);
    }
    
    if (filterOptions.dateFrom) {
        filteredData = filteredData.filter(emp => 
            new Date(emp.hireDate) >= new Date(filterOptions.dateFrom)
        );
    }
    
    if (filterOptions.dateTo) {
        filteredData = filteredData.filter(emp => 
            new Date(emp.hireDate) <= new Date(filterOptions.dateTo)
        );
    }
    
    // حساب الإحصائيات
    const stats = {
        totalEmployees: filteredData.length,
        totalCarriedOver: filteredData.reduce((sum, emp) => sum + (emp.carriedOverLeave || 0), 0),
        totalUsedLeaves: filteredData.reduce((sum, emp) => 
            sum + (emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0), 0),
        totalRemainingLeaves: filteredData.reduce((sum, emp) => 
            sum + ((emp.annualLeave + emp.carriedOverLeave) - emp.usedAnnual) + 
            (emp.sickLeave - emp.usedSick) + (emp.emergencyLeave - emp.usedEmergency), 0),
        departmentStats: {}
    };
    
    // إحصائيات الأقسام
    filteredData.forEach(emp => {
        if (!stats.departmentStats[emp.department]) {
            stats.departmentStats[emp.department] = {
                count: 0,
                totalCarriedOver: 0,
                totalUsed: 0,
                totalRemaining: 0
            };
        }
        
        const dept = stats.departmentStats[emp.department];
        dept.count++;
        dept.totalCarriedOver += emp.carriedOverLeave || 0;
        dept.totalUsed += (emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0);
        dept.totalRemaining += ((emp.annualLeave + emp.carriedOverLeave) - emp.usedAnnual) + 
                              (emp.sickLeave - emp.usedSick) + (emp.emergencyLeave - emp.usedEmergency);
    });
    
    return {
        employees: filteredData,
        stats: stats,
        generatedAt: new Date().toLocaleString('ar-SA'),
        filterOptions: filterOptions
    };
}

/**
 * تنسيق التاريخ للعرض
 */
function formatDateArabic(dateString) {
    if (!dateString) return 'غير محدد';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

/**
 * حساب سنوات الخدمة
 */
function calculateServiceYears(hireDate) {
    if (!hireDate) return 0;
    const today = new Date();
    const hire = new Date(hireDate);
    const years = (today - hire) / (365.25 * 24 * 60 * 60 * 1000);
    return Math.max(0, Math.floor(years));
}

/**
 * تحديد حالة الرصيد
 */
function getLeaveStatus(employee) {
    const totalRemaining = ((employee.annualLeave + employee.carriedOverLeave) - employee.usedAnnual) + 
                          (employee.sickLeave - employee.usedSick) + 
                          (employee.emergencyLeave - employee.usedEmergency);
    
    if (totalRemaining <= 0) return { status: 'نفد الرصيد', color: REPORT_CONFIG.colors.danger };
    if (totalRemaining <= 5) return { status: 'رصيد منخفض', color: REPORT_CONFIG.colors.warning };
    return { status: 'رصيد طبيعي', color: REPORT_CONFIG.colors.success };
}

// ===== وظائف تصدير PDF =====

/**
 * تحميل مكتبة jsPDF
 */
async function loadJsPDF() {
    if (window.jsPDF) return window.jsPDF;
    
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
        script.onload = () => {
            // تحميل خط عربي
            const fontScript = document.createElement('script');
            fontScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.plugin.autotable.min.js';
            fontScript.onload = () => resolve(window.jsPDF);
            fontScript.onerror = reject;
            document.head.appendChild(fontScript);
        };
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

/**
 * إنشاء تقرير PDF شامل
 */
async function generateComprehensiveReport(filterOptions = {}) {
    try {
        showNotification('🔄 جاري إنشاء التقرير الشامل...', 'info', 3000);
        
        const jsPDF = await loadJsPDF();
        const doc = new jsPDF.jsPDF('p', 'mm', 'a4');
        
        const reportData = prepareReportData(filterOptions);
        
        // إعداد الخط العربي
        doc.setFont('helvetica');
        doc.setFontSize(16);
        
        // رأس التقرير
        addReportHeader(doc, 'تقرير شامل لإجازات الموظفين');
        
        let yPosition = 60;
        
        // معلومات التقرير
        doc.setFontSize(12);
        doc.text(`تاريخ الإنشاء: ${reportData.generatedAt}`, 20, yPosition);
        yPosition += 10;
        doc.text(`عدد الموظفين: ${reportData.stats.totalEmployees}`, 20, yPosition);
        yPosition += 10;
        doc.text(`إجمالي الرصيد المرحل: ${reportData.stats.totalCarriedOver} يوم`, 20, yPosition);
        yPosition += 10;
        doc.text(`إجمالي الإجازات المستخدمة: ${reportData.stats.totalUsedLeaves} يوم`, 20, yPosition);
        yPosition += 20;
        
        // جدول الموظفين
        const tableData = reportData.employees.map(emp => {
            const status = getLeaveStatus(emp);
            return [
                emp.name,
                emp.id,
                emp.department,
                formatDateArabic(emp.hireDate),
                calculateServiceYears(emp.hireDate).toString(),
                (emp.carriedOverLeave || 0).toString(),
                ((emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0)).toString(),
                status.status
            ];
        });
        
        doc.autoTable({
            head: [['اسم الموظف', 'الرقم الوظيفي', 'القسم', 'تاريخ التوظيف', 'سنوات الخدمة', 'الرصيد المرحل', 'المستخدم', 'الحالة']],
            body: tableData,
            startY: yPosition,
            styles: {
                font: 'helvetica',
                fontSize: 10,
                cellPadding: 3,
                textColor: [0, 0, 0],
                fillColor: [255, 255, 255]
            },
            headStyles: {
                fillColor: [52, 152, 219],
                textColor: [255, 255, 255],
                fontStyle: 'bold'
            },
            alternateRowStyles: {
                fillColor: [236, 240, 241]
            },
            margin: { top: 20, right: 20, bottom: 20, left: 20 }
        });
        
        // إضافة صفحة للإحصائيات
        doc.addPage();
        addReportHeader(doc, 'إحصائيات الأقسام');
        
        yPosition = 60;
        
        // جدول إحصائيات الأقسام
        const deptData = Object.entries(reportData.stats.departmentStats).map(([dept, stats]) => [
            dept,
            stats.count.toString(),
            stats.totalCarriedOver.toString(),
            stats.totalUsed.toString(),
            stats.totalRemaining.toString()
        ]);
        
        doc.autoTable({
            head: [['القسم', 'عدد الموظفين', 'الرصيد المرحل', 'المستخدم', 'المتبقي']],
            body: deptData,
            startY: yPosition,
            styles: {
                font: 'helvetica',
                fontSize: 12,
                cellPadding: 5
            },
            headStyles: {
                fillColor: [46, 62, 80],
                textColor: [255, 255, 255],
                fontStyle: 'bold'
            }
        });
        
        // حفظ الملف
        const fileName = `تقرير_شامل_${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);
        
        showNotification('✅ تم إنشاء التقرير الشامل بنجاح!', 'success', 5000);
        
    } catch (error) {
        console.error('خطأ في إنشاء التقرير:', error);
        showNotification('❌ حدث خطأ في إنشاء التقرير', 'danger', 5000);
    }
}

/**
 * إضافة رأس التقرير
 */
function addReportHeader(doc, title) {
    // شعار الشركة (مبسط)
    doc.setFillColor(52, 152, 219);
    doc.rect(20, 10, 170, 30, 'F');
    
    // اسم الشركة
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(18);
    doc.text(REPORT_CONFIG.company.name, 105, 20, { align: 'center' });
    
    doc.setFontSize(12);
    doc.text(REPORT_CONFIG.company.nameEn, 105, 28, { align: 'center' });
    
    // عنوان التقرير
    doc.setTextColor(0, 0, 0);
    doc.setFontSize(16);
    doc.text(title, 105, 50, { align: 'center' });
    
    // خط فاصل
    doc.setDrawColor(52, 152, 219);
    doc.setLineWidth(0.5);
    doc.line(20, 55, 190, 55);
}

/**
 * إنشاء تقرير فردي لموظف
 */
async function generateEmployeeReport(employeeIndex) {
    try {
        if (employeeIndex < 0 || employeeIndex >= window.employees.length) {
            showNotification('❌ موظف غير صالح', 'danger', 3000);
            return;
        }

        const employee = window.employees[employeeIndex];
        showNotification(`🔄 جاري إنشاء تقرير للموظف: ${employee.name}...`, 'info', 3000);

        const jsPDF = await loadJsPDF();
        const doc = new jsPDF.jsPDF('p', 'mm', 'a4');

        // رأس التقرير
        addReportHeader(doc, `تقرير إجازات الموظف: ${employee.name}`);

        let yPosition = 70;

        // معلومات الموظف الأساسية
        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.text('المعلومات الأساسية:', 20, yPosition);
        yPosition += 15;

        doc.setFont('helvetica', 'normal');
        doc.setFontSize(12);

        const basicInfo = [
            ['اسم الموظف:', employee.name],
            ['الرقم الوظيفي:', employee.id],
            ['القسم:', employee.department],
            ['تاريخ التوظيف:', formatDateArabic(employee.hireDate)],
            ['سنوات الخدمة:', `${calculateServiceYears(employee.hireDate)} سنة`]
        ];

        basicInfo.forEach(([label, value]) => {
            doc.text(label, 20, yPosition);
            doc.text(value, 80, yPosition);
            yPosition += 8;
        });

        yPosition += 10;

        // رصيد الإجازات
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(14);
        doc.text('رصيد الإجازات:', 20, yPosition);
        yPosition += 15;

        // جدول رصيد الإجازات
        const leaveBalance = [
            ['نوع الإجازة', 'المخصص', 'المرحل', 'المستخدم', 'المتبقي'],
            [
                'إجازة سنوية',
                employee.annualLeave.toString(),
                (employee.carriedOverLeave || 0).toString(),
                (employee.usedAnnual || 0).toString(),
                ((employee.annualLeave + (employee.carriedOverLeave || 0)) - (employee.usedAnnual || 0)).toString()
            ],
            [
                'إجازة مرضية',
                employee.sickLeave.toString(),
                '0',
                (employee.usedSick || 0).toString(),
                (employee.sickLeave - (employee.usedSick || 0)).toString()
            ],
            [
                'إجازة طارئة',
                employee.emergencyLeave.toString(),
                '0',
                (employee.usedEmergency || 0).toString(),
                (employee.emergencyLeave - (employee.usedEmergency || 0)).toString()
            ]
        ];

        doc.autoTable({
            body: leaveBalance,
            startY: yPosition,
            styles: {
                font: 'helvetica',
                fontSize: 11,
                cellPadding: 4
            },
            headStyles: {
                fillColor: [52, 152, 219],
                textColor: [255, 255, 255],
                fontStyle: 'bold'
            },
            alternateRowStyles: {
                fillColor: [236, 240, 241]
            }
        });

        yPosition = doc.lastAutoTable.finalY + 20;

        // سجل الإجازات
        if (employee.leaveHistory && employee.leaveHistory.length > 0) {
            doc.setFont('helvetica', 'bold');
            doc.setFontSize(14);
            doc.text('سجل الإجازات:', 20, yPosition);
            yPosition += 15;

            const historyData = employee.leaveHistory.map(leave => [
                leave.typeName || leave.type,
                leave.days.toString(),
                formatDateArabic(leave.startDate),
                formatDateArabic(leave.endDate),
                leave.reason || 'غير محدد'
            ]);

            doc.autoTable({
                head: [['نوع الإجازة', 'عدد الأيام', 'تاريخ البداية', 'تاريخ النهاية', 'السبب']],
                body: historyData,
                startY: yPosition,
                styles: {
                    font: 'helvetica',
                    fontSize: 10,
                    cellPadding: 3
                },
                headStyles: {
                    fillColor: [46, 62, 80],
                    textColor: [255, 255, 255],
                    fontStyle: 'bold'
                }
            });
        } else {
            doc.setFont('helvetica', 'normal');
            doc.setFontSize(12);
            doc.text('لا توجد إجازات مسجلة لهذا الموظف', 20, yPosition);
        }

        // تذييل التقرير
        addReportFooter(doc);

        // حفظ الملف
        const fileName = `تقرير_${employee.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);

        showNotification(`✅ تم إنشاء تقرير الموظف ${employee.name} بنجاح!`, 'success', 5000);

    } catch (error) {
        console.error('خطأ في إنشاء تقرير الموظف:', error);
        showNotification('❌ حدث خطأ في إنشاء تقرير الموظف', 'danger', 5000);
    }
}

/**
 * إنشاء تقرير حسب الأقسام
 */
async function generateDepartmentReport(department = 'all') {
    try {
        showNotification('🔄 جاري إنشاء تقرير الأقسام...', 'info', 3000);

        const jsPDF = await loadJsPDF();
        const doc = new jsPDF.jsPDF('p', 'mm', 'a4');

        const filterOptions = department !== 'all' ? { department } : {};
        const reportData = prepareReportData(filterOptions);

        const title = department !== 'all' ? `تقرير قسم: ${department}` : 'تقرير جميع الأقسام';
        addReportHeader(doc, title);

        let yPosition = 70;

        // إحصائيات عامة
        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.text('الإحصائيات العامة:', 20, yPosition);
        yPosition += 15;

        doc.setFont('helvetica', 'normal');
        doc.setFontSize(12);

        const generalStats = [
            ['إجمالي الموظفين:', reportData.stats.totalEmployees.toString()],
            ['إجمالي الرصيد المرحل:', `${reportData.stats.totalCarriedOver} يوم`],
            ['إجمالي الإجازات المستخدمة:', `${reportData.stats.totalUsedLeaves} يوم`],
            ['إجمالي الرصيد المتبقي:', `${reportData.stats.totalRemainingLeaves} يوم`]
        ];

        generalStats.forEach(([label, value]) => {
            doc.text(label, 20, yPosition);
            doc.text(value, 100, yPosition);
            yPosition += 8;
        });

        yPosition += 15;

        // جدول تفصيلي للأقسام
        if (department === 'all') {
            doc.setFont('helvetica', 'bold');
            doc.setFontSize(14);
            doc.text('تفصيل الأقسام:', 20, yPosition);
            yPosition += 15;

            const deptTableData = Object.entries(reportData.stats.departmentStats).map(([dept, stats]) => [
                dept,
                stats.count.toString(),
                stats.totalCarriedOver.toString(),
                stats.totalUsed.toString(),
                stats.totalRemaining.toString(),
                Math.round((stats.totalUsed / (stats.totalUsed + stats.totalRemaining)) * 100) + '%'
            ]);

            doc.autoTable({
                head: [['القسم', 'الموظفين', 'المرحل', 'المستخدم', 'المتبقي', 'نسبة الاستخدام']],
                body: deptTableData,
                startY: yPosition,
                styles: {
                    font: 'helvetica',
                    fontSize: 11,
                    cellPadding: 4
                },
                headStyles: {
                    fillColor: [52, 152, 219],
                    textColor: [255, 255, 255],
                    fontStyle: 'bold'
                }
            });

            yPosition = doc.lastAutoTable.finalY + 20;
        }

        // إضافة صفحة جديدة للموظفين
        if (yPosition > 200) {
            doc.addPage();
            yPosition = 30;
        }

        // قائمة الموظفين
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(14);
        doc.text('قائمة الموظفين:', 20, yPosition);
        yPosition += 15;

        const employeeTableData = reportData.employees.map(emp => {
            const status = getLeaveStatus(emp);
            return [
                emp.name,
                emp.id,
                emp.department,
                (emp.carriedOverLeave || 0).toString(),
                ((emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0)).toString(),
                status.status
            ];
        });

        doc.autoTable({
            head: [['اسم الموظف', 'الرقم الوظيفي', 'القسم', 'المرحل', 'المستخدم', 'الحالة']],
            body: employeeTableData,
            startY: yPosition,
            styles: {
                font: 'helvetica',
                fontSize: 10,
                cellPadding: 3
            },
            headStyles: {
                fillColor: [46, 62, 80],
                textColor: [255, 255, 255],
                fontStyle: 'bold'
            },
            alternateRowStyles: {
                fillColor: [236, 240, 241]
            }
        });

        // تذييل التقرير
        addReportFooter(doc);

        // حفظ الملف
        const fileName = department !== 'all'
            ? `تقرير_قسم_${department.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`
            : `تقرير_جميع_الأقسام_${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);

        showNotification('✅ تم إنشاء تقرير الأقسام بنجاح!', 'success', 5000);

    } catch (error) {
        console.error('خطأ في إنشاء تقرير الأقسام:', error);
        showNotification('❌ حدث خطأ في إنشاء تقرير الأقسام', 'danger', 5000);
    }
}

/**
 * إضافة تذييل التقرير
 */
function addReportFooter(doc) {
    const pageHeight = doc.internal.pageSize.height;
    const pageWidth = doc.internal.pageSize.width;

    // خط فاصل
    doc.setDrawColor(52, 152, 219);
    doc.setLineWidth(0.5);
    doc.line(20, pageHeight - 30, pageWidth - 20, pageHeight - 30);

    // معلومات الشركة
    doc.setFontSize(10);
    doc.setTextColor(100, 100, 100);
    doc.text(REPORT_CONFIG.company.address, 20, pageHeight - 20);
    doc.text(`${REPORT_CONFIG.company.phone} | ${REPORT_CONFIG.company.email}`, 20, pageHeight - 15);

    // تاريخ الإنشاء
    doc.text(`تم الإنشاء في: ${new Date().toLocaleString('ar-SA')}`, pageWidth - 20, pageHeight - 20, { align: 'right' });

    // رقم الصفحة
    const pageNumber = doc.internal.getCurrentPageInfo().pageNumber;
    doc.text(`صفحة ${pageNumber}`, pageWidth - 20, pageHeight - 15, { align: 'right' });
}

// ===== وظائف تصدير Excel المتقدمة =====

/**
 * تحميل مكتبة SheetJS
 */
async function loadSheetJS() {
    if (window.XLSX) return window.XLSX;

    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
        script.onload = () => resolve(window.XLSX);
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

/**
 * إنشاء ملف Excel متقدم متعدد الأوراق
 */
async function generateAdvancedExcelReport(filterOptions = {}) {
    try {
        showNotification('🔄 جاري إنشاء تقرير Excel المتقدم...', 'info', 3000);

        const XLSX = await loadSheetJS();
        const reportData = prepareReportData(filterOptions);

        // إنشاء مصنف جديد
        const workbook = XLSX.utils.book_new();

        // ورقة الملخص العام
        createSummarySheet(workbook, XLSX, reportData);

        // ورقة لكل قسم
        const departments = Object.keys(reportData.stats.departmentStats);
        departments.forEach(dept => {
            createDepartmentSheet(workbook, XLSX, reportData, dept);
        });

        // ورقة تفصيلية لجميع الموظفين
        createDetailedEmployeeSheet(workbook, XLSX, reportData);

        // ورقة الإحصائيات والرسوم البيانية
        createStatisticsSheet(workbook, XLSX, reportData);

        // حفظ الملف
        const fileName = `تقرير_Excel_متقدم_${new Date().toISOString().split('T')[0]}.xlsx`;
        XLSX.writeFile(workbook, fileName);

        showNotification('✅ تم إنشاء تقرير Excel المتقدم بنجاح!', 'success', 5000);

    } catch (error) {
        console.error('خطأ في إنشاء تقرير Excel:', error);
        showNotification('❌ حدث خطأ في إنشاء تقرير Excel', 'danger', 5000);
    }
}

/**
 * إنشاء ورقة الملخص العام
 */
function createSummarySheet(workbook, XLSX, reportData) {
    const ws_data = [
        ['تقرير إجازات الموظفين - الملخص العام'],
        [''],
        ['تاريخ الإنشاء:', reportData.generatedAt],
        [''],
        ['الإحصائيات العامة:'],
        ['إجمالي الموظفين', reportData.stats.totalEmployees],
        ['إجمالي الرصيد المرحل', reportData.stats.totalCarriedOver],
        ['إجمالي الإجازات المستخدمة', reportData.stats.totalUsedLeaves],
        ['إجمالي الرصيد المتبقي', reportData.stats.totalRemainingLeaves],
        [''],
        ['إحصائيات الأقسام:'],
        ['القسم', 'عدد الموظفين', 'الرصيد المرحل', 'المستخدم', 'المتبقي', 'نسبة الاستخدام']
    ];

    // إضافة بيانات الأقسام
    Object.entries(reportData.stats.departmentStats).forEach(([dept, stats]) => {
        const usageRate = stats.totalUsed + stats.totalRemaining > 0
            ? Math.round((stats.totalUsed / (stats.totalUsed + stats.totalRemaining)) * 100)
            : 0;
        ws_data.push([
            dept,
            stats.count,
            stats.totalCarriedOver,
            stats.totalUsed,
            stats.totalRemaining,
            usageRate + '%'
        ]);
    });

    const ws = XLSX.utils.aoa_to_sheet(ws_data);

    // تنسيق الخلايا
    const range = XLSX.utils.decode_range(ws['!ref']);

    // تنسيق العنوان الرئيسي
    ws['A1'].s = {
        font: { bold: true, sz: 16, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "3498DB" } },
        alignment: { horizontal: "center" }
    };

    // دمج خلايا العنوان
    ws['!merges'] = [{ s: { r: 0, c: 0 }, e: { r: 0, c: 5 } }];

    // تنسيق رؤوس الأقسام
    for (let i = 4; i <= 10; i++) {
        if (ws[`A${i + 1}`]) {
            ws[`A${i + 1}`].s = {
                font: { bold: true },
                fill: { fgColor: { rgb: "ECF0F1" } }
            };
        }
    }

    // تنسيق رأس جدول الأقسام
    for (let c = 0; c < 6; c++) {
        const cellRef = XLSX.utils.encode_cell({ r: 11, c: c });
        if (ws[cellRef]) {
            ws[cellRef].s = {
                font: { bold: true, color: { rgb: "FFFFFF" } },
                fill: { fgColor: { rgb: "2C3E50" } },
                alignment: { horizontal: "center" }
            };
        }
    }

    // تعيين عرض الأعمدة
    ws['!cols'] = [
        { wch: 25 }, { wch: 15 }, { wch: 15 }, { wch: 15 }, { wch: 15 }, { wch: 15 }
    ];

    XLSX.utils.book_append_sheet(workbook, ws, 'الملخص العام');
}

/**
 * إنشاء ورقة لقسم محدد
 */
function createDepartmentSheet(workbook, XLSX, reportData, department) {
    const deptEmployees = reportData.employees.filter(emp => emp.department === department);

    const ws_data = [
        [`تقرير قسم: ${department}`],
        [''],
        ['إحصائيات القسم:'],
        ['عدد الموظفين', deptEmployees.length],
        ['الرصيد المرحل الإجمالي', deptEmployees.reduce((sum, emp) => sum + (emp.carriedOverLeave || 0), 0)],
        ['الإجازات المستخدمة', deptEmployees.reduce((sum, emp) => sum + (emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0), 0)],
        [''],
        ['قائمة الموظفين:'],
        ['اسم الموظف', 'الرقم الوظيفي', 'تاريخ التوظيف', 'سنوات الخدمة', 'الرصيد المرحل', 'السنوي المخصص', 'السنوي المستخدم', 'السنوي المتبقي', 'المرضي المستخدم', 'المرضي المتبقي', 'الطارئ المستخدم', 'الطارئ المتبقي', 'الحالة']
    ];

    // إضافة بيانات الموظفين
    deptEmployees.forEach(emp => {
        const status = getLeaveStatus(emp);
        ws_data.push([
            emp.name,
            emp.id,
            formatDateArabic(emp.hireDate),
            calculateServiceYears(emp.hireDate),
            emp.carriedOverLeave || 0,
            emp.annualLeave,
            emp.usedAnnual || 0,
            (emp.annualLeave + (emp.carriedOverLeave || 0)) - (emp.usedAnnual || 0),
            emp.usedSick || 0,
            emp.sickLeave - (emp.usedSick || 0),
            emp.usedEmergency || 0,
            emp.emergencyLeave - (emp.usedEmergency || 0),
            status.status
        ]);
    });

    const ws = XLSX.utils.aoa_to_sheet(ws_data);

    // تنسيق الورقة
    formatWorksheet(ws, ws_data.length);

    // تعيين عرض الأعمدة
    ws['!cols'] = [
        { wch: 20 }, { wch: 12 }, { wch: 15 }, { wch: 10 }, { wch: 10 },
        { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 12 },
        { wch: 12 }, { wch: 12 }, { wch: 15 }
    ];

    XLSX.utils.book_append_sheet(workbook, ws, department);
}

/**
 * إنشاء ورقة تفصيلية لجميع الموظفين
 */
function createDetailedEmployeeSheet(workbook, XLSX, reportData) {
    const ws_data = [
        ['قائمة شاملة لجميع الموظفين'],
        [''],
        ['اسم الموظف', 'الرقم الوظيفي', 'القسم', 'تاريخ التوظيف', 'سنوات الخدمة', 'الرصيد المرحل', 'السنوي (مخصص/مستخدم/متبقي)', 'المرضي (مخصص/مستخدم/متبقي)', 'الطارئ (مخصص/مستخدم/متبقي)', 'إجمالي المتبقي', 'الحالة', 'عدد الإجازات المسجلة']
    ];

    // إضافة بيانات جميع الموظفين
    reportData.employees.forEach(emp => {
        const status = getLeaveStatus(emp);
        const annualInfo = `${emp.annualLeave}/${emp.usedAnnual || 0}/${(emp.annualLeave + (emp.carriedOverLeave || 0)) - (emp.usedAnnual || 0)}`;
        const sickInfo = `${emp.sickLeave}/${emp.usedSick || 0}/${emp.sickLeave - (emp.usedSick || 0)}`;
        const emergencyInfo = `${emp.emergencyLeave}/${emp.usedEmergency || 0}/${emp.emergencyLeave - (emp.usedEmergency || 0)}`;
        const totalRemaining = ((emp.annualLeave + (emp.carriedOverLeave || 0)) - (emp.usedAnnual || 0)) +
                              (emp.sickLeave - (emp.usedSick || 0)) +
                              (emp.emergencyLeave - (emp.usedEmergency || 0));

        ws_data.push([
            emp.name,
            emp.id,
            emp.department,
            formatDateArabic(emp.hireDate),
            calculateServiceYears(emp.hireDate),
            emp.carriedOverLeave || 0,
            annualInfo,
            sickInfo,
            emergencyInfo,
            totalRemaining,
            status.status,
            (emp.leaveHistory || []).length
        ]);
    });

    const ws = XLSX.utils.aoa_to_sheet(ws_data);

    // تنسيق الورقة
    formatWorksheet(ws, ws_data.length);

    // تعيين عرض الأعمدة
    ws['!cols'] = [
        { wch: 20 }, { wch: 12 }, { wch: 15 }, { wch: 15 }, { wch: 10 },
        { wch: 12 }, { wch: 20 }, { wch: 20 }, { wch: 20 }, { wch: 12 },
        { wch: 15 }, { wch: 15 }
    ];

    XLSX.utils.book_append_sheet(workbook, ws, 'جميع الموظفين');
}

/**
 * إنشاء ورقة الإحصائيات
 */
function createStatisticsSheet(workbook, XLSX, reportData) {
    const ws_data = [
        ['إحصائيات وتحليلات متقدمة'],
        [''],
        ['التحليل حسب سنوات الخدمة:'],
        ['سنوات الخدمة', 'عدد الموظفين', 'متوسط الرصيد المرحل', 'متوسط الاستخدام']
    ];

    // تجميع البيانات حسب سنوات الخدمة
    const serviceYearsGroups = {};
    reportData.employees.forEach(emp => {
        const years = calculateServiceYears(emp.hireDate);
        const group = years < 2 ? 'أقل من سنتين' :
                     years < 5 ? '2-5 سنوات' :
                     years < 10 ? '5-10 سنوات' : 'أكثر من 10 سنوات';

        if (!serviceYearsGroups[group]) {
            serviceYearsGroups[group] = {
                count: 0,
                totalCarriedOver: 0,
                totalUsed: 0
            };
        }

        serviceYearsGroups[group].count++;
        serviceYearsGroups[group].totalCarriedOver += emp.carriedOverLeave || 0;
        serviceYearsGroups[group].totalUsed += (emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0);
    });

    // إضافة بيانات التحليل
    Object.entries(serviceYearsGroups).forEach(([group, stats]) => {
        ws_data.push([
            group,
            stats.count,
            Math.round(stats.totalCarriedOver / stats.count),
            Math.round(stats.totalUsed / stats.count)
        ]);
    });

    ws_data.push(['']);
    ws_data.push(['التحليل حسب حالة الرصيد:']);
    ws_data.push(['الحالة', 'عدد الموظفين', 'النسبة المئوية']);

    // تحليل حالات الرصيد
    const statusGroups = { 'رصيد طبيعي': 0, 'رصيد منخفض': 0, 'نفد الرصيد': 0 };
    reportData.employees.forEach(emp => {
        const status = getLeaveStatus(emp);
        statusGroups[status.status]++;
    });

    Object.entries(statusGroups).forEach(([status, count]) => {
        const percentage = Math.round((count / reportData.employees.length) * 100);
        ws_data.push([status, count, percentage + '%']);
    });

    const ws = XLSX.utils.aoa_to_sheet(ws_data);

    // تنسيق الورقة
    formatWorksheet(ws, ws_data.length);

    XLSX.utils.book_append_sheet(workbook, ws, 'الإحصائيات');
}

/**
 * تنسيق ورقة العمل
 */
function formatWorksheet(ws, rowCount) {
    if (!ws['!ref']) return;

    const range = XLSX.utils.decode_range(ws['!ref']);

    // تنسيق العنوان الرئيسي
    if (ws['A1']) {
        ws['A1'].s = {
            font: { bold: true, sz: 14, color: { rgb: "FFFFFF" } },
            fill: { fgColor: { rgb: "3498DB" } },
            alignment: { horizontal: "center" }
        };
    }

    // تنسيق رؤوس الجداول
    for (let r = 0; r <= range.e.r; r++) {
        for (let c = 0; c <= range.e.c; c++) {
            const cellRef = XLSX.utils.encode_cell({ r, c });
            if (ws[cellRef] && typeof ws[cellRef].v === 'string' &&
                (ws[cellRef].v.includes(':') || ws[cellRef].v.includes('اسم الموظف'))) {
                ws[cellRef].s = {
                    font: { bold: true },
                    fill: { fgColor: { rgb: "ECF0F1" } },
                    border: {
                        top: { style: "thin" },
                        bottom: { style: "thin" },
                        left: { style: "thin" },
                        right: { style: "thin" }
                    }
                };
            }
        }
    }

    // إضافة حدود للجدول
    for (let r = 0; r <= range.e.r; r++) {
        for (let c = 0; c <= range.e.c; c++) {
            const cellRef = XLSX.utils.encode_cell({ r, c });
            if (ws[cellRef]) {
                if (!ws[cellRef].s) ws[cellRef].s = {};
                ws[cellRef].s.border = {
                    top: { style: "thin" },
                    bottom: { style: "thin" },
                    left: { style: "thin" },
                    right: { style: "thin" }
                };
            }
        }
    }
}

// ===== واجهة المستخدم للتقارير المتقدمة =====

/**
 * عرض نافذة خيارات التقرير المتقدم
 */
function showAdvancedReportDialog() {
    const dialogHTML = `
        <div class="modal-overlay" id="advancedReportModal" onclick="closeAdvancedReportDialog()">
            <div class="modal-content advanced-report-modal" onclick="event.stopPropagation()">
                <div class="modal-header">
                    <h3>📊 التقارير المتقدمة</h3>
                    <button class="close-btn" onclick="closeAdvancedReportDialog()">×</button>
                </div>

                <div class="modal-body">
                    <div class="report-options">
                        <div class="option-group">
                            <h4>🎯 نوع التقرير:</h4>
                            <div class="radio-group">
                                <label><input type="radio" name="reportType" value="comprehensive" checked> تقرير شامل</label>
                                <label><input type="radio" name="reportType" value="department"> تقرير حسب الأقسام</label>
                                <label><input type="radio" name="reportType" value="employee"> تقرير موظف محدد</label>
                            </div>
                        </div>

                        <div class="option-group">
                            <h4>📄 تنسيق التصدير:</h4>
                            <div class="radio-group">
                                <label><input type="radio" name="exportFormat" value="pdf" checked> PDF احترافي</label>
                                <label><input type="radio" name="exportFormat" value="excel"> Excel متقدم</label>
                                <label><input type="radio" name="exportFormat" value="both"> كلاهما</label>
                            </div>
                        </div>

                        <div class="option-group" id="departmentGroup" style="display: none;">
                            <h4>🏢 اختيار القسم:</h4>
                            <select id="departmentSelect" class="form-control">
                                <option value="all">جميع الأقسام</option>
                            </select>
                        </div>

                        <div class="option-group" id="employeeGroup" style="display: none;">
                            <h4>👤 اختيار الموظف:</h4>
                            <select id="employeeSelect" class="form-control">
                                <option value="">اختر موظف...</option>
                            </select>
                        </div>

                        <div class="option-group">
                            <h4>📅 فترة التقرير:</h4>
                            <div class="date-range">
                                <label>من تاريخ:</label>
                                <input type="date" id="dateFrom" class="form-control">
                                <label>إلى تاريخ:</label>
                                <input type="date" id="dateTo" class="form-control">
                            </div>
                        </div>

                        <div class="option-group">
                            <h4>⚙️ خيارات إضافية:</h4>
                            <div class="checkbox-group">
                                <label><input type="checkbox" id="includeCharts" checked> تضمين الرسوم البيانية</label>
                                <label><input type="checkbox" id="includeHistory" checked> تضمين سجل الإجازات</label>
                                <label><input type="checkbox" id="includeStatistics" checked> تضمين الإحصائيات المتقدمة</label>
                                <label><input type="checkbox" id="showPreview"> معاينة قبل التصدير</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeAdvancedReportDialog()">إلغاء</button>
                    <button class="btn btn-primary" onclick="generateSelectedReport()">📊 إنشاء التقرير</button>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', dialogHTML);

    // تعبئة قوائم الأقسام والموظفين
    populateReportSelectors();

    // إضافة مستمعي الأحداث
    setupReportDialogEvents();
}

/**
 * إغلاق نافذة التقارير المتقدمة
 */
function closeAdvancedReportDialog() {
    const modal = document.getElementById('advancedReportModal');
    if (modal) {
        modal.remove();
    }
}

/**
 * تعبئة قوائم الاختيار
 */
function populateReportSelectors() {
    const employees = window.employees || [];

    // تعبئة قائمة الأقسام
    const departments = [...new Set(employees.map(emp => emp.department))];
    const departmentSelect = document.getElementById('departmentSelect');
    departments.forEach(dept => {
        const option = document.createElement('option');
        option.value = dept;
        option.textContent = dept;
        departmentSelect.appendChild(option);
    });

    // تعبئة قائمة الموظفين
    const employeeSelect = document.getElementById('employeeSelect');
    employees.forEach((emp, index) => {
        const option = document.createElement('option');
        option.value = index;
        option.textContent = `${emp.name} (${emp.id})`;
        employeeSelect.appendChild(option);
    });
}

/**
 * إعداد أحداث نافذة التقارير
 */
function setupReportDialogEvents() {
    // تغيير نوع التقرير
    document.querySelectorAll('input[name="reportType"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const departmentGroup = document.getElementById('departmentGroup');
            const employeeGroup = document.getElementById('employeeGroup');

            departmentGroup.style.display = this.value === 'department' ? 'block' : 'none';
            employeeGroup.style.display = this.value === 'employee' ? 'block' : 'none';
        });
    });
}

/**
 * إنشاء التقرير المحدد
 */
async function generateSelectedReport() {
    const reportType = document.querySelector('input[name="reportType"]:checked').value;
    const exportFormat = document.querySelector('input[name="exportFormat"]:checked').value;
    const showPreview = document.getElementById('showPreview').checked;

    // جمع خيارات التقرير
    const options = {
        dateFrom: document.getElementById('dateFrom').value,
        dateTo: document.getElementById('dateTo').value,
        includeCharts: document.getElementById('includeCharts').checked,
        includeHistory: document.getElementById('includeHistory').checked,
        includeStatistics: document.getElementById('includeStatistics').checked
    };

    // إضافة خيارات خاصة حسب نوع التقرير
    if (reportType === 'department') {
        options.department = document.getElementById('departmentSelect').value;
    } else if (reportType === 'employee') {
        options.employeeIndex = parseInt(document.getElementById('employeeSelect').value);
        if (isNaN(options.employeeIndex)) {
            showNotification('❌ يرجى اختيار موظف', 'danger', 3000);
            return;
        }
    }

    // إغلاق النافذة
    closeAdvancedReportDialog();

    // معاينة التقرير إذا كان مطلوباً
    if (showPreview) {
        showReportPreview(reportType, options);
        return;
    }

    // إنشاء التقرير
    try {
        if (exportFormat === 'pdf' || exportFormat === 'both') {
            await generateReportByType(reportType, options, 'pdf');
        }

        if (exportFormat === 'excel' || exportFormat === 'both') {
            await generateReportByType(reportType, options, 'excel');
        }

        if (exportFormat === 'both') {
            showNotification('✅ تم إنشاء التقارير بكلا التنسيقين بنجاح!', 'success', 5000);
        }

    } catch (error) {
        console.error('خطأ في إنشاء التقرير:', error);
        showNotification('❌ حدث خطأ في إنشاء التقرير', 'danger', 5000);
    }
}

/**
 * إنشاء تقرير حسب النوع
 */
async function generateReportByType(reportType, options, format) {
    switch (reportType) {
        case 'comprehensive':
            if (format === 'pdf') {
                await generateComprehensiveReport(options);
            } else {
                await generateAdvancedExcelReport(options);
            }
            break;

        case 'department':
            if (format === 'pdf') {
                await generateDepartmentReport(options.department);
            } else {
                await generateAdvancedExcelReport({ department: options.department });
            }
            break;

        case 'employee':
            if (format === 'pdf') {
                await generateEmployeeReport(options.employeeIndex);
            } else {
                // إنشاء Excel خاص بموظف واحد
                await generateEmployeeExcelReport(options.employeeIndex);
            }
            break;
    }
}

/**
 * إنشاء تقرير Excel لموظف واحد
 */
async function generateEmployeeExcelReport(employeeIndex) {
    try {
        const employee = window.employees[employeeIndex];
        if (!employee) {
            showNotification('❌ موظف غير صالح', 'danger', 3000);
            return;
        }

        showNotification(`🔄 جاري إنشاء تقرير Excel للموظف: ${employee.name}...`, 'info', 3000);

        const XLSX = await loadSheetJS();
        const workbook = XLSX.utils.book_new();

        // ورقة معلومات الموظف
        const employeeData = [
            [`تقرير الموظف: ${employee.name}`],
            [''],
            ['المعلومات الأساسية:'],
            ['اسم الموظف', employee.name],
            ['الرقم الوظيفي', employee.id],
            ['القسم', employee.department],
            ['تاريخ التوظيف', formatDateArabic(employee.hireDate)],
            ['سنوات الخدمة', calculateServiceYears(employee.hireDate)],
            [''],
            ['رصيد الإجازات:'],
            ['نوع الإجازة', 'المخصص', 'المرحل', 'المستخدم', 'المتبقي'],
            [
                'إجازة سنوية',
                employee.annualLeave,
                employee.carriedOverLeave || 0,
                employee.usedAnnual || 0,
                (employee.annualLeave + (employee.carriedOverLeave || 0)) - (employee.usedAnnual || 0)
            ],
            [
                'إجازة مرضية',
                employee.sickLeave,
                0,
                employee.usedSick || 0,
                employee.sickLeave - (employee.usedSick || 0)
            ],
            [
                'إجازة طارئة',
                employee.emergencyLeave,
                0,
                employee.usedEmergency || 0,
                employee.emergencyLeave - (employee.usedEmergency || 0)
            ]
        ];

        const ws = XLSX.utils.aoa_to_sheet(employeeData);
        formatWorksheet(ws, employeeData.length);
        XLSX.utils.book_append_sheet(workbook, ws, 'معلومات الموظف');

        // ورقة سجل الإجازات إذا كان متاحاً
        if (employee.leaveHistory && employee.leaveHistory.length > 0) {
            const historyData = [
                ['سجل الإجازات'],
                [''],
                ['نوع الإجازة', 'عدد الأيام', 'تاريخ البداية', 'تاريخ النهاية', 'السبب', 'تاريخ التسجيل']
            ];

            employee.leaveHistory.forEach(leave => {
                historyData.push([
                    leave.typeName || leave.type,
                    leave.days,
                    formatDateArabic(leave.startDate),
                    formatDateArabic(leave.endDate),
                    leave.reason || 'غير محدد',
                    formatDateArabic(leave.recordDate)
                ]);
            });

            const historyWs = XLSX.utils.aoa_to_sheet(historyData);
            formatWorksheet(historyWs, historyData.length);
            XLSX.utils.book_append_sheet(workbook, historyWs, 'سجل الإجازات');
        }

        // حفظ الملف
        const fileName = `تقرير_Excel_${employee.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.xlsx`;
        XLSX.writeFile(workbook, fileName);

        showNotification(`✅ تم إنشاء تقرير Excel للموظف ${employee.name} بنجاح!`, 'success', 5000);

    } catch (error) {
        console.error('خطأ في إنشاء تقرير Excel للموظف:', error);
        showNotification('❌ حدث خطأ في إنشاء تقرير Excel للموظف', 'danger', 5000);
    }
}

/**
 * عرض معاينة التقرير
 */
function showReportPreview(reportType, options) {
    const reportData = prepareReportData(options);

    let previewContent = '';

    switch (reportType) {
        case 'comprehensive':
            previewContent = generateComprehensivePreview(reportData);
            break;
        case 'department':
            previewContent = generateDepartmentPreview(reportData, options.department);
            break;
        case 'employee':
            previewContent = generateEmployeePreview(window.employees[options.employeeIndex]);
            break;
    }

    const previewHTML = `
        <div class="modal-overlay" id="reportPreviewModal" onclick="closeReportPreview()">
            <div class="modal-content report-preview-modal" onclick="event.stopPropagation()">
                <div class="modal-header">
                    <h3>👁️ معاينة التقرير</h3>
                    <button class="close-btn" onclick="closeReportPreview()">×</button>
                </div>

                <div class="modal-body">
                    <div class="preview-content">
                        ${previewContent}
                    </div>
                </div>

                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeReportPreview()">إغلاق</button>
                    <button class="btn btn-primary" onclick="proceedWithReport('${reportType}', ${JSON.stringify(options).replace(/"/g, '&quot;')})">📄 متابعة التصدير</button>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', previewHTML);
}

/**
 * إغلاق معاينة التقرير
 */
function closeReportPreview() {
    const modal = document.getElementById('reportPreviewModal');
    if (modal) {
        modal.remove();
    }
}

/**
 * المتابعة مع إنشاء التقرير بعد المعاينة
 */
async function proceedWithReport(reportType, options) {
    closeReportPreview();

    const exportFormat = document.querySelector('input[name="exportFormat"]:checked')?.value || 'pdf';

    try {
        if (exportFormat === 'pdf' || exportFormat === 'both') {
            await generateReportByType(reportType, options, 'pdf');
        }

        if (exportFormat === 'excel' || exportFormat === 'both') {
            await generateReportByType(reportType, options, 'excel');
        }

    } catch (error) {
        console.error('خطأ في إنشاء التقرير:', error);
        showNotification('❌ حدث خطأ في إنشاء التقرير', 'danger', 5000);
    }
}

/**
 * إنشاء معاينة شاملة
 */
function generateComprehensivePreview(reportData) {
    return `
        <div class="preview-section">
            <h4>📊 الإحصائيات العامة</h4>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-label">إجمالي الموظفين:</span>
                    <span class="stat-value">${reportData.stats.totalEmployees}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">الرصيد المرحل:</span>
                    <span class="stat-value">${reportData.stats.totalCarriedOver} يوم</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">الإجازات المستخدمة:</span>
                    <span class="stat-value">${reportData.stats.totalUsedLeaves} يوم</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">الرصيد المتبقي:</span>
                    <span class="stat-value">${reportData.stats.totalRemainingLeaves} يوم</span>
                </div>
            </div>
        </div>

        <div class="preview-section">
            <h4>🏢 إحصائيات الأقسام</h4>
            <div class="departments-preview">
                ${Object.entries(reportData.stats.departmentStats).map(([dept, stats]) => `
                    <div class="dept-item">
                        <strong>${dept}:</strong> ${stats.count} موظف،
                        مرحل: ${stats.totalCarriedOver}،
                        مستخدم: ${stats.totalUsed}
                    </div>
                `).join('')}
            </div>
        </div>

        <div class="preview-section">
            <h4>👥 عينة من الموظفين (أول 5)</h4>
            <div class="employees-preview">
                ${reportData.employees.slice(0, 5).map(emp => {
                    const status = getLeaveStatus(emp);
                    return `
                        <div class="emp-item">
                            <strong>${emp.name}</strong> (${emp.department}) -
                            مرحل: ${emp.carriedOverLeave || 0}،
                            حالة: <span style="color: ${status.color}">${status.status}</span>
                        </div>
                    `;
                }).join('')}
                ${reportData.employees.length > 5 ? `<div class="more-items">... و ${reportData.employees.length - 5} موظف آخر</div>` : ''}
            </div>
        </div>
    `;
}

/**
 * إنشاء معاينة القسم
 */
function generateDepartmentPreview(reportData, department) {
    const deptEmployees = reportData.employees.filter(emp => emp.department === department);

    return `
        <div class="preview-section">
            <h4>🏢 تقرير قسم: ${department}</h4>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-label">عدد الموظفين:</span>
                    <span class="stat-value">${deptEmployees.length}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">الرصيد المرحل:</span>
                    <span class="stat-value">${deptEmployees.reduce((sum, emp) => sum + (emp.carriedOverLeave || 0), 0)} يوم</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">الإجازات المستخدمة:</span>
                    <span class="stat-value">${deptEmployees.reduce((sum, emp) => sum + (emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0), 0)} يوم</span>
                </div>
            </div>
        </div>

        <div class="preview-section">
            <h4>👥 قائمة الموظفين</h4>
            <div class="employees-preview">
                ${deptEmployees.map(emp => {
                    const status = getLeaveStatus(emp);
                    return `
                        <div class="emp-item">
                            <strong>${emp.name}</strong> (${emp.id}) -
                            مرحل: ${emp.carriedOverLeave || 0}،
                            حالة: <span style="color: ${status.color}">${status.status}</span>
                        </div>
                    `;
                }).join('')}
            </div>
        </div>
    `;
}

/**
 * إنشاء معاينة الموظف
 */
function generateEmployeePreview(employee) {
    const status = getLeaveStatus(employee);

    return `
        <div class="preview-section">
            <h4>👤 تقرير الموظف: ${employee.name}</h4>
            <div class="employee-info">
                <div class="info-row">
                    <span class="info-label">الرقم الوظيفي:</span>
                    <span class="info-value">${employee.id}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">القسم:</span>
                    <span class="info-value">${employee.department}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">تاريخ التوظيف:</span>
                    <span class="info-value">${formatDateArabic(employee.hireDate)}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">سنوات الخدمة:</span>
                    <span class="info-value">${calculateServiceYears(employee.hireDate)} سنة</span>
                </div>
                <div class="info-row">
                    <span class="info-label">الحالة:</span>
                    <span class="info-value" style="color: ${status.color}">${status.status}</span>
                </div>
            </div>
        </div>

        <div class="preview-section">
            <h4>📅 رصيد الإجازات</h4>
            <div class="leave-balance">
                <div class="balance-item">
                    <strong>إجازة سنوية:</strong>
                    ${employee.annualLeave} مخصص + ${employee.carriedOverLeave || 0} مرحل - ${employee.usedAnnual || 0} مستخدم =
                    <strong>${(employee.annualLeave + (employee.carriedOverLeave || 0)) - (employee.usedAnnual || 0)} متبقي</strong>
                </div>
                <div class="balance-item">
                    <strong>إجازة مرضية:</strong>
                    ${employee.sickLeave} مخصص - ${employee.usedSick || 0} مستخدم =
                    <strong>${employee.sickLeave - (employee.usedSick || 0)} متبقي</strong>
                </div>
                <div class="balance-item">
                    <strong>إجازة طارئة:</strong>
                    ${employee.emergencyLeave} مخصص - ${employee.usedEmergency || 0} مستخدم =
                    <strong>${employee.emergencyLeave - (employee.usedEmergency || 0)} متبقي</strong>
                </div>
            </div>
        </div>

        ${employee.leaveHistory && employee.leaveHistory.length > 0 ? `
            <div class="preview-section">
                <h4>📋 سجل الإجازات (آخر 3 إجازات)</h4>
                <div class="leave-history">
                    ${employee.leaveHistory.slice(-3).map(leave => `
                        <div class="history-item">
                            <strong>${leave.typeName || leave.type}:</strong>
                            ${leave.days} أيام من ${formatDateArabic(leave.startDate)} إلى ${formatDateArabic(leave.endDate)}
                            ${leave.reason ? `<br><small>السبب: ${leave.reason}</small>` : ''}
                        </div>
                    `).join('')}
                    ${employee.leaveHistory.length > 3 ? `<div class="more-items">... و ${employee.leaveHistory.length - 3} إجازة أخرى</div>` : ''}
                </div>
            </div>
        ` : '<div class="preview-section"><h4>📋 سجل الإجازات</h4><p>لا توجد إجازات مسجلة</p></div>'}
    `;
}

console.log('🔧 تم تحميل ملف التقارير المتقدمة بنجاح');
