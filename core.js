/**
 * نظام إدارة إجازات الموظفين المتطور
 * ملف الوظائف الأساسية المتبقية
 * يحتوي على وظائف الإحصائيات والبحث والفلترة والتهيئة
 */

// ===== وظائف الإحصائيات =====

/**
 * تحديث الإحصائيات
 */
function updateStats() {
    try {
        console.log('📊 تحديث الإحصائيات...');

        // التأكد من وجود مصفوفة الموظفين
        if (!window.employees || !Array.isArray(window.employees)) {
            window.employees = [];
        }

        const employees = window.employees;
        const totalEmployees = employees.length;
        const totalCarriedOver = employees.reduce((sum, emp) => sum + (emp.carriedOverLeave || 0), 0);
        const totalUsedLeaves = employees.reduce((sum, emp) => sum + (emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0), 0);
        const totalRemainingLeaves = employees.reduce((sum, emp) => {
            const annualRemaining = ((emp.annualLeave || 0) + (emp.carriedOverLeave || 0)) - (emp.usedAnnual || 0);
            const sickRemaining = (emp.sickLeave || 0) - (emp.usedSick || 0);
            const emergencyRemaining = (emp.emergencyLeave || 0) - (emp.usedEmergency || 0);
            return sum + annualRemaining + sickRemaining + emergencyRemaining;
        }, 0);
        const totalAllowedLeaves = employees.reduce((sum, emp) =>
            sum + (emp.annualLeave || 0) + (emp.carriedOverLeave || 0) + (emp.sickLeave || 0) + (emp.emergencyLeave || 0), 0);
        const averageUsage = totalAllowedLeaves > 0 ? Math.round((totalUsedLeaves / totalAllowedLeaves) * 100) : 0;

        console.log(`📊 الإحصائيات المحسوبة:`, {
            totalEmployees,
            totalCarriedOver,
            totalUsedLeaves,
            totalRemainingLeaves,
            averageUsage
        });

        // تحديث العناصر مع معالجة أفضل للأخطاء
        const updateElement = (id, value, label) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
                element.style.opacity = '1';
                element.style.transform = 'scale(1)';
                console.log(`✅ تم تحديث ${label}: ${value}`);
                return true;
            } else {
                console.warn(`⚠️ لم يتم العثور على العنصر: ${id}`);
                return false;
            }
        };

        // انتظار قليل للتأكد من جاهزية DOM
        setTimeout(() => {
            updateElement('totalEmployees', totalEmployees, 'إجمالي الموظفين');
            updateElement('totalCarriedOver', totalCarriedOver, 'الرصيد المرحل');
            updateElement('totalUsedLeaves', totalUsedLeaves, 'الإجازات المستخدمة');
            updateElement('totalRemainingLeaves', totalRemainingLeaves, 'الإجازات المتبقية');
            updateElement('averageUsage', averageUsage + '%', 'متوسط الاستخدام');
        }, 50);

        console.log('✅ تم تحديث الإحصائيات بنجاح');

    } catch (error) {
        console.error('❌ خطأ في تحديث الإحصائيات:', error);

        // قيم احتياطية مع تأخير
        setTimeout(() => {
            const fallbackUpdate = (id, value) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                    element.style.color = '#e74c3c';
                }
            };

            fallbackUpdate('totalEmployees', '0');
            fallbackUpdate('totalCarriedOver', '0');
            fallbackUpdate('totalUsedLeaves', '0');
            fallbackUpdate('totalRemainingLeaves', '0');
            fallbackUpdate('averageUsage', '0%');
        }, 100);
    }
}

/**
 * تحديث العرض السريع للموظفين
 */
function updateQuickEmployeeView() {
    const quickView = document.getElementById('quickEmployeeView');
    if (!quickView) return;

    if (window.employees.length === 0) {
        quickView.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <div style="font-size: 48px; margin-bottom: 20px; opacity: 0.5;">👥</div>
                <h4 style="color: #2c3e50; margin-bottom: 15px;">مرحباً بك في نظام إدارة الإجازات!</h4>
                <p style="color: #7f8c8d; margin-bottom: 20px; line-height: 1.6;">
                    لا توجد بيانات موظفين حالياً. ابدأ ببناء قاعدة بيانات الموظفين الخاصة بك.
                </p>
                <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                    <button class="btn btn-primary" onclick="showSection('employees')">
                        ➕ إضافة موظف جديد
                    </button>
                    <button class="btn btn-secondary" onclick="loadTestData()">
                        🧪 تحميل بيانات تجريبية
                    </button>
                </div>
            </div>
        `;
        return;
    }

    // عرض الموظفين الأخيرين مع عرض محسن
    const recentEmployees = window.employees.slice(-5);
    const totalEmployees = window.employees.length;

    quickView.innerHTML = `
        <div style="margin-bottom: 20px; padding: 15px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border-radius: 8px;">
            <h4 style="margin: 0 0 10px 0;">📊 ملخص سريع</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px; text-align: center;">
                <div>
                    <div style="font-size: 24px; font-weight: bold;">${totalEmployees}</div>
                    <div style="font-size: 12px; opacity: 0.9;">إجمالي الموظفين</div>
                </div>
                <div>
                    <div style="font-size: 24px; font-weight: bold;">${window.employees.filter(emp => getEmployeeStatus(emp) === 'طبيعي').length}</div>
                    <div style="font-size: 12px; opacity: 0.9;">رصيد طبيعي</div>
                </div>
                <div>
                    <div style="font-size: 24px; font-weight: bold;">${window.employees.filter(emp => getEmployeeStatus(emp) === 'رصيد منخفض').length}</div>
                    <div style="font-size: 12px; opacity: 0.9;">رصيد منخفض</div>
                </div>
                <div>
                    <div style="font-size: 24px; font-weight: bold;">${window.employees.filter(emp => getEmployeeStatus(emp) === 'نفد الرصيد').length}</div>
                    <div style="font-size: 12px; opacity: 0.9;">نفد الرصيد</div>
                </div>
            </div>
        </div>

        <h5 style="margin-bottom: 15px; color: #2c3e50;">آخر الموظفين المضافين:</h5>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            ${recentEmployees.map(emp => {
                const status = getEmployeeStatus(emp);
                const statusColor = status === 'طبيعي' ? '#27ae60' : status === 'رصيد منخفض' ? '#f39c12' : '#e74c3c';
                const remaining = (emp.annualLeave + emp.carriedOverLeave) - emp.usedAnnual;
                return `
                    <div style="padding: 15px; border: 1px solid #e9ecef; border-radius: 8px; background: white; transition: transform 0.2s; cursor: pointer;"
                         onclick="openLeaveManagement(${window.employees.findIndex(e => e.id === emp.id)})"
                         onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 15px rgba(0,0,0,0.1)'"
                         onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                        <h4 style="margin: 0 0 10px 0; color: #2c3e50; font-size: 14px;">${emp.name}</h4>
                        <p style="margin: 5px 0; font-size: 12px; color: #7f8c8d;">🏢 ${emp.department}</p>
                        <p style="margin: 5px 0; font-size: 12px; color: ${statusColor}; font-weight: 600;">📊 ${status}</p>
                        <p style="margin: 5px 0; font-size: 12px; color: #2c3e50;">📅 المتبقي: ${remaining} يوم</p>
                        <div style="margin-top: 10px; font-size: 11px; color: #95a5a6;">انقر لإدارة الإجازات</div>
                    </div>
                `;
            }).join('')}
        </div>

        ${totalEmployees > 5 ? `
            <div style="text-align: center; margin-top: 15px;">
                <button class="btn btn-primary btn-small" onclick="showSection('employees')">
                    عرض جميع الموظفين (${totalEmployees})
                </button>
            </div>
        ` : ''}
    `;
}

// ===== وظائف البحث والفلترة =====

/**
 * البحث والفلترة مع debounce
 */
const debouncedFilterEmployees = debounce(filterEmployees, 300);

/**
 * فلترة الموظفين
 */
function filterEmployees() {
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const departmentFilter = document.getElementById('departmentFilter');

    if (!searchInput || !statusFilter || !departmentFilter) return;

    const searchTerm = searchInput.value.toLowerCase().trim();
    const statusValue = statusFilter.value;
    const departmentValue = departmentFilter.value;

    // فلاتر البحث المتقدم
    const hireDateFrom = document.getElementById('hireDateFrom')?.value;
    const hireDateTo = document.getElementById('hireDateTo')?.value;
    const serviceYearsFrom = document.getElementById('serviceYearsFrom')?.value;
    const serviceYearsTo = document.getElementById('serviceYearsTo')?.value;
    const remainingFrom = document.getElementById('remainingFrom')?.value;
    const remainingTo = document.getElementById('remainingTo')?.value;

    window.filteredEmployees = window.employees.filter(employee => {
        const matchesSearch = !searchTerm ||
            employee.name.toLowerCase().includes(searchTerm) ||
            employee.id.toLowerCase().includes(searchTerm) ||
            employee.department.toLowerCase().includes(searchTerm);

        const employeeStatus = getEmployeeStatus(employee);
        const matchesStatus = !statusValue || employeeStatus === statusValue;

        const matchesDepartment = !departmentValue || employee.department === departmentValue;

        // فلاتر البحث المتقدم
        const hireDate = new Date(employee.hireDate);
        const matchesHireDateFrom = !hireDateFrom || hireDate >= new Date(hireDateFrom);
        const matchesHireDateTo = !hireDateTo || hireDate <= new Date(hireDateTo);

        const yearsOfService = calculateYearsOfService(employee.hireDate);
        const matchesServiceYearsFrom = !serviceYearsFrom || yearsOfService >= parseInt(serviceYearsFrom);
        const matchesServiceYearsTo = !serviceYearsTo || yearsOfService <= parseInt(serviceYearsTo);

        const totalAvailable = employee.annualLeave + employee.carriedOverLeave;
        const remaining = totalAvailable - employee.usedAnnual;
        const matchesRemainingFrom = !remainingFrom || remaining >= parseInt(remainingFrom);
        const matchesRemainingTo = !remainingTo || remaining <= parseInt(remainingTo);

        return matchesSearch && matchesStatus && matchesDepartment &&
               matchesHireDateFrom && matchesHireDateTo &&
               matchesServiceYearsFrom && matchesServiceYearsTo &&
               matchesRemainingFrom && matchesRemainingTo;
    });

    // العودة للصفحة الأولى عند الفلترة
    currentPage = 1;
    updateTable();
    updateFilterResults();
}

/**
 * تحديث نتائج الفلترة
 */
function updateFilterResults() {
    const resultsElement = document.getElementById('filterResults');
    if (!resultsElement) return;

    const totalEmployees = window.employees.length;
    const filteredCount = window.filteredEmployees.length;

    if (filteredCount === totalEmployees) {
        resultsElement.textContent = `عرض جميع الموظفين (${totalEmployees})`;
    } else {
        resultsElement.textContent = `عرض ${filteredCount} من أصل ${totalEmployees} موظف`;
    }
}

/**
 * مسح الفلاتر
 */
function clearFilters() {
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const departmentFilter = document.getElementById('departmentFilter');

    if (searchInput) searchInput.value = '';
    if (statusFilter) statusFilter.value = '';
    if (departmentFilter) departmentFilter.value = '';

    window.filteredEmployees = [...window.employees];
    updateTable();
    updateFilterResults();
}

/**
 * تحديث فلتر الأقسام
 */
function updateDepartmentFilter() {
    const departmentFilter = document.getElementById('departmentFilter');
    if (!departmentFilter) return;

    const currentValue = departmentFilter.value;
    const departments = [...new Set(window.employees.map(emp => emp.department))].sort();

    departmentFilter.innerHTML = '<option value="">جميع الأقسام</option>';

    departments.forEach(dept => {
        const option = document.createElement('option');
        option.value = dept;
        option.textContent = dept;
        departmentFilter.appendChild(option);
    });

    if (departments.includes(currentValue)) {
        departmentFilter.value = currentValue;
    }
}

// ===== وظائف البحث المتقدم =====

/**
 * تبديل البحث المتقدم
 */
function toggleAdvancedSearch() {
    const advancedSearch = document.getElementById('advancedSearch');
    const toggleButton = document.getElementById('advancedSearchToggle');

    if (advancedSearch.style.display === 'none') {
        advancedSearch.style.display = 'block';
        toggleButton.textContent = '🔼 إخفاء البحث المتقدم';
    } else {
        advancedSearch.style.display = 'none';
        toggleButton.textContent = '🔍 البحث المتقدم';
    }
}

/**
 * مسح البحث المتقدم
 */
function clearAdvancedSearch() {
    document.getElementById('hireDateFrom').value = '';
    document.getElementById('hireDateTo').value = '';
    document.getElementById('serviceYearsFrom').value = '';
    document.getElementById('serviceYearsTo').value = '';
    document.getElementById('remainingFrom').value = '';
    document.getElementById('remainingTo').value = '';
    filterEmployees();
}
