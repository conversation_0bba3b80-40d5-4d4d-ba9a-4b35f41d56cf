// تحليل سريع للخطأ - تشغيل في console
console.log('🔍 بدء التحليل السريع للخطأ...');

// 1. فحص الأخطاء المحفوظة
try {
    const savedErrors = JSON.parse(localStorage.getItem('systemErrors') || '[]');
    console.log('📋 الأخطاء المحفوظة:', savedErrors);
    
    if (savedErrors.length > 0) {
        console.group('🔍 تحليل الأخطاء المحفوظة');
        savedErrors.forEach((error, index) => {
            console.group(`خطأ ${index + 1} - ${error.timestamp}`);
            console.error('الملف:', error.filename);
            console.error('السطر:', error.lineno);
            console.error('الرسالة:', error.message);
            if (error.stack && error.stack !== 'غير متاح') {
                console.error('Stack:', error.stack);
            }
            console.groupEnd();
        });
        console.groupEnd();
    } else {
        console.log('✅ لا توجد أخطاء محفوظة');
    }
} catch (error) {
    console.error('❌ فشل في قراءة الأخطاء المحفوظة:', error);
}

// 2. فحص الوظائف الأساسية
console.group('⚙️ فحص الوظائف الأساسية');
const criticalFunctions = [
    'showSection', 'addEmployee', 'editEmployee', 'deleteEmployee',
    'updateTable', 'filterEmployees', 'showAlert', 'saveData',
    'loadDataFromStorage', 'getEmployeeStatus', 'showNotification'
];

let missingFunctions = [];
criticalFunctions.forEach(funcName => {
    if (typeof window[funcName] === 'function') {
        console.log(`✅ ${funcName} متاحة`);
    } else {
        console.error(`❌ ${funcName} غير متاحة`);
        missingFunctions.push(funcName);
    }
});

if (missingFunctions.length > 0) {
    console.error('🚨 وظائف مفقودة:', missingFunctions);
} else {
    console.log('✅ جميع الوظائف الأساسية متاحة');
}
console.groupEnd();

// 3. فحص المتغيرات الأساسية
console.group('📊 فحص المتغيرات الأساسية');
const criticalVariables = [
    { name: 'employees', type: 'array' },
    { name: 'filteredEmployees', type: 'array' },
    { name: 'editingIndex', type: 'number' }
];

let missingVariables = [];
criticalVariables.forEach(variable => {
    const value = window[variable.name];
    if (value !== undefined) {
        const actualType = Array.isArray(value) ? 'array' : typeof value;
        if (actualType === variable.type || (variable.type === 'array' && Array.isArray(value))) {
            console.log(`✅ ${variable.name}: ${actualType} (${Array.isArray(value) ? value.length + ' عنصر' : value})`);
        } else {
            console.warn(`⚠️ ${variable.name}: نوع خاطئ - متوقع ${variable.type}, فعلي ${actualType}`);
        }
    } else {
        console.error(`❌ ${variable.name}: غير معرف`);
        missingVariables.push(variable.name);
    }
});

if (missingVariables.length > 0) {
    console.error('🚨 متغيرات مفقودة:', missingVariables);
} else {
    console.log('✅ جميع المتغيرات الأساسية موجودة');
}
console.groupEnd();

// 4. فحص تحميل الملفات
console.group('📁 فحص تحميل الملفات');
const requiredFiles = [
    'utils.js', 'core.js', 'sections.js', 'functions.js',
    'features.js', 'extras.js', 'reports-advanced.js',
    'settings-functions.js', 'app.js', 'init.js'
];

const scripts = Array.from(document.scripts);
const loadedFiles = scripts.map(script => {
    const src = script.src;
    return src ? src.split('/').pop() : null;
}).filter(Boolean);

let missingFiles = [];
requiredFiles.forEach(file => {
    if (loadedFiles.includes(file)) {
        console.log(`✅ ${file} محمل`);
    } else {
        console.error(`❌ ${file} مفقود`);
        missingFiles.push(file);
    }
});

if (missingFiles.length > 0) {
    console.error('🚨 ملفات مفقودة:', missingFiles);
} else {
    console.log('✅ جميع الملفات محملة');
}
console.groupEnd();

// 5. اختبار وظائف محددة قد تسبب المشكلة
console.group('🧪 اختبار الوظائف المشكوك فيها');

// اختبار showSection
try {
    if (typeof showSection === 'function') {
        console.log('✅ showSection متاحة - اختبار استدعاء...');
        // لا نستدعيها فعلياً لتجنب تغيير الواجهة
        console.log('✅ showSection جاهزة للاستدعاء');
    } else {
        console.error('❌ showSection غير متاحة');
    }
} catch (error) {
    console.error('❌ خطأ في اختبار showSection:', error);
}

// اختبار editEmployee
try {
    if (typeof editEmployee === 'function') {
        console.log('✅ editEmployee متاحة');
        // فحص إذا كانت تحتاج متغيرات معينة
        if (window.employees && Array.isArray(window.employees)) {
            console.log('✅ مصفوفة employees متاحة للاختبار');
        } else {
            console.warn('⚠️ مصفوفة employees غير متاحة - قد تسبب خطأ في editEmployee');
        }
    } else {
        console.error('❌ editEmployee غير متاحة');
    }
} catch (error) {
    console.error('❌ خطأ في اختبار editEmployee:', error);
}

// اختبار showNotification
try {
    if (typeof showNotification === 'function') {
        console.log('✅ showNotification متاحة - اختبار استدعاء...');
        showNotification('اختبار نظام الإشعارات', 'info', 2000);
        console.log('✅ showNotification تعمل بشكل صحيح');
    } else {
        console.error('❌ showNotification غير متاحة');
    }
} catch (error) {
    console.error('❌ خطأ في اختبار showNotification:', error);
}

console.groupEnd();

// 6. تحليل النتائج وتقديم الحل
console.group('💡 تحليل النتائج والحل المقترح');

let problemsFound = [];
let solutions = [];

if (missingFiles.length > 0) {
    problemsFound.push(`ملفات مفقودة: ${missingFiles.join(', ')}`);
    solutions.push('تحقق من وجود الملفات في المجلد وصحة مساراتها في index.html');
}

if (missingFunctions.length > 0) {
    problemsFound.push(`وظائف مفقودة: ${missingFunctions.join(', ')}`);
    solutions.push('تحقق من تحميل الملفات بالترتيب الصحيح وعدم وجود أخطاء syntax');
}

if (missingVariables.length > 0) {
    problemsFound.push(`متغيرات مفقودة: ${missingVariables.join(', ')}`);
    solutions.push('تهيئة المتغيرات المفقودة في بداية التطبيق');
}

if (problemsFound.length === 0) {
    console.log('✅ لم يتم العثور على مشاكل واضحة في الفحص الحالي');
    console.log('💡 الخطأ قد يكون:');
    console.log('   - خطأ مؤقت في تحميل الملفات');
    console.log('   - تضارب في cache المتصفح');
    console.log('   - خطأ في توقيت تنفيذ الكود');
    console.log('');
    console.log('🔧 الحلول المقترحة:');
    console.log('   1. مسح cache المتصفح (Ctrl + Shift + Delete)');
    console.log('   2. إعادة تحميل الصفحة (F5)');
    console.log('   3. فتح الصفحة في نافذة خاصة (Incognito)');
    console.log('   4. إعادة تشغيل المتصفح');
} else {
    console.error('🚨 مشاكل مكتشفة:');
    problemsFound.forEach((problem, index) => {
        console.error(`   ${index + 1}. ${problem}`);
    });
    console.log('');
    console.log('🔧 الحلول المقترحة:');
    solutions.forEach((solution, index) => {
        console.log(`   ${index + 1}. ${solution}`);
    });
}

console.groupEnd();

// 7. إنشاء تقرير سريع
const report = {
    timestamp: new Date().toLocaleString('ar-SA'),
    filesStatus: missingFiles.length === 0 ? 'OK' : 'ISSUES',
    functionsStatus: missingFunctions.length === 0 ? 'OK' : 'ISSUES',
    variablesStatus: missingVariables.length === 0 ? 'OK' : 'ISSUES',
    missingFiles,
    missingFunctions,
    missingVariables,
    problemsFound,
    solutions
};

console.log('📊 تقرير سريع:', report);

// حفظ التقرير في localStorage للمراجعة اللاحقة
try {
    localStorage.setItem('quickDiagnosticReport', JSON.stringify(report));
    console.log('💾 تم حفظ التقرير في localStorage');
} catch (error) {
    console.warn('⚠️ فشل في حفظ التقرير:', error);
}

console.log('✅ انتهى التحليل السريع');
console.log('');
console.log('📋 للحصول على تقرير مفصل، استخدم:');
console.log('   JSON.parse(localStorage.getItem("quickDiagnosticReport"))');
console.log('');
console.log('🔧 لتشغيل فحص النظام المدمج، استخدم:');
console.log('   systemHealthCheck()');
