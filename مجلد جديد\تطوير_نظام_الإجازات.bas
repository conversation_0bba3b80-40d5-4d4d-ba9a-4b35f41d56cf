Attribute VB_Name = "تطوير_نظام_الإجازات"
' وحدة ماكرو لتطوير نظام الإجازات المتكامل
' تم إنشاؤها بتاريخ: 2023

' إعداد لوحة المعلومات الرئيسية
Sub إنشاء_لوحة_المعلومات()
    Dim ws As Worksheet
    Dim dashboardExists As Boolean
    dashboardExists = False
    
    ' التحقق من وجود ورقة لوحة المعلومات
    For Each ws In ThisWorkbook.Worksheets
        If ws.Name = "لوحة المعلومات" Then
            dashboardExists = True
            Exit For
        End If
    Next ws
    
    ' إنشاء ورقة لوحة المعلومات إذا لم تكن موجودة
    If Not dashboardExists Then
        Set ws = ThisWorkbook.Worksheets.Add(Before:=ThisWorkbook.Worksheets(1))
        ws.Name = "لوحة المعلومات"
    Else
        Set ws = ThisWorkbook.Worksheets("لوحة المعلومات")
        ws.Cells.Clear
    End If
    
    ' تنسيق لوحة المعلومات
    With ws
        ' عنوان لوحة المعلومات
        .Range("A1:J1").Merge
        .Range("A1").Value = "لوحة معلومات نظام الإجازات المتكامل"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1").HorizontalAlignment = xlCenter
        
        ' إضافة التاريخ
        .Range("A2").Value = "تاريخ التحديث:"
        .Range("B2").Value = Date
        .Range("B2").NumberFormat = "yyyy/mm/dd"
        
        ' إضافة أقسام لوحة المعلومات
        .Range("A4").Value = "ملخص الإجازات"
        .Range("A4").Font.Bold = True
        
        .Range("A10").Value = "الإجازات حسب النوع"
        .Range("A10").Font.Bold = True
        
        .Range("F4").Value = "الإجازات حسب القسم"
        .Range("F4").Font.Bold = True
        
        .Range("F10").Value = "الإجازات المقبلة"
        .Range("F10").Font.Bold = True
        
        ' تنسيق الخلايا
        .Range("A4:E4").Interior.Color = RGB(200, 200, 200)
        .Range("A10:E10").Interior.Color = RGB(200, 200, 200)
        .Range("F4:J4").Interior.Color = RGB(200, 200, 200)
        .Range("F10:J10").Interior.Color = RGB(200, 200, 200)
    End With
    
    MsgBox "تم إنشاء لوحة المعلومات بنجاح!", vbInformation
End Sub

' إضافة نموذج إدخال الإجازات
Sub إضافة_نموذج_إدخال_الإجازات()
    Dim ws As Worksheet
    Dim formExists As Boolean
    formExists = False
    
    ' التحقق من وجود ورقة نموذج الإدخال
    For Each ws In ThisWorkbook.Worksheets
        If ws.Name = "نموذج إدخال الإجازات" Then
            formExists = True
            Exit For
        End If
    Next ws
    
    ' إنشاء ورقة نموذج الإدخال إذا لم تكن موجودة
    If Not formExists Then
        Set ws = ThisWorkbook.Worksheets.Add(After:=ThisWorkbook.Worksheets(1))
        ws.Name = "نموذج إدخال الإجازات"
    Else
        Set ws = ThisWorkbook.Worksheets("نموذج إدخال الإجازات")
        ws.Cells.Clear
    End If
    
    ' تنسيق نموذج الإدخال
    With ws
        ' عنوان النموذج
        .Range("A1:F1").Merge
        .Range("A1").Value = "نموذج إدخال الإجازات"
        .Range("A1").Font.Size = 14
        .Range("A1").Font.Bold = True
        .Range("A1").HorizontalAlignment = xlCenter
        
        ' حقول النموذج
        .Range("A3").Value = "اسم الموظف:"
        .Range("A4").Value = "الرقم الوظيفي:"
        .Range("A5").Value = "القسم:"
        .Range("A6").Value = "نوع الإجازة:"
        .Range("A7").Value = "تاريخ البداية:"
        .Range("A8").Value = "تاريخ النهاية:"
        .Range("A9").Value = "عدد الأيام:"
        .Range("A10").Value = "ملاحظات:"
        
        ' تنسيق حقول الإدخال
        .Range("B3:D3").Merge
        .Range("B4:D4").Merge
        .Range("B5:D5").Merge
        .Range("B6:D6").Merge
        .Range("B7:D7").Merge
        .Range("B8:D8").Merge
        .Range("B9:D9").Merge
        .Range("B10:D10").Merge
        
        ' إضافة قائمة منسدلة لنوع الإجازة
        With .Range("B6").Validation
            .Delete
            .Add Type:=xlValidateList, AlertStyle:=xlValidAlertStop, Operator:= _
            xlBetween, Formula1:="سنوية,مرضية,اضطرارية,بدون راتب,أخرى"
        End With
        
        ' إضافة زر الحفظ
        .Shapes.AddShape(msoShapeRoundedRectangle, 300, 250, 100, 30).Select
        With Selection
            .Characters.Text = "حفظ الإجازة"
            .Name = "زر_حفظ_الإجازة"
            .Fill.ForeColor.RGB = RGB(0, 112, 192)
            .Line.ForeColor.RGB = RGB(0, 0, 0)
            .Characters.Font.Color = RGB(255, 255, 255)
            .Characters.Font.Bold = True
        End With
    End With
    
    MsgBox "تم إنشاء نموذج إدخال الإجازات بنجاح!", vbInformation
End Sub

' إنشاء تقرير الإجازات
Sub إنشاء_تقرير_الإجازات()
    Dim ws As Worksheet
    Dim reportExists As Boolean
    reportExists = False
    
    ' التحقق من وجود ورقة التقرير
    For Each ws In ThisWorkbook.Worksheets
        If ws.Name = "تقرير الإجازات" Then
            reportExists = True
            Exit For
        End If
    Next ws
    
    ' إنشاء ورقة التقرير إذا لم تكن موجودة
    If Not reportExists Then
        Set ws = ThisWorkbook.Worksheets.Add(After:=ThisWorkbook.Worksheets(2))
        ws.Name = "تقرير الإجازات"
    Else
        Set ws = ThisWorkbook.Worksheets("تقرير الإجازات")
        ws.Cells.Clear
    End If
    
    ' تنسيق التقرير
    With ws
        ' عنوان التقرير
        .Range("A1:H1").Merge
        .Range("A1").Value = "تقرير الإجازات"
        .Range("A1").Font.Size = 14
        .Range("A1").Font.Bold = True
        .Range("A1").HorizontalAlignment = xlCenter
        
        ' رأس الجدول
        .Range("A3").Value = "م"
        .Range("B3").Value = "اسم الموظف"
        .Range("C3").Value = "الرقم الوظيفي"
        .Range("D3").Value = "القسم"
        .Range("E3").Value = "نوع الإجازة"
        .Range("F3").Value = "تاريخ البداية"
        .Range("G3").Value = "تاريخ النهاية"
        .Range("H3").Value = "عدد الأيام"
        
        ' تنسيق رأس الجدول
        .Range("A3:H3").Font.Bold = True
        .Range("A3:H3").Interior.Color = RGB(200, 200, 200)
        .Range("A3:H3").Borders.Weight = xlThin
    End With
    
    MsgBox "تم إنشاء تقرير الإجازات بنجاح!", vbInformation
End Sub

' حساب رصيد الإجازات
Sub حساب_رصيد_الإجازات()
    Dim ws As Worksheet
    Dim balanceExists As Boolean
    balanceExists = False
    
    ' التحقق من وجود ورقة رصيد الإجازات
    For Each ws In ThisWorkbook.Worksheets
        If ws.Name = "رصيد الإجازات" Then
            balanceExists = True
            Exit For
        End If
    Next ws
    
    ' إنشاء ورقة رصيد الإجازات إذا لم تكن موجودة
    If Not balanceExists Then
        Set ws = ThisWorkbook.Worksheets.Add(After:=ThisWorkbook.Worksheets(3))
        ws.Name = "رصيد الإجازات"
    Else
        Set ws = ThisWorkbook.Worksheets("رصيد الإجازات")
        ws.Cells.Clear
    End If
    
    ' تنسيق ورقة رصيد الإجازات
    With ws
        ' عنوان الورقة
        .Range("A1:G1").Merge
        .Range("A1").Value = "رصيد الإجازات"
        .Range("A1").Font.Size = 14
        .Range("A1").Font.Bold = True
        .Range("A1").HorizontalAlignment = xlCenter
        
        ' رأس الجدول
        .Range("A3").Value = "م"
        .Range("B3").Value = "اسم الموظف"
        .Range("C3").Value = "الرقم الوظيفي"
        .Range("D3").Value = "الرصيد السنوي"
        .Range("E3").Value = "الإجازات المستخدمة"
        .Range("F3").Value = "الرصيد المتبقي"
        .Range("G3").Value = "ملاحظات"
        
        ' تنسيق رأس الجدول
        .Range("A3:G3").Font.Bold = True
        .Range("A3:G3").Interior.Color = RGB(200, 200, 200)
        .Range("A3:G3").Borders.Weight = xlThin
        
        ' إضافة معادلة لحساب الرصيد المتبقي
        .Range("F4").Formula = "=D4-E4"
    End With
    
    MsgBox "تم إنشاء ورقة رصيد الإجازات بنجاح!", vbInformation
End Sub

' إنشاء نظام التنبيهات
Sub إنشاء_نظام_التنبيهات()
    Dim ws As Worksheet
    Dim alertsExists As Boolean
    alertsExists = False
    
    ' التحقق من وجود ورقة التنبيهات
    For Each ws In ThisWorkbook.Worksheets
        If ws.Name = "التنبيهات" Then
            alertsExists = True
            Exit For
        End If
    Next ws
    
    ' إنشاء ورقة التنبيهات إذا لم تكن موجودة
    If Not alertsExists Then
        Set ws = ThisWorkbook.Worksheets.Add(After:=ThisWorkbook.Worksheets(4))
        ws.Name = "التنبيهات"
    Else
        Set ws = ThisWorkbook.Worksheets("التنبيهات")
        ws.Cells.Clear
    End If
    
    ' تنسيق ورقة التنبيهات
    With ws
        ' عنوان الورقة
        .Range("A1:F1").Merge
        .Range("A1").Value = "نظام التنبيهات"
        .Range("A1").Font.Size = 14
        .Range("A1").Font.Bold = True
        .Range("A1").HorizontalAlignment = xlCenter
        
        ' رأس الجدول
        .Range("A3").Value = "م"
        .Range("B3").Value = "اسم الموظف"
        .Range("C3").Value = "نوع التنبيه"
        .Range("D3").Value = "التاريخ"
        .Range("E3").Value = "الحالة"
        .Range("F3").Value = "ملاحظات"
        
        ' تنسيق رأس الجدول
        .Range("A3:F3").Font.Bold = True
        .Range("A3:F3").Interior.Color = RGB(200, 200, 200)
        .Range("A3:F3").Borders.Weight = xlThin
    End With
    
    MsgBox "تم إنشاء نظام التنبيهات بنجاح!", vbInformation
End Sub

' إنشاء القائمة الرئيسية
Sub إنشاء_القائمة_الرئيسية()
    Dim ws As Worksheet
    Dim menuExists As Boolean
    menuExists = False
    
    ' التحقق من وجود ورقة القائمة الرئيسية
    For Each ws In ThisWorkbook.Worksheets
        If ws.Name = "القائمة الرئيسية" Then
            menuExists = True
            Exit For
        End If
    Next ws
    
    ' إنشاء ورقة القائمة الرئيسية إذا لم تكن موجودة
    If Not menuExists Then
        Set ws = ThisWorkbook.Worksheets.Add(Before:=ThisWorkbook.Worksheets(1))
        ws.Name = "القائمة الرئيسية"
    Else
        Set ws = ThisWorkbook.Worksheets("القائمة الرئيسية")
        ws.Cells.Clear
    End If
    
    ' تنسيق القائمة الرئيسية
    With ws
        ' عنوان القائمة
        .Range("A1:F1").Merge
        .Range("A1").Value = "نظام الإجازات المتكامل"
        .Range("A1").Font.Size = 18
        .Range("A1").Font.Bold = True
        .Range("A1").HorizontalAlignment = xlCenter
        
        ' إضافة أزرار القائمة
        ' زر لوحة المعلومات
        .Shapes.AddShape(msoShapeRoundedRectangle, 200, 100, 150, 40).Select
        With Selection
            .Characters.Text = "لوحة المعلومات"
            .Name = "زر_لوحة_المعلومات"
            .Fill.ForeColor.RGB = RGB(0, 112, 192)
            .Line.ForeColor.RGB = RGB(0, 0, 0)
            .Characters.Font.Color = RGB(255, 255, 255)
            .Characters.Font.Bold = True
        End With
        
        ' زر نموذج إدخال الإجازات
        .Shapes.AddShape(msoShapeRoundedRectangle, 200, 150, 150, 40).Select
        With Selection
            .Characters.Text = "إدخال إجازة جديدة"
            .Name = "زر_إدخال_إجازة"
            .Fill.ForeColor.RGB = RGB(0, 176, 80)
            .Line.ForeColor.RGB = RGB(0, 0, 0)
            .Characters.Font.Color = RGB(255, 255, 255)
            .Characters.Font.Bold = True
        End With
        
        ' زر تقرير الإجازات
        .Shapes.AddShape(msoShapeRoundedRectangle, 200, 200, 150, 40).Select
        With Selection
            .Characters.Text = "تقرير الإجازات"
            .Name = "زر_تقرير_الإجازات"
            .Fill.ForeColor.RGB = RGB(255, 192, 0)
            .Line.ForeColor.RGB = RGB(0, 0, 0)
            .Characters.Font.Color = RGB(0, 0, 0)
            .Characters.Font.Bold = True
        End With
        
        ' زر رصيد الإجازات
        .Shapes.AddShape(msoShapeRoundedRectangle, 200, 250, 150, 40).Select
        With Selection
            .Characters.Text = "رصيد الإجازات"
            .Name = "زر_رصيد_الإجازات"
            .Fill.ForeColor.RGB = RGB(192, 0, 0)
            .Line.ForeColor.RGB = RGB(0, 0, 0)
            .Characters.Font.Color = RGB(255, 255, 255)
            .Characters.Font.Bold = True
        End With
        
        ' زر التنبيهات
        .Shapes.AddShape(msoShapeRoundedRectangle, 200, 300, 150, 40).Select
        With Selection
            .Characters.Text = "التنبيهات"
            .Name = "زر_التنبيهات"
            .Fill.ForeColor.RGB = RGB(112, 48, 160)
            .Line.ForeColor.RGB = RGB(0, 0, 0)
            .Characters.Font.Color = RGB(255, 255, 255)
            .Characters.Font.Bold = True
        End With
    End With
    
    MsgBox "تم إنشاء القائمة الرئيسية بنجاح!", vbInformation
End Sub

' تنفيذ جميع الوظائف
Sub تنفيذ_جميع_الوظائف()
    Call إنشاء_القائمة_الرئيسية
    Call إنشاء_لوحة_المعلومات
    Call إضافة_نموذج_إدخال_الإجازات
    Call إنشاء_تقرير_الإجازات
    Call حساب_رصيد_الإجازات
    Call إنشاء_نظام_التنبيهات
    
    MsgBox "تم تنفيذ جميع الوظائف بنجاح! يمكنك الآن استخدام نظام الإجازات المتكامل.", vbInformation
End Sub