# 📊 دليل التقارير المتقدمة - نظام إدارة الإجازات

## 🎯 نظرة عامة

تم تطوير نظام التقارير المتقدمة ليوفر تقارير احترافية ومفصلة بتنسيقات PDF و Excel مع دعم كامل للغة العربية والتقويم الميلادي.

## ✨ الميزات الجديدة

### 📄 تقارير PDF احترافية
- **تقرير شامل**: يتضمن جميع الموظفين مع الإحصائيات والرسوم البيانية
- **تقرير فردي**: تقرير مفصل لكل موظف مع سجل الإجازات
- **تقرير الأقسام**: تقارير مخصصة لكل قسم أو جميع الأقسام
- **تصميم احترافي**: شعار الشركة وتنسيق عربي صحيح

### 📈 تصدير Excel متقدم
- **ملف متعدد الأوراق**: ورقة لكل قسم + ورقة إجمالية
- **تنسيق احترافي**: ألوان وحدود وتنسيق متقدم
- **معادلات تلقائية**: حسابات Excel للإحصائيات
- **فلترة وترتيب**: جداول قابلة للفلترة والترتيب

### 🎨 واجهة مستخدم محسنة
- **نافذة خيارات متقدمة**: اختيار نوع التقرير والتنسيق
- **معاينة قبل التصدير**: عرض محتوى التقرير قبل الإنشاء
- **خيارات تخصيص**: فترة زمنية، أقسام محددة، موظفين محددين
- **تصميم متجاوب**: يعمل على جميع الأجهزة

## 🚀 كيفية الاستخدام

### الوصول للتقارير المتقدمة
1. انتقل إلى قسم **"التقارير"** من الشريط الجانبي
2. ستجد قسم **"التقارير المتقدمة"** باللون الأزرق
3. اختر نوع التقرير المطلوب

### أنواع التقارير المتاحة

#### 📊 تقرير شامل متقدم
- **الوصول**: انقر على "تقرير شامل متقدم"
- **المحتوى**: جميع الموظفين مع إحصائيات شاملة
- **التنسيقات**: PDF و Excel
- **الميزات**: رسوم بيانية، إحصائيات الأقسام، تحليلات متقدمة

#### 📄 PDF شامل
- **الوصول**: انقر على "PDF شامل"
- **المحتوى**: تقرير PDF احترافي لجميع الموظفين
- **الميزات**: تصميم احترافي، شعار الشركة، تنسيق عربي

#### 📈 Excel متعدد الأوراق
- **الوصول**: انقر على "Excel متعدد الأوراق"
- **المحتوى**: ملف Excel مع أوراق منفصلة لكل قسم
- **الميزات**: تنسيق متقدم، معادلات، رسوم بيانية

#### 🏢 تقارير الأقسام
- **الوصول**: انقر على "تقارير الأقسام"
- **الخيارات**: اختيار قسم محدد أو جميع الأقسام
- **التنسيقات**: PDF و Excel

#### 👤 تقارير فردية
- **الوصول**: انقر على "تقارير فردية"
- **الخيارات**: اختيار موظف محدد من القائمة
- **المحتوى**: معلومات مفصلة، رصيد الإجازات، سجل الإجازات

#### 👁️ معاينة التقارير
- **الوصول**: انقر على "معاينة التقارير"
- **الميزة**: عرض محتوى التقرير قبل التصدير
- **الفائدة**: توفير الوقت والتأكد من المحتوى

## ⚙️ خيارات التخصيص

### نافذة الخيارات المتقدمة
عند النقر على "تقرير شامل متقدم" ستظهر نافذة تحتوي على:

#### 🎯 نوع التقرير
- **تقرير شامل**: جميع الموظفين والأقسام
- **تقرير حسب الأقسام**: قسم محدد أو جميع الأقسام
- **تقرير موظف محدد**: موظف واحد فقط

#### 📄 تنسيق التصدير
- **PDF احترافي**: ملف PDF مع تصميم احترافي
- **Excel متقدم**: ملف Excel متعدد الأوراق
- **كلاهما**: إنشاء التقرير بكلا التنسيقين

#### 📅 فترة التقرير
- **من تاريخ**: تحديد تاريخ البداية للفلترة
- **إلى تاريخ**: تحديد تاريخ النهاية للفلترة
- **ملاحظة**: الفلترة تطبق على تاريخ التوظيف

#### ⚙️ خيارات إضافية
- **تضمين الرسوم البيانية**: إضافة رسوم بيانية للتقرير
- **تضمين سجل الإجازات**: إضافة تفاصيل الإجازات
- **تضمين الإحصائيات المتقدمة**: تحليلات وإحصائيات مفصلة
- **معاينة قبل التصدير**: عرض التقرير قبل الإنشاء

## 📋 محتوى التقارير

### التقرير الشامل PDF
1. **رأس التقرير**: شعار الشركة واسمها
2. **معلومات التقرير**: تاريخ الإنشاء وعدد الموظفين
3. **الإحصائيات العامة**: أرقام إجمالية للرصيد والاستخدام
4. **جدول الموظفين**: قائمة مفصلة بجميع الموظفين
5. **إحصائيات الأقسام**: تفصيل حسب كل قسم
6. **تذييل التقرير**: معلومات الشركة وتاريخ الإنشاء

### التقرير الفردي PDF
1. **معلومات الموظف الأساسية**: الاسم، الرقم، القسم، التوظيف
2. **رصيد الإجازات**: جدول مفصل لكل نوع إجازة
3. **سجل الإجازات**: تاريخ وتفاصيل كل إجازة
4. **الحالة الحالية**: تقييم وضع الرصيد

### ملف Excel المتقدم
1. **ورقة الملخص العام**: إحصائيات شاملة
2. **ورقة لكل قسم**: تفاصيل موظفي كل قسم
3. **ورقة جميع الموظفين**: قائمة شاملة مفصلة
4. **ورقة الإحصائيات**: تحليلات متقدمة ورسوم بيانية

## 🔧 المتطلبات التقنية

### المكتبات المستخدمة
- **jsPDF**: لإنشاء ملفات PDF
- **jsPDF AutoTable**: لإنشاء الجداول في PDF
- **SheetJS (XLSX)**: لإنشاء ملفات Excel

### التحميل التلقائي
- المكتبات تحمل تلقائياً من CDN عند الحاجة
- لا حاجة لتثبيت إضافي
- يعمل بدون اتصال بعد التحميل الأول

### دعم المتصفحات
- **Chrome**: دعم كامل
- **Firefox**: دعم كامل
- **Safari**: دعم كامل
- **Edge**: دعم كامل
- **المتصفحات القديمة**: دعم محدود

## 🎨 التصميم والتنسيق

### ألوان النظام
- **الأساسي**: #3498db (أزرق)
- **الثانوي**: #2c3e50 (رمادي داكن)
- **النجاح**: #27ae60 (أخضر)
- **التحذير**: #f39c12 (برتقالي)
- **الخطر**: #e74c3c (أحمر)

### الخطوط
- **العربية**: Arial, sans-serif
- **الإنجليزية**: Arial, sans-serif
- **الحجم**: متدرج حسب الأهمية

### التخطيط
- **اتجاه النص**: من اليمين لليسار (RTL)
- **التقويم**: ميلادي (Gregorian)
- **التاريخ**: تنسيق عربي

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### لا تظهر أزرار التقارير المتقدمة
- **السبب**: عدم تحميل ملف `reports-advanced.js`
- **الحل**: تأكد من وجود الملف وتحميله في `index.html`

#### خطأ في تحميل المكتبات
- **السبب**: مشكلة في الاتصال بالإنترنت
- **الحل**: تأكد من الاتصال بالإنترنت أو استخدم نسخ محلية

#### التقرير فارغ أو لا يحتوي على بيانات
- **السبب**: عدم وجود موظفين في النظام
- **الحل**: أضف موظفين أو استخدم البيانات التجريبية

#### مشكلة في التنسيق العربي
- **السبب**: عدم دعم المتصفح للنصوص العربية
- **الحل**: استخدم متصفح حديث أو حدث المتصفح الحالي

## 📞 الدعم والمساعدة

### الحصول على المساعدة
- **دليل المستخدم**: متاح في قسم الإعدادات
- **اختصارات لوحة المفاتيح**: `Ctrl + ?`
- **البيانات التجريبية**: `Ctrl + T`

### نصائح للاستخدام الأمثل
1. **استخدم المعاينة**: دائماً اعرض التقرير قبل التصدير
2. **اختر التنسيق المناسب**: PDF للعرض، Excel للتحليل
3. **فلتر البيانات**: استخدم الفلاتر لتقارير محددة
4. **احفظ النسخ الاحتياطية**: صدر نسخ احتياطية بانتظام

---

**تم تطوير التقارير المتقدمة بـ ❤️ لتوفير أفضل تجربة للمستخدمين**
