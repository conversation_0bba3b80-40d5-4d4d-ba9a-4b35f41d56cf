/**
 * نظام إدارة إجازات الموظفين المتطور
 * ملف التهيئة والإعداد
 * يحتوي على وظائف التهيئة واختصارات لوحة المفاتيح والحفظ التلقائي
 */

// ===== وظائف التهيئة =====

/**
 * تهيئة التطبيق
 */
function initializeApplication() {
    console.log('🔧 تهيئة التطبيق...');
    
    try {
        // تهيئة التنقل
        setupNavigation();
        
        // تهيئة اختصارات لوحة المفاتيح
        setupKeyboardShortcuts();
        
        // تهيئة الحفظ التلقائي
        setupAutoSave();
        
        // تحميل لوحة التحكم
        loadDashboard();
        
        // تحديث المكونات
        setTimeout(() => {
            updateStats();
            updateQuickEmployeeView();
            updateCharts();
            
            console.log('✅ تم تحميل النظام بنجاح');
            showNotification('🎉 مرحباً بك! تم تحميل نظام إدارة الإجازات بنجاح.', 'success', 5000);
        }, 500);
        
    } catch (error) {
        console.error('❌ خطأ في تهيئة التطبيق:', error);
        initializeFallback();
    }
}

/**
 * تهيئة احتياطية في حالة الأخطاء
 */
function initializeFallback() {
    console.log('🔄 تهيئة احتياطية...');
    
    try {
        // إعادة تعيين المصفوفات
        window.employees = [];
        window.filteredEmployees = [];
        
        // تحميل لوحة التحكم الأساسية
        loadDashboard();
        setupNavigation();
        
        // تحديث المكونات مع بيانات فارغة
        setTimeout(() => {
            updateStats();
            updateQuickEmployeeView();
            updateCharts();
            
            showNotification('تم تحميل النظام مع إعدادات افتراضية. يمكنك البدء بإضافة موظفين.', 'warning', 8000);
            console.log('✅ تم تحميل النظام بالوضع الاحتياطي');
        }, 500);
        
    } catch (error) {
        console.error('❌ خطأ في التهيئة الاحتياطية:', error);
        document.body.innerHTML = `
            <div style="display: flex; justify-content: center; align-items: center; height: 100vh; background: #f8f9fa;">
                <div style="text-align: center; padding: 40px; background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
                    <h2 style="color: #e74c3c; margin-bottom: 20px;">⚠️ خطأ في تحميل النظام</h2>
                    <p style="color: #666; margin-bottom: 20px;">حدث خطأ غير متوقع أثناء تحميل النظام.</p>
                    <button onclick="location.reload()" style="background: #3498db; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer;">
                        🔄 إعادة تحميل الصفحة
                    </button>
                </div>
            </div>
        `;
    }
}

/**
 * إعداد التنقل
 */
function setupNavigation() {
    console.log('🧭 إعداد التنقل...');
    
    // التأكد من وجود عناصر التنقل
    const navItems = document.querySelectorAll('.nav-item');
    if (navItems.length === 0) {
        console.warn('⚠️ لم يتم العثور على عناصر التنقل');
        return;
    }
    
    // إضافة مستمعي الأحداث للتنقل
    navItems.forEach((item, index) => {
        item.addEventListener('click', function() {
            const sections = ['dashboard', 'employees', 'leaves', 'reports', 'settings'];
            if (sections[index]) {
                showSection(sections[index]);
            }
        });
    });
    
    console.log('✅ تم إعداد التنقل');
}

// ===== اختصارات لوحة المفاتيح =====

/**
 * إعداد اختصارات لوحة المفاتيح
 */
function setupKeyboardShortcuts() {
    console.log('⌨️ إعداد اختصارات لوحة المفاتيح...');
    
    document.addEventListener('keydown', function(e) {
        // التحقق من الضغط على Ctrl
        if (e.ctrlKey) {
            switch(e.key) {
                case '1':
                    e.preventDefault();
                    showSection('dashboard');
                    break;
                case '2':
                    e.preventDefault();
                    showSection('employees');
                    break;
                case '3':
                    e.preventDefault();
                    showSection('leaves');
                    break;
                case '4':
                    e.preventDefault();
                    showSection('reports');
                    break;
                case '5':
                    e.preventDefault();
                    showSection('settings');
                    break;
                case 's':
                    e.preventDefault();
                    saveData();
                    showNotification('تم حفظ البيانات', 'success', 2000);
                    break;
                case 'e':
                    e.preventDefault();
                    if (window.currentSection === 'reports') {
                        exportToCSV();
                    }
                    break;
                case 'b':
                    e.preventDefault();
                    if (window.currentSection === 'reports') {
                        exportToJSON();
                    }
                    break;
                case 'f':
                    e.preventDefault();
                    const searchInput = document.getElementById('searchInput');
                    if (searchInput) {
                        searchInput.focus();
                        searchInput.select();
                    }
                    break;
                case 'r':
                    e.preventDefault();
                    if (window.currentSection === 'employees') {
                        clearFilters();
                    }
                    break;
                case 'n':
                    e.preventDefault();
                    if (window.currentSection === 'employees') {
                        document.getElementById('employeeName')?.focus();
                    }
                    break;
                case 'l':
                    e.preventDefault();
                    if (window.currentSection === 'leaves') {
                        document.getElementById('leaveDays')?.focus();
                    }
                    break;
                case 't':
                    e.preventDefault();
                    loadTestData();
                    break;
                case 'd':
                    e.preventDefault();
                    toggleTheme();
                    break;
                case 'm':
                    e.preventDefault();
                    toggleSidebar();
                    break;
                case '?':
                    e.preventDefault();
                    showSection('settings');
                    setTimeout(() => showKeyboardShortcuts(), 300);
                    break;
                case 'i':
                    e.preventDefault();
                    showSystemInfo();
                    break;
            }
        }
        
        // اختصارات بدون Ctrl
        switch(e.key) {
            case 'F1':
                e.preventDefault();
                showSection('settings');
                setTimeout(() => startUserGuide(), 300);
                break;
            case 'F3':
                e.preventDefault();
                if (window.currentSection === 'employees') {
                    toggleAdvancedSearch();
                }
                break;
            case 'Escape':
                // إغلاق النوافذ المنبثقة أو إلغاء التحرير
                if (editingIndex !== -1) {
                    editingIndex = -1;
                    clearForm();
                    document.getElementById('addEmployeeBtn').innerHTML = '➕ إضافة موظف';
                }
                break;
        }
    });
    
    console.log('✅ تم إعداد اختصارات لوحة المفاتيح');
}

/**
 * عرض معلومات النظام
 */
function showSystemInfo() {
    const stats = testBasicFunctionality();
    const info = `
📊 معلومات النظام:
• الإصدار: 4.0 المحدث
• عدد الموظفين: ${stats.totalEmployees}
• الرصيد المرحل الإجمالي: ${stats.totalCarriedOver}
• الإجازات المستخدمة: ${stats.totalUsed}
• التخزين المحلي: ${isLocalStorageAvailable() ? 'متاح' : 'غير متاح'}
• الوضع الليلي: ${isDarkMode ? 'مفعل' : 'غير مفعل'}
• المتصفح: ${navigator.userAgent.split(' ')[0]}
    `;
    
    showNotification(info, 'info', 10000);
}

// ===== الحفظ التلقائي =====

/**
 * إعداد الحفظ التلقائي
 */
function setupAutoSave() {
    console.log('💾 إعداد الحفظ التلقائي...');
    
    // حفظ تلقائي كل 30 ثانية
    setInterval(() => {
        if (window.employees && window.employees.length > 0) {
            saveData();
            console.log('💾 تم الحفظ التلقائي');
        }
    }, 30000);
    
    // حفظ عند إغلاق الصفحة
    window.addEventListener('beforeunload', function(e) {
        saveData();
    });
    
    // حفظ عند فقدان التركيز على النافذة
    window.addEventListener('blur', function() {
        if (window.employees && window.employees.length > 0) {
            saveData();
        }
    });
    
    console.log('✅ تم إعداد الحفظ التلقائي');
}

// ===== تهيئة النظام عند تحميل الصفحة =====

/**
 * تهيئة النظام عند تحميل DOM
 */
document.addEventListener('DOMContentLoaded', function() {
    if (isInitialized) {
        console.log('⚠️ النظام مهيأ بالفعل، تجاهل التهيئة المكررة');
        return;
    }
    
    console.log('🚀 بدء تحميل النظام...');
    isInitialized = true;
    
    try {
        // تحميل إعدادات الثيم أولاً
        loadTheme();
        console.log('✅ تم تحميل إعدادات الثيم');
        
        // تهيئة المصفوفات
        if (!window.employees) window.employees = [];
        if (!window.filteredEmployees) window.filteredEmployees = [];
        
        // تحميل البيانات
        loadData();
        console.log('✅ تم تحميل البيانات');
        
        // تهيئة التطبيق مع التسلسل المناسب
        initializeApplication();
        
    } catch (error) {
        console.error('❌ خطأ في تحميل النظام:', error);
        initializeFallback();
    }
});

// ===== معالجة الأخطاء العامة =====

/**
 * معالج الأخطاء العام
 */
window.addEventListener('error', function(e) {
    console.error('❌ خطأ عام في النظام:', e.error);
    showNotification('حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة إذا استمر المشكل.', 'danger', 10000);
});

/**
 * معالج الأخطاء غير المعالجة
 */
window.addEventListener('unhandledrejection', function(e) {
    console.error('❌ خطأ غير معالج:', e.reason);
    showNotification('حدث خطأ في معالجة البيانات. تم حفظ البيانات احتياطياً.', 'warning', 8000);
    
    // محاولة حفظ البيانات في حالة الخطأ
    try {
        saveData();
    } catch (saveError) {
        console.error('❌ فشل في حفظ البيانات الاحتياطي:', saveError);
    }
});

console.log('🔧 تم تحميل ملف التهيئة بنجاح');
