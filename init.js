/**
 * نظام إدارة إجازات الموظفين - ملف التهيئة النظيف
 * ملف التهيئة والإعداد الأساسي للنظام
 */

// ===== تهيئة التطبيق =====

/**
 * تهيئة التطبيق
 */
function initializeApplication() {
    try {
        setupNavigation();
        setupKeyboardShortcuts();
        setupAutoSave();
        loadDashboard();
        
        setTimeout(() => {
            updateStats();
            updateQuickEmployeeView();
            updateCharts();
            showNotification('🎉 مرحباً بك! تم تحميل نظام إدارة الإجازات بنجاح.', 'success', 5000);
        }, 500);
        
    } catch (error) {
        console.error('❌ خطأ في تهيئة التطبيق:', error);
        initializeFallback();
    }
}

/**
 * تهيئة احتياطية في حالة الأخطاء
 */
function initializeFallback() {
    try {
        window.employees = [];
        window.filteredEmployees = [];
        loadDashboard();
        setupNavigation();
        
        setTimeout(() => {
            updateStats();
            updateQuickEmployeeView();
            updateCharts();
            showNotification('تم تحميل النظام مع إعدادات افتراضية. يمكنك البدء بإضافة موظفين.', 'warning', 8000);
        }, 500);
        
    } catch (error) {
        console.error('❌ خطأ في التهيئة الاحتياطية:', error);
        document.body.innerHTML = `
            <div style="display: flex; justify-content: center; align-items: center; height: 100vh; background: #f8f9fa;">
                <div style="text-align: center; padding: 40px; background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
                    <h2 style="color: #e74c3c; margin-bottom: 20px;">⚠️ خطأ في تحميل النظام</h2>
                    <p style="color: #666; margin-bottom: 20px;">حدث خطأ غير متوقع أثناء تحميل النظام.</p>
                    <button onclick="location.reload()" style="background: #3498db; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer;">
                        🔄 إعادة تحميل الصفحة
                    </button>
                </div>
            </div>
        `;
    }
}

// ===== إعداد التنقل =====

/**
 * إعداد التنقل
 */
function setupNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    if (navItems.length === 0) return;
    
    const sections = ['dashboard', 'employees', 'leaves', 'reports', 'settings'];
    navItems.forEach((item, index) => {
        item.addEventListener('click', () => {
            if (sections[index]) {
                showSection(sections[index]);
            }
        });
    });
}

// ===== اختصارات لوحة المفاتيح =====

/**
 * إعداد اختصارات لوحة المفاتيح
 */
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey) {
            switch(e.key) {
                case '1':
                    e.preventDefault();
                    showSection('dashboard');
                    break;
                case '2':
                    e.preventDefault();
                    showSection('employees');
                    break;
                case '3':
                    e.preventDefault();
                    showSection('leaves');
                    break;
                case '4':
                    e.preventDefault();
                    showSection('reports');
                    break;
                case '5':
                    e.preventDefault();
                    showSection('settings');
                    break;
                case 's':
                    e.preventDefault();
                    saveData();
                    showNotification('تم حفظ البيانات', 'success', 3000);
                    break;
                case 'n':
                    e.preventDefault();
                    if (document.getElementById('employees').style.display !== 'none') {
                        document.getElementById('employeeName').focus();
                    }
                    break;
                case 'f':
                    e.preventDefault();
                    const searchInput = document.getElementById('searchInput');
                    if (searchInput) {
                        searchInput.focus();
                    }
                    break;
                case 'e':
                    e.preventDefault();
                    exportData('excel');
                    break;
                case 'p':
                    e.preventDefault();
                    exportData('pdf');
                    break;
                case 'h':
                    e.preventDefault();
                    showSystemInfo();
                    break;
            }
        }
        
        if (e.key === 'Escape') {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (modal.style.display === 'block') {
                    modal.style.display = 'none';
                }
            });
        }
    });
}

/**
 * عرض معلومات النظام
 */
function showSystemInfo() {
    const info = `
نظام إدارة إجازات الموظفين
الإصدار: 2.0 (نسخة الإنتاج النظيفة)
عدد الموظفين: ${window.employees ? window.employees.length : 0}

اختصارات لوحة المفاتيح:
Ctrl + 1: لوحة التحكم
Ctrl + 2: الموظفين
Ctrl + 3: الإجازات
Ctrl + 4: التقارير
Ctrl + 5: الإعدادات
Ctrl + S: حفظ البيانات
Ctrl + N: موظف جديد
Ctrl + F: البحث
Ctrl + E: تصدير Excel
Ctrl + P: تصدير PDF
Ctrl + H: هذه المساعدة
ESC: إغلاق النوافذ
    `;
    
    alert(info);
}

// ===== الحفظ التلقائي =====

/**
 * إعداد الحفظ التلقائي
 */
function setupAutoSave() {
    // حفظ تلقائي كل 30 ثانية
    setInterval(() => {
        if (window.employees && window.employees.length > 0) {
            saveData();
        }
    }, 30000);
    
    // حفظ عند إغلاق الصفحة
    window.addEventListener('beforeunload', function(e) {
        if (window.employees && window.employees.length > 0) {
            saveData();
        }
    });
    
    window.addEventListener('pagehide', function(e) {
        if (window.employees && window.employees.length > 0) {
            saveData();
        }
    });
}

// ===== تهيئة النظام =====

/**
 * تهيئة النظام عند تحميل DOM
 */
function initializeSystem() {
    if (window.isInitialized) return;
    
    window.isInitialized = true;

    try {
        loadTheme();
        
        if (!window.employees) window.employees = [];
        if (!window.filteredEmployees) window.filteredEmployees = [];

        loadData();
        initializeApplication();

    } catch (error) {
        console.error('❌ خطأ في تحميل النظام:', error);
        initializeFallback();
    }
}

// تشغيل التهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initializeSystem);

// ===== معالجة الأخطاء =====

/**
 * معالج الأخطاء العام
 */
window.addEventListener('error', function(e) {
    console.error('❌ خطأ عام في النظام:', e.error);
    
    const errorDetails = {
        message: e.message,
        filename: e.filename ? e.filename.split('/').pop() : 'غير معروف',
        lineno: e.lineno,
        colno: e.colno,
        stack: e.error ? e.error.stack : 'غير متاح',
        timestamp: new Date().toLocaleString('ar-SA')
    };
    
    let userMessage = 'حدث خطأ غير متوقع';
    let suggestions = [];
    
    if (errorDetails.message.includes('is not defined')) {
        userMessage = 'خطأ في تحميل مكونات النظام';
        suggestions = ['إعادة تحميل الصفحة', 'التحقق من اتصال الإنترنت'];
    } else if (errorDetails.message.includes('Cannot read property')) {
        userMessage = 'خطأ في الوصول للبيانات';
        suggestions = ['تحديث الصفحة', 'مسح cache المتصفح'];
    } else if (errorDetails.message.includes('getElementById')) {
        userMessage = 'خطأ في عناصر الواجهة';
        suggestions = ['انتظار تحميل الصفحة كاملة', 'إعادة تحميل الصفحة'];
    } else {
        suggestions = ['إعادة تحميل الصفحة', 'التحقق من console للتفاصيل'];
    }
    
    const fullMessage = `${userMessage}. الاقتراحات: ${suggestions.join(', ')}`;
    
    if (typeof showNotification === 'function') {
        showNotification(fullMessage, 'danger', 15000);
    } else {
        alert(fullMessage);
    }
    
    // حفظ الخطأ للتحليل اللاحق
    try {
        const savedErrors = JSON.parse(localStorage.getItem('systemErrors') || '[]');
        savedErrors.push(errorDetails);
        if (savedErrors.length > 10) {
            savedErrors.splice(0, savedErrors.length - 10);
        }
        localStorage.setItem('systemErrors', JSON.stringify(savedErrors));
    } catch (storageError) {
        console.warn('فشل في حفظ تفاصيل الخطأ:', storageError);
    }
});

/**
 * معالج الأخطاء غير المعالجة
 */
window.addEventListener('unhandledrejection', function(e) {
    console.error('❌ خطأ غير معالج:', e.reason);
    showNotification('حدث خطأ في معالجة البيانات. تم حفظ البيانات احتياطياً.', 'warning', 8000);
    
    try {
        saveData();
    } catch (saveError) {
        console.error('❌ فشل في حفظ البيانات الاحتياطي:', saveError);
    }
});
