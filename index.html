<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة إجازات الموظفين المتطور</title>
    
    <!-- تحميل ملفات CSS -->
    <link rel="stylesheet" href="styles.css">
    
    <!-- أيقونة الموقع -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📅</text></svg>">
</head>
<body>
    <!-- زر تبديل الوضع الليلي -->
    <button class="theme-toggle" onclick="toggleTheme()" title="تبديل الوضع الليلي">
        🌙
    </button>

    <!-- حاوية الإشعارات -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- الشريط الجانبي -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
            <div class="sidebar-title">نظام الإجازات</div>
            <div class="sidebar-subtitle">إدارة متطورة</div>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item active" onclick="showSection('dashboard')">
                <span class="nav-icon">📊</span>
                <span class="nav-text">لوحة التحكم</span>
            </div>
            <div class="nav-item" onclick="showSection('employees')">
                <span class="nav-icon">👥</span>
                <span class="nav-text">إدارة الموظفين</span>
            </div>
            <div class="nav-item" onclick="showSection('leaves')">
                <span class="nav-icon">📅</span>
                <span class="nav-text">إدارة الإجازات</span>
            </div>
            <div class="nav-item" onclick="showSection('reports')">
                <span class="nav-icon">📈</span>
                <span class="nav-text">التقارير</span>
            </div>
            <div class="nav-item" onclick="showSection('settings')">
                <span class="nav-icon">⚙️</span>
                <span class="nav-text">الإعدادات</span>
            </div>
        </nav>
    </div>

    <!-- الحاوية الرئيسية -->
    <div class="main-container" id="mainContainer">
        <!-- رأس الصفحة -->
        <div class="main-header">
            <div class="save-indicator" id="saveIndicator">محفوظ تلقائياً</div>
            <h1 class="main-title" id="pageTitle">لوحة التحكم</h1>
            <p class="main-subtitle" id="pageSubtitle">نظرة عامة على إحصائيات الإجازات</p>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="main-content">
            <div id="alertContainer"></div>
            
            <!-- قسم لوحة التحكم -->
            <div id="dashboard" class="section active">
                <!-- سيتم تحميل المحتوى هنا بواسطة JavaScript -->
            </div>

            <!-- قسم إدارة الموظفين -->
            <div id="employees" class="section">
                <!-- سيتم تحميل المحتوى هنا بواسطة JavaScript -->
            </div>

            <!-- قسم إدارة الإجازات -->
            <div id="leaves" class="section">
                <!-- سيتم تحميل المحتوى هنا بواسطة JavaScript -->
            </div>

            <!-- قسم التقارير -->
            <div id="reports" class="section">
                <!-- سيتم تحميل المحتوى هنا بواسطة JavaScript -->
            </div>

            <!-- قسم الإعدادات -->
            <div id="settings" class="section">
                <!-- سيتم تحميل المحتوى هنا بواسطة JavaScript -->
            </div>
        </div>
    </div>

    <!-- تحميل ملفات JavaScript -->
    <script src="utils.js"></script>
    <script src="core.js"></script>
    <script src="sections.js"></script>
    <script src="functions.js"></script>
    <script src="features.js"></script>
    <script src="extras.js"></script>
    <script src="app.js"></script>
    <script src="init.js"></script>
</body>
</html>
