<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 فحص الخطأ الفوري</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            margin: 20px;
            background: #f8f9fa;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .error-section {
            background: #fdf2f2;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .success-section {
            background: #f2fdf2;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .info-section {
            background: #f2f8fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #2980b9; }
        .btn.danger { background: #e74c3c; }
        .btn.success { background: #27ae60; }
        .btn.warning { background: #f39c12; }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-ok { background: #27ae60; }
        .status-error { background: #e74c3c; }
        .status-warning { background: #f39c12; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 فحص الخطأ الفوري - نظام إدارة الإجازات</h1>
        
        <div class="error-section">
            <h3>🚨 الخطأ الحالي</h3>
            <p><strong>الرسالة:</strong> "حدث خطأ غير متوقع. الاقتراحات: إعادة تحميل الصفحة, التحقق من console للتفاصيل"</p>
            <p><strong>الحالة:</strong> تم تحسين معالج الأخطاء ولكن ما زال هناك خطأ أساسي</p>
        </div>

        <div class="info-section">
            <h3>🎯 خطة الفحص الفوري</h3>
            <ol>
                <li>فحص الأخطاء المحفوظة في localStorage</li>
                <li>اختبار الوظائف الأساسية واحدة تلو الأخرى</li>
                <li>فحص تحميل الملفات</li>
                <li>تحديد مصدر الخطأ بدقة</li>
                <li>تطبيق الحل المناسب</li>
            </ol>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button class="btn success" onclick="runImmediateDiagnostic()">🚀 فحص فوري شامل</button>
            <button class="btn" onclick="checkSavedErrors()">📋 فحص الأخطاء المحفوظة</button>
            <button class="btn warning" onclick="testBasicFunctions()">⚙️ اختبار الوظائف الأساسية</button>
            <button class="btn danger" onclick="simulateCommonErrors()">🧪 محاكاة الأخطاء الشائعة</button>
        </div>

        <div id="diagnosticResults"></div>
        
        <div class="status-grid">
            <div class="status-card">
                <h4>📁 حالة الملفات</h4>
                <div id="fileStatus">جاري الفحص...</div>
            </div>
            <div class="status-card">
                <h4>⚙️ حالة الوظائف</h4>
                <div id="functionStatus">جاري الفحص...</div>
            </div>
            <div class="status-card">
                <h4>📊 حالة المتغيرات</h4>
                <div id="variableStatus">جاري الفحص...</div>
            </div>
            <div class="status-card">
                <h4>🔧 حالة النظام</h4>
                <div id="systemStatus">جاري الفحص...</div>
            </div>
        </div>

        <div id="errorDetails" class="code-block" style="display: none;">
            <h4>📋 تفاصيل الأخطاء:</h4>
            <div id="errorContent"></div>
        </div>

        <div id="solutionSection" style="display: none;">
            <div class="success-section">
                <h3>💡 الحل المقترح</h3>
                <div id="suggestedSolution"></div>
            </div>
        </div>
    </div>

    <!-- تحميل ملفات النظام للفحص -->
    <script src="utils.js"></script>
    <script src="core.js"></script>
    <script src="sections.js"></script>
    <script src="functions.js"></script>
    <script src="features.js"></script>
    <script src="extras.js"></script>
    <script src="reports-advanced.js"></script>
    <script src="settings-functions.js"></script>
    <script src="app.js"></script>
    <script src="init.js"></script>

    <script>
        let diagnosticLog = [];
        let foundErrors = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const entry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            diagnosticLog.push(entry);
            console.log(entry);
            updateErrorDisplay();
        }

        function updateErrorDisplay() {
            const errorDetails = document.getElementById('errorDetails');
            const errorContent = document.getElementById('errorContent');
            
            if (diagnosticLog.length > 0) {
                errorDetails.style.display = 'block';
                errorContent.textContent = diagnosticLog.join('\n');
            }
        }

        // تسجيل الأخطاء الجديدة
        window.addEventListener('error', function(e) {
            const error = {
                message: e.message,
                filename: e.filename ? e.filename.split('/').pop() : 'غير معروف',
                lineno: e.lineno,
                colno: e.colno,
                stack: e.error ? e.error.stack : 'غير متاح',
                timestamp: new Date().toLocaleString('ar-SA')
            };
            
            foundErrors.push(error);
            log(`خطأ JavaScript: ${error.message} في ${error.filename}:${error.lineno}`, 'error');
        });

        function checkSavedErrors() {
            log('فحص الأخطاء المحفوظة...', 'info');
            
            try {
                const savedErrors = JSON.parse(localStorage.getItem('systemErrors') || '[]');
                
                if (savedErrors.length === 0) {
                    log('لا توجد أخطاء محفوظة', 'info');
                    updateStatus('systemStatus', 'لا توجد أخطاء محفوظة', 'ok');
                } else {
                    log(`تم العثور على ${savedErrors.length} خطأ محفوظ`, 'warning');
                    
                    savedErrors.forEach((error, index) => {
                        log(`خطأ ${index + 1}: ${error.message} في ${error.filename}:${error.lineno} - ${error.timestamp}`, 'error');
                    });
                    
                    updateStatus('systemStatus', `${savedErrors.length} خطأ محفوظ`, 'error');
                    
                    // عرض الحل المقترح
                    showSolution(savedErrors);
                }
            } catch (error) {
                log(`فشل في قراءة الأخطاء المحفوظة: ${error.message}`, 'error');
            }
        }

        function testBasicFunctions() {
            log('اختبار الوظائف الأساسية...', 'info');
            
            const functions = [
                'showSection', 'addEmployee', 'editEmployee', 'deleteEmployee',
                'updateTable', 'filterEmployees', 'showAlert', 'saveData',
                'loadDataFromStorage', 'getEmployeeStatus', 'showNotification'
            ];
            
            let workingFunctions = 0;
            let brokenFunctions = [];
            
            functions.forEach(funcName => {
                try {
                    if (typeof window[funcName] === 'function') {
                        workingFunctions++;
                        log(`✅ وظيفة ${funcName} متاحة`, 'success');
                    } else {
                        brokenFunctions.push(funcName);
                        log(`❌ وظيفة ${funcName} غير متاحة`, 'error');
                    }
                } catch (error) {
                    brokenFunctions.push(funcName);
                    log(`❌ خطأ في فحص وظيفة ${funcName}: ${error.message}`, 'error');
                }
            });
            
            if (brokenFunctions.length === 0) {
                updateStatus('functionStatus', 'جميع الوظائف تعمل', 'ok');
            } else {
                updateStatus('functionStatus', `${brokenFunctions.length} وظيفة معطلة`, 'error');
                log(`الوظائف المعطلة: ${brokenFunctions.join(', ')}`, 'error');
            }
        }

        function runImmediateDiagnostic() {
            log('بدء الفحص الفوري الشامل...', 'info');
            
            // مسح السجلات السابقة
            diagnosticLog = [];
            foundErrors = [];
            
            // فحص تحميل الملفات
            checkFileLoading();
            
            setTimeout(() => {
                // فحص الوظائف
                testBasicFunctions();
                
                setTimeout(() => {
                    // فحص المتغيرات
                    checkVariables();
                    
                    setTimeout(() => {
                        // فحص الأخطاء المحفوظة
                        checkSavedErrors();
                        
                        setTimeout(() => {
                            // تحليل النتائج
                            analyzeResults();
                        }, 500);
                    }, 500);
                }, 500);
            }, 500);
        }

        function checkFileLoading() {
            log('فحص تحميل الملفات...', 'info');
            
            const requiredFiles = [
                'utils.js', 'core.js', 'sections.js', 'functions.js',
                'features.js', 'extras.js', 'reports-advanced.js',
                'settings-functions.js', 'app.js', 'init.js'
            ];
            
            const scripts = Array.from(document.scripts);
            const loadedFiles = scripts.map(script => {
                const src = script.src;
                return src ? src.split('/').pop() : null;
            }).filter(Boolean);
            
            let missingFiles = [];
            requiredFiles.forEach(file => {
                if (loadedFiles.includes(file)) {
                    log(`✅ ملف ${file} محمل`, 'success');
                } else {
                    missingFiles.push(file);
                    log(`❌ ملف ${file} مفقود`, 'error');
                }
            });
            
            if (missingFiles.length === 0) {
                updateStatus('fileStatus', 'جميع الملفات محملة', 'ok');
            } else {
                updateStatus('fileStatus', `${missingFiles.length} ملف مفقود`, 'error');
            }
        }

        function checkVariables() {
            log('فحص المتغيرات الأساسية...', 'info');
            
            const variables = [
                { name: 'employees', type: 'array' },
                { name: 'filteredEmployees', type: 'array' },
                { name: 'editingIndex', type: 'number' }
            ];
            
            let missingVars = [];
            variables.forEach(variable => {
                const value = window[variable.name];
                if (value !== undefined) {
                    const actualType = Array.isArray(value) ? 'array' : typeof value;
                    if (actualType === variable.type || (variable.type === 'array' && Array.isArray(value))) {
                        log(`✅ متغير ${variable.name} موجود ونوعه صحيح (${actualType})`, 'success');
                    } else {
                        log(`⚠️ متغير ${variable.name} نوعه خاطئ - متوقع: ${variable.type}, فعلي: ${actualType}`, 'warning');
                    }
                } else {
                    missingVars.push(variable.name);
                    log(`❌ متغير ${variable.name} غير معرف`, 'error');
                }
            });
            
            if (missingVars.length === 0) {
                updateStatus('variableStatus', 'جميع المتغيرات صحيحة', 'ok');
            } else {
                updateStatus('variableStatus', `${missingVars.length} متغير مفقود`, 'error');
            }
        }

        function updateStatus(elementId, message, status) {
            const element = document.getElementById(elementId);
            const statusClass = status === 'ok' ? 'status-ok' : 
                              status === 'error' ? 'status-error' : 'status-warning';
            
            element.innerHTML = `<span class="status-indicator ${statusClass}"></span>${message}`;
        }

        function analyzeResults() {
            log('تحليل النتائج...', 'info');
            
            const errorCount = diagnosticLog.filter(entry => entry.includes('ERROR')).length;
            const warningCount = diagnosticLog.filter(entry => entry.includes('WARNING')).length;
            
            if (errorCount === 0) {
                log('✅ لم يتم العثور على أخطاء في الفحص الحالي', 'success');
                showSolution([]);
            } else {
                log(`❌ تم العثور على ${errorCount} خطأ و ${warningCount} تحذير`, 'error');
                showSolution(foundErrors);
            }
        }

        function showSolution(errors) {
            const solutionSection = document.getElementById('solutionSection');
            const suggestedSolution = document.getElementById('suggestedSolution');
            
            let solution = '';
            
            if (errors.length === 0) {
                solution = `
                    <h4>✅ النظام يبدو سليماً في الفحص الحالي</h4>
                    <p>الخطأ قد يكون:</p>
                    <ul>
                        <li>خطأ مؤقت تم حله تلقائياً</li>
                        <li>مشكلة في cache المتصفح</li>
                        <li>تضارب مؤقت في تحميل الملفات</li>
                    </ul>
                    <h5>الحلول المقترحة:</h5>
                    <ol>
                        <li>مسح cache المتصفح (Ctrl + Shift + Delete)</li>
                        <li>إعادة تحميل الصفحة (F5)</li>
                        <li>إعادة تشغيل المتصفح</li>
                        <li>التحقق من console عند حدوث الخطأ مرة أخرى</li>
                    </ol>
                `;
            } else {
                const commonErrors = errors.map(e => e.message).join(', ');
                solution = `
                    <h4>❌ تم تحديد مصدر المشكلة</h4>
                    <p><strong>الأخطاء المكتشفة:</strong> ${commonErrors}</p>
                    <h5>الحلول المقترحة:</h5>
                    <ol>
                        <li>إعادة ترتيب تحميل ملفات JavaScript</li>
                        <li>التحقق من وجود جميع الملفات المطلوبة</li>
                        <li>فحص أخطاء syntax في الملفات</li>
                        <li>مسح localStorage وإعادة التحميل</li>
                    </ol>
                `;
            }
            
            suggestedSolution.innerHTML = solution;
            solutionSection.style.display = 'block';
        }

        function simulateCommonErrors() {
            log('محاكاة الأخطاء الشائعة للاختبار...', 'info');
            
            try {
                // محاكاة خطأ في استدعاء وظيفة غير موجودة
                nonExistentFunction();
            } catch (error) {
                log(`خطأ محاكى: ${error.message}`, 'error');
            }
            
            try {
                // محاكاة خطأ في الوصول لخاصية غير موجودة
                const obj = null;
                obj.property;
            } catch (error) {
                log(`خطأ محاكى: ${error.message}`, 'error');
            }
        }

        // تشغيل فحص أولي عند التحميل
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('تم تحميل أداة الفحص الفوري', 'info');
                log('اضغط على "فحص فوري شامل" لبدء التشخيص', 'info');
            }, 1000);
        });

        console.log('🔍 تم تحميل أداة الفحص الفوري');
    </script>
</body>
</html>
