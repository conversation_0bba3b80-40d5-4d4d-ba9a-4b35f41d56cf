<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام حساب إجازات الموظفين المتطور</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .save-indicator {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
        }
        
        .save-indicator.saving {
            background: rgba(255,193,7,0.8);
            color: #000;
        }
        
        .save-indicator.saved {
            background: rgba(40,167,69,0.8);
            color: #fff;
        }
        
        .controls {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: end;
        }
        
        .form-group {
            flex: 1;
            min-width: 200px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        .table-container {
            padding: 30px;
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        th {
            background: linear-gradient(135deg, #34495e, #2c3e50);
            color: white;
            padding: 15px;
            font-weight: 600;
            text-align: center;
        }
        
        td {
            padding: 12px 15px;
            border-bottom: 1px solid #ecf0f1;
            text-align: center;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .leave-balance {
            font-weight: bold;
        }
        
        .balance-positive {
            color: #27ae60;
        }
        
        .balance-negative {
            color: #e74c3c;
        }
        
        .balance-warning {
            color: #f39c12;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            text-align: center;
        }
        
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        
        .stat-card .number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }
        
        .export-section {
            padding: 30px;
            text-align: center;
            background: #ecf0f1;
        }
        
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            font-weight: 600;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }
        
        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
        
        .modal h2 {
            color: #2c3e50;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .leave-history {
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        
        .leave-record {
            padding: 15px;
            border-bottom: 1px solid #f1f1f1;
        }
        
        .leave-record:last-child {
            border-bottom: none;
        }
        
        .leave-record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .delete-leave {
            background: #dc3545;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .backup-controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .file-input {
            display: none;
        }
        
        .file-input-label {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            display: inline-block;
        }
        
        .file-input-label:hover {
            transform: translateY(-2px);
        }

        /* Enhanced validation styles */
        .form-group input.invalid {
            border-color: #e74c3c !important;
            box-shadow: 0 0 5px rgba(231, 76, 60, 0.3);
        }

        .form-group input.warning {
            border-color: #f39c12 !important;
            box-shadow: 0 0 5px rgba(243, 156, 18, 0.3);
        }

        .form-group input.valid {
            border-color: #27ae60 !important;
            box-shadow: 0 0 5px rgba(39, 174, 96, 0.3);
        }

        /* Search and filter enhancements */
        #searchInput {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/></svg>');
            background-repeat: no-repeat;
            background-position: 10px center;
            padding-left: 40px;
        }

        .filter-active {
            background-color: #e3f2fd !important;
            border-color: #2196f3 !important;
        }

        /* Carried over leave styling */
        .carried-over-info {
            background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
            border: 1px solid #c8e6c9;
            border-radius: 8px;
            padding: 12px;
            margin: 10px 0;
            font-size: 13px;
            color: #2d5a2d;
        }

        .carried-over-highlight {
            background-color: #e8f5e8 !important;
            font-weight: 600;
        }

        /* Table responsive improvements */
        @media (max-width: 1200px) {
            .table-container {
                overflow-x: auto;
            }

            table {
                min-width: 1200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="save-indicator" id="saveIndicator">محفوظ تلقائياً</div>
            <h1>🏢 نظام حساب إجازات الموظفين المتطور</h1>
            <p>إدارة احترافية لإجازات الموظفين مع الحفظ التلقائي</p>
        </div>
        
        <div class="controls">
            <div class="form-row">
                <div class="form-group">
                    <label>اسم الموظف *</label>
                    <input type="text" id="employeeName" placeholder="أدخل اسم الموظف" required
                           oninput="validateTextInput(this, /^[\u0600-\u06FFa-zA-Z\s]+$/, 'اسم الموظف يجب أن يحتوي على أحرف عربية أو إنجليزية فقط')"
                           title="أدخل اسم الموظف (أحرف عربية أو إنجليزية فقط)">
                </div>
                <div class="form-group">
                    <label>الرقم الوظيفي *</label>
                    <input type="text" id="employeeId" placeholder="أدخل الرقم الوظيفي" required
                           oninput="validateTextInput(this, /^[a-zA-Z0-9]+$/, 'الرقم الوظيفي يجب أن يحتوي على أرقام وأحرف إنجليزية فقط')"
                           title="أدخل الرقم الوظيفي (أرقام وأحرف إنجليزية فقط)">
                </div>
                <div class="form-group">
                    <label>القسم *</label>
                    <input type="text" id="department" placeholder="أدخل القسم" required
                           oninput="validateTextInput(this, /^[\u0600-\u06FFa-zA-Z\s]+$/, 'اسم القسم يجب أن يحتوي على أحرف عربية أو إنجليزية فقط')"
                           title="أدخل اسم القسم (أحرف عربية أو إنجليزية فقط)">
                </div>
                <div class="form-group">
                    <label>تاريخ التوظيف *</label>
                    <input type="date" id="hireDate" required
                           onchange="validateDateInput(this, false)"
                           title="أدخل تاريخ التوظيف">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label>الرصيد السنوي (يوم)</label>
                    <input type="number" id="annualLeave" value="30" min="0" max="365" step="1"
                           oninput="validateNumberInput(this, 0, 365)"
                           title="أدخل رقم بين 0 و 365">
                </div>
                <div class="form-group">
                    <label>الرصيد المرحل (يوم)</label>
                    <input type="number" id="carriedOverLeave" value="0" min="0" max="365" step="1"
                           oninput="validateNumberInput(this, 0, 365)"
                           title="أدخل رقم بين 0 و 365 - الرصيد المتبقي من السنوات السابقة">
                </div>
                <div class="form-group">
                    <label>إجازة مرضية (يوم)</label>
                    <input type="number" id="sickLeave" value="15" min="0" max="90" step="1"
                           oninput="validateNumberInput(this, 0, 90)"
                           title="أدخل رقم بين 0 و 90">
                </div>
                <div class="form-group">
                    <label>إجازة طارئة (يوم)</label>
                    <input type="number" id="emergencyLeave" value="5" min="0" max="30" step="1"
                           oninput="validateNumberInput(this, 0, 30)"
                           title="أدخل رقم بين 0 و 30">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <button class="btn" onclick="addEmployee()" id="addEmployeeBtn">إضافة موظف</button>
                </div>
                <div class="form-group">
                    <div style="padding: 10px; background: #e8f5e8; border-radius: 5px; font-size: 13px; color: #2d5a2d;">
                        💡 <strong>الرصيد المرحل:</strong> أيام الإجازة المتبقية من السنوات السابقة والتي لم تُستخدم
                    </div>
                </div>
            </div>
        </div>
        
        <div id="alertContainer"></div>

        <!-- Search and Filter Section -->
        <div class="controls" style="border-bottom: none; padding-bottom: 15px;">
            <div class="form-row">
                <div class="form-group">
                    <label>🔍 البحث في الموظفين</label>
                    <input type="text" id="searchInput" placeholder="ابحث بالاسم، الرقم الوظيفي، أو القسم..."
                           oninput="filterEmployees()" title="ابحث في بيانات الموظفين">
                </div>
                <div class="form-group">
                    <label>📊 فلترة حسب الحالة</label>
                    <select id="statusFilter" onchange="filterEmployees()">
                        <option value="">جميع الحالات</option>
                        <option value="طبيعي">طبيعي</option>
                        <option value="رصيد منخفض">رصيد منخفض</option>
                        <option value="نفد الرصيد">نفد الرصيد</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>🏢 فلترة حسب القسم</label>
                    <select id="departmentFilter" onchange="filterEmployees()">
                        <option value="">جميع الأقسام</option>
                    </select>
                </div>
                <div class="form-group">
                    <button class="btn btn-warning" onclick="clearFilters()" title="مسح جميع الفلاتر">🗑️ مسح الفلاتر</button>
                </div>
            </div>
            <div style="margin-top: 10px; font-size: 14px; color: #666;">
                <span id="filterResults">عرض جميع الموظفين</span>
            </div>
        </div>

        <div class="table-container">
            <table id="employeeTable">
                <thead>
                    <tr>
                        <th>اسم الموظف</th>
                        <th>الرقم الوظيفي</th>
                        <th>القسم</th>
                        <th>تاريخ التوظيف</th>
                        <th>سنوات الخدمة</th>
                        <th>الرصيد السنوي</th>
                        <th>الرصيد المرحل</th>
                        <th>إجمالي المتاح</th>
                        <th>المستخدم</th>
                        <th>المتبقي</th>
                        <th>إجازة مرضية</th>
                        <th>إجازة طارئة</th>
                        <th>الحالة</th>
                        <th>إجراءات</th>
                    </tr>
                </thead>
                <tbody id="employeeTableBody">
                </tbody>
            </table>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <h3>إجمالي الموظفين</h3>
                <div class="number" id="totalEmployees">0</div>
            </div>
            <div class="stat-card">
                <h3>إجمالي الرصيد المرحل</h3>
                <div class="number" id="totalCarriedOver">0</div>
            </div>
            <div class="stat-card">
                <h3>إجمالي الإجازات المستخدمة</h3>
                <div class="number" id="totalUsedLeaves">0</div>
            </div>
            <div class="stat-card">
                <h3>إجمالي الإجازات المتبقية</h3>
                <div class="number" id="totalRemainingLeaves">0</div>
            </div>
            <div class="stat-card">
                <h3>متوسط الاستخدام</h3>
                <div class="number" id="averageUsage">0%</div>
            </div>
        </div>
        
        <div class="export-section">
            <h3>إدارة البيانات</h3>
            <div class="backup-controls">
                <button class="btn btn-success" onclick="exportToCSV()">📊 تصدير CSV</button>
                <button class="btn btn-success" onclick="exportToJSON()">💾 تصدير نسخة احتياطية</button>
                <label for="importFile" class="file-input-label">📁 استيراد نسخة احتياطية</label>
                <input type="file" id="importFile" class="file-input" accept=".json" onchange="importData()">
                <button class="btn btn-warning" onclick="resetData()">🔄 إعادة تعيين</button>
                <button class="btn btn-danger" onclick="clearLocalStorage()">🗂️ مسح التخزين المحلي</button>
                <button class="btn btn-danger" onclick="clearAllData()">🗑️ مسح الكل</button>
            </div>

            <h3 style="margin-top: 30px;">🧪 البيانات التجريبية والاختبار</h3>
            <div class="backup-controls">
                <button class="btn" onclick="loadTestData()" style="background: linear-gradient(135deg, #9c27b0, #673ab7);">🎯 تحميل بيانات تجريبية</button>
                <button class="btn" onclick="runComprehensiveTest()" style="background: linear-gradient(135deg, #ff9800, #f57c00);">🧪 اختبار شامل</button>
                <button class="btn" onclick="window.open('', '_blank').document.write('<pre>' + JSON.stringify({employees: employees, stats: {totalEmployees: employees.length, totalCarriedOver: employees.reduce((s,e) => s + e.carriedOverLeave, 0)}}, null, 2) + '</pre>')" style="background: linear-gradient(135deg, #607d8b, #455a64);">📋 عرض البيانات JSON</button>
            </div>

            <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 8px; font-size: 14px; color: #666;">
                💡 <strong>ملاحظة:</strong> يتم حفظ البيانات تلقائياً في التخزين المحلي للمتصفح. البيانات ستبقى محفوظة حتى بعد إغلاق المتصفح.
            </div>

            <div style="margin-top: 10px; padding: 10px; background: #e3f2fd; border-radius: 8px; font-size: 13px; color: #1565c0;">
                🧪 <strong>البيانات التجريبية تشمل:</strong> 8 موظفين من أقسام مختلفة، أسماء عربية وإنجليزية، قيم متنوعة للرصيد المرحل، حالات مختلفة (طبيعي/منخفض/نفد الرصيد)، سجلات إجازات شاملة.
            </div>
        </div>
    </div>

    <!-- Leave Management Modal -->
    <div id="leaveModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeLeaveModal()">&times;</span>
            <h2>إدارة إجازات: <span id="modalEmployeeName"></span></h2>
            
            <div class="form-row">
                <div class="form-group">
                    <label>نوع الإجازة</label>
                    <select id="leaveType">
                        <option value="annual">إجازة سنوية</option>
                        <option value="sick">إجازة مرضية</option>
                        <option value="emergency">إجازة طارئة</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>عدد الأيام</label>
                    <input type="number" id="leaveDays" min="0.5" max="365" step="0.5"
                           placeholder="أدخل عدد الأيام"
                           oninput="validateNumberInput(this, 0.5, 365)"
                           title="أدخل رقم بين 0.5 و 365">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label>تاريخ البداية</label>
                    <input type="date" id="leaveStartDate"
                           onchange="validateDateInput(this, true); calculateLeaveDays()"
                           title="أدخل تاريخ بداية الإجازة">
                </div>
                <div class="form-group">
                    <label>تاريخ النهاية</label>
                    <input type="date" id="leaveEndDate"
                           onchange="validateDateInput(this, true); calculateLeaveDays()"
                           title="أدخل تاريخ نهاية الإجازة">
                </div>
            </div>
            
            <div class="form-group">
                <label>السبب / الملاحظات</label>
                <input type="text" id="leaveReason" placeholder="أدخل سبب الإجازة (اختياري)">
            </div>
            
            <div style="text-align: center; margin: 20px 0;">
                <button class="btn btn-success" onclick="addLeaveRecord()">إضافة الإجازة</button>
                <button class="btn" onclick="closeLeaveModal()">إلغاء</button>
            </div>
            
            <div class="leave-history" id="leaveHistory">
                <h4 style="text-align: center; padding: 15px; margin: 0; background: #f8f9fa;">سجل الإجازات</h4>
                <div id="leaveHistoryContent"></div>
            </div>
        </div>
    </div>

    <script>
        let employees = [];
        let filteredEmployees = [];
        let editingIndex = -1;
        let currentEmployeeIndex = -1;

        // Enhanced auto-save functionality with localStorage
        function saveData() {
            try {
                const data = {
                    employees: employees,
                    lastSaved: new Date().toISOString(),
                    version: '2.1'
                };

                // Save to localStorage
                localStorage.setItem('employeeLeaveSystem', JSON.stringify(data));

                // Create a JSON string for export
                const jsonData = JSON.stringify(data, null, 2);

                showSaveIndicator('saving');

                setTimeout(() => {
                    showSaveIndicator('saved');
                }, 500);

                return jsonData;
            } catch (error) {
                console.error('Error saving data:', error);
                showAlert('خطأ في حفظ البيانات: ' + error.message, 'danger');
                return null;
            }
        }

        // Load data from localStorage
        function loadDataFromStorage() {
            try {
                const savedData = localStorage.getItem('employeeLeaveSystem');
                if (savedData) {
                    const data = JSON.parse(savedData);

                    // Validate data structure
                    if (data.employees && Array.isArray(data.employees)) {
                        employees = data.employees;

                        // Ensure all employees have required properties
                        employees = employees.map(emp => ({
                            name: emp.name || '',
                            id: emp.id || '',
                            department: emp.department || '',
                            hireDate: emp.hireDate || new Date().toISOString().split('T')[0],
                            annualLeave: emp.annualLeave || 30,
                            carriedOverLeave: emp.carriedOverLeave || 0,
                            sickLeave: emp.sickLeave || 15,
                            emergencyLeave: emp.emergencyLeave || 5,
                            usedAnnual: emp.usedAnnual || 0,
                            usedSick: emp.usedSick || 0,
                            usedEmergency: emp.usedEmergency || 0,
                            leaveHistory: emp.leaveHistory || []
                        }));

                        console.log(`تم تحميل ${employees.length} موظف من التخزين المحلي`);
                        showAlert(`تم استرجاع بيانات ${employees.length} موظف من التخزين المحلي`, 'success');
                        return true;
                    }
                }
                return false;
            } catch (error) {
                console.error('Error loading data from localStorage:', error);
                showAlert('خطأ في تحميل البيانات المحفوظة: ' + error.message, 'warning');
                return false;
            }
        }

        // Clear localStorage
        function clearLocalStorage() {
            try {
                localStorage.removeItem('employeeLeaveSystem');
                showAlert('تم مسح البيانات المحفوظة محلياً', 'success');
            } catch (error) {
                console.error('Error clearing localStorage:', error);
                showAlert('خطأ في مسح البيانات المحفوظة', 'danger');
            }
        }

        // Check localStorage availability
        function isLocalStorageAvailable() {
            try {
                const test = 'test';
                localStorage.setItem(test, test);
                localStorage.removeItem(test);
                return true;
            } catch (error) {
                return false;
            }
        }

        // Security functions for data sanitization
        function sanitizeInput(input) {
            if (typeof input !== 'string') return input;

            // Remove HTML tags and dangerous characters
            return input
                .replace(/[<>]/g, '') // Remove < and >
                .replace(/javascript:/gi, '') // Remove javascript: protocol
                .replace(/on\w+=/gi, '') // Remove event handlers like onclick=
                .replace(/&/g, '&amp;') // Escape ampersand
                .replace(/"/g, '&quot;') // Escape quotes
                .replace(/'/g, '&#x27;') // Escape single quotes
                .trim(); // Remove leading/trailing whitespace
        }

        function sanitizeEmployee(employee) {
            return {
                name: sanitizeInput(employee.name),
                id: sanitizeInput(employee.id),
                department: sanitizeInput(employee.department),
                hireDate: employee.hireDate, // Date inputs are safe
                annualLeave: parseInt(employee.annualLeave) || 0,
                carriedOverLeave: parseInt(employee.carriedOverLeave) || 0,
                sickLeave: parseInt(employee.sickLeave) || 0,
                emergencyLeave: parseInt(employee.emergencyLeave) || 0,
                usedAnnual: parseFloat(employee.usedAnnual) || 0,
                usedSick: parseFloat(employee.usedSick) || 0,
                usedEmergency: parseFloat(employee.usedEmergency) || 0,
                leaveHistory: employee.leaveHistory ? employee.leaveHistory.map(record => ({
                    id: record.id,
                    type: sanitizeInput(record.type),
                    typeName: sanitizeInput(record.typeName),
                    days: parseFloat(record.days) || 0,
                    startDate: record.startDate,
                    endDate: record.endDate,
                    reason: sanitizeInput(record.reason),
                    addedDate: record.addedDate
                })) : []
            };
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Enhanced validation functions
        function validateNumberInput(input, min, max) {
            const value = parseFloat(input.value);

            // Remove any invalid characters
            input.value = input.value.replace(/[^0-9.-]/g, '');

            if (isNaN(value)) {
                input.style.borderColor = '#e74c3c';
                return false;
            }

            if (value < min || value > max) {
                input.style.borderColor = '#f39c12';
                input.title = `القيمة يجب أن تكون بين ${min} و ${max}`;
                return false;
            }

            input.style.borderColor = '#27ae60';
            input.title = '';
            return true;
        }

        function validateDateInput(dateInput, allowFuture = false) {
            const inputDate = new Date(dateInput.value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (isNaN(inputDate.getTime())) {
                dateInput.style.borderColor = '#e74c3c';
                return false;
            }

            if (!allowFuture && inputDate > today) {
                dateInput.style.borderColor = '#f39c12';
                dateInput.title = 'التاريخ لا يمكن أن يكون في المستقبل';
                return false;
            }

            // Check if date is too far in the past (more than 50 years)
            const fiftyYearsAgo = new Date();
            fiftyYearsAgo.setFullYear(fiftyYearsAgo.getFullYear() - 50);

            if (inputDate < fiftyYearsAgo) {
                dateInput.style.borderColor = '#f39c12';
                dateInput.title = 'التاريخ قديم جداً';
                return false;
            }

            dateInput.style.borderColor = '#27ae60';
            dateInput.title = '';
            return true;
        }

        function validateTextInput(input, pattern, errorMessage) {
            const value = input.value.trim();

            if (!value) {
                input.style.borderColor = '#e74c3c';
                return false;
            }

            if (pattern && !pattern.test(value)) {
                input.style.borderColor = '#f39c12';
                input.title = errorMessage;
                return false;
            }

            input.style.borderColor = '#27ae60';
            input.title = '';
            return true;
        }

        // Search and Filter Functions
        function filterEmployees() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
            const statusFilter = document.getElementById('statusFilter').value;
            const departmentFilter = document.getElementById('departmentFilter').value;

            filteredEmployees = employees.filter(employee => {
                // Search filter
                const matchesSearch = !searchTerm ||
                    employee.name.toLowerCase().includes(searchTerm) ||
                    employee.id.toLowerCase().includes(searchTerm) ||
                    employee.department.toLowerCase().includes(searchTerm);

                // Status filter
                const employeeStatus = getEmployeeStatus(employee);
                const matchesStatus = !statusFilter || employeeStatus === statusFilter;

                // Department filter
                const matchesDepartment = !departmentFilter || employee.department === departmentFilter;

                return matchesSearch && matchesStatus && matchesDepartment;
            });

            updateTable();
            updateFilterResults();
        }

        function updateFilterResults() {
            const totalEmployees = employees.length;
            const filteredCount = filteredEmployees.length;
            const resultsElement = document.getElementById('filterResults');

            if (filteredCount === totalEmployees) {
                resultsElement.textContent = `عرض جميع الموظفين (${totalEmployees})`;
            } else {
                resultsElement.textContent = `عرض ${filteredCount} من أصل ${totalEmployees} موظف`;
            }
        }

        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('departmentFilter').value = '';
            filteredEmployees = [...employees];
            updateTable();
            updateFilterResults();
        }

        function updateDepartmentFilter() {
            const departmentFilter = document.getElementById('departmentFilter');
            const currentValue = departmentFilter.value;

            // Get unique departments
            const departments = [...new Set(employees.map(emp => emp.department))].sort();

            // Clear existing options except the first one
            departmentFilter.innerHTML = '<option value="">جميع الأقسام</option>';

            // Add department options
            departments.forEach(dept => {
                const option = document.createElement('option');
                option.value = dept;
                option.textContent = dept;
                departmentFilter.appendChild(option);
            });

            // Restore previous selection if it still exists
            if (departments.includes(currentValue)) {
                departmentFilter.value = currentValue;
            }
        }

        function showSaveIndicator(status) {
            const indicator = document.getElementById('saveIndicator');
            indicator.className = 'save-indicator';
            
            if (status === 'saving') {
                indicator.className += ' saving';
                indicator.textContent = 'جاري الحفظ...';
            } else if (status === 'saved') {
                indicator.className += ' saved';
                indicator.textContent = 'تم الحفظ ✓';
                setTimeout(() => {
                    indicator.className = 'save-indicator';
                    indicator.textContent = 'محفوظ تلقائياً';
                }, 2000);
            }
        }

        // Enhanced load data function
        function loadData() {
            // Check if localStorage is available
            if (!isLocalStorageAvailable()) {
                showAlert('التخزين المحلي غير متاح في هذا المتصفح', 'warning');
                employees = [];
                updateTable();
                updateStats();
                return;
            }

            // Try to load from localStorage first
            const loaded = loadDataFromStorage();

            if (!loaded) {
                // Initialize with empty data if no saved data
                employees = [];
                showAlert('لا توجد بيانات محفوظة مسبقاً. ابدأ بإضافة الموظفين', 'info');
            }

            // Initialize filters
            filteredEmployees = [...employees];
            updateDepartmentFilter();
            updateTable();
            updateStats();
            updateFilterResults();
        }

        // Enhanced alert system
        function showAlert(message, type = 'success') {
            const alertContainer = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            
            const icon = type === 'success' ? '✅' : type === 'warning' ? '⚠️' : type === 'danger' ? '❌' : 'ℹ️';
            alert.innerHTML = `${icon} ${message}`;
            
            alertContainer.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 4000);
        }

        // Enhanced employee addition with security
        function addEmployee() {
            const name = sanitizeInput(document.getElementById('employeeName').value.trim());
            const id = sanitizeInput(document.getElementById('employeeId').value.trim());
            const department = sanitizeInput(document.getElementById('department').value.trim());
            const hireDate = document.getElementById('hireDate').value;
            const annualLeave = parseInt(document.getElementById('annualLeave').value) || 0;
            const carriedOverLeave = parseInt(document.getElementById('carriedOverLeave').value) || 0;
            const sickLeave = parseInt(document.getElementById('sickLeave').value) || 0;
            const emergencyLeave = parseInt(document.getElementById('emergencyLeave').value) || 0;

            // Enhanced validation
            if (!name || !id || !department || !hireDate) {
                showAlert('يرجى ملء جميع الحقول المطلوبة المميزة بـ *', 'warning');
                return;
            }

            // Validate name (only letters, spaces, and Arabic characters)
            if (!/^[\u0600-\u06FFa-zA-Z\s]+$/.test(name)) {
                showAlert('اسم الموظف يجب أن يحتوي على أحرف عربية أو إنجليزية فقط', 'warning');
                return;
            }

            // Validate employee ID (alphanumeric only)
            if (!/^[a-zA-Z0-9]+$/.test(id)) {
                showAlert('الرقم الوظيفي يجب أن يحتوي على أرقام وأحرف إنجليزية فقط', 'warning');
                return;
            }

            // Check if employee ID already exists (except when editing)
            if (editingIndex === -1 && employees.some(emp => emp.id === id)) {
                showAlert('الرقم الوظيفي موجود مسبقاً. يرجى استخدام رقم مختلف', 'warning');
                return;
            }

            // Check hire date is not in future
            if (new Date(hireDate) > new Date()) {
                showAlert('تاريخ التوظيف لا يمكن أن يكون في المستقبل', 'warning');
                return;
            }

            // Validate leave values (must be positive)
            if (annualLeave < 0 || carriedOverLeave < 0 || sickLeave < 0 || emergencyLeave < 0) {
                showAlert('قيم الإجازات يجب أن تكون أرقام موجبة', 'warning');
                return;
            }

            // Validate reasonable leave limits
            if (annualLeave > 365 || carriedOverLeave > 365 || sickLeave > 90 || emergencyLeave > 30) {
                showAlert('قيم الإجازات تبدو غير منطقية. يرجى المراجعة', 'warning');
                return;
            }

            // Validate carried over leave logic
            if (carriedOverLeave > annualLeave * 2) {
                showAlert('الرصيد المرحل لا يمكن أن يتجاوز ضعف الرصيد السنوي', 'warning');
                return;
            }

            const employee = sanitizeEmployee({
                name,
                id,
                department,
                hireDate,
                annualLeave,
                carriedOverLeave,
                sickLeave,
                emergencyLeave,
                usedAnnual: 0,
                usedSick: 0,
                usedEmergency: 0,
                leaveHistory: []
            });

            if (editingIndex === -1) {
                employees.push(employee);
                showAlert(`تم إضافة الموظف ${escapeHtml(name)} بنجاح`, 'success');
            } else {
                // Preserve leave history when editing
                employee.leaveHistory = employees[editingIndex].leaveHistory || [];
                employees[editingIndex] = employee;
                showAlert(`تم تحديث بيانات الموظف ${escapeHtml(name)} بنجاح`, 'success');
                editingIndex = -1;
                document.getElementById('addEmployeeBtn').textContent = 'إضافة موظف';
            }

            clearForm();
            updateDepartmentFilter();
            filterEmployees();
            updateStats();
            saveData();
        }

        function clearForm() {
            document.getElementById('employeeName').value = '';
            document.getElementById('employeeId').value = '';
            document.getElementById('department').value = '';
            document.getElementById('hireDate').value = '';
            document.getElementById('annualLeave').value = '30';
            document.getElementById('carriedOverLeave').value = '0';
            document.getElementById('sickLeave').value = '15';
            document.getElementById('emergencyLeave').value = '5';
        }

        function calculateYearsOfService(hireDate) {
            const today = new Date();
            const hire = new Date(hireDate);
            const years = (today - hire) / (365.25 * 24 * 60 * 60 * 1000);
            return Math.max(0, Math.floor(years));
        }

        function getEmployeeStatus(employee) {
            const totalAvailable = employee.annualLeave + employee.carriedOverLeave;
            const remainingTotal = totalAvailable - employee.usedAnnual;
            if (remainingTotal <= 0) return 'نفد الرصيد';
            if (remainingTotal <= 5) return 'رصيد منخفض';
            return 'طبيعي';
        }

        function updateTable() {
            const tbody = document.getElementById('employeeTableBody');
            tbody.innerHTML = '';

            // Use filtered employees if filters are active, otherwise use all employees
            const employeesToShow = filteredEmployees.length > 0 || hasActiveFilters() ? filteredEmployees : employees;

            employeesToShow.forEach((employee, filteredIndex) => {
                // Find the original index in the employees array
                const originalIndex = employees.findIndex(emp => emp.id === employee.id);
                const row = tbody.insertRow();
                const yearsOfService = calculateYearsOfService(employee.hireDate);
                const totalAvailable = employee.annualLeave + employee.carriedOverLeave;
                const remainingTotal = totalAvailable - employee.usedAnnual;
                const remainingSick = employee.sickLeave - employee.usedSick;
                const remainingEmergency = employee.emergencyLeave - employee.usedEmergency;
                const status = getEmployeeStatus(employee);

                let statusClass = 'balance-positive';
                if (remainingTotal <= 0) statusClass = 'balance-negative';
                else if (remainingTotal <= 5) statusClass = 'balance-warning';

                // Create cells safely using textContent for user data
                const cells = [
                    { content: employee.name, isText: true },
                    { content: employee.id, isText: true },
                    { content: employee.department, isText: true },
                    { content: new Date(employee.hireDate).toLocaleDateString('ar-SA'), isText: true },
                    { content: yearsOfService, isText: true },
                    { content: employee.annualLeave, isText: true },
                    { content: employee.carriedOverLeave, isText: true, className: 'leave-balance balance-positive' },
                    { content: totalAvailable, isText: true, className: 'leave-balance balance-positive' },
                    { content: employee.usedAnnual, isText: true },
                    { content: remainingTotal, isText: true, className: `leave-balance ${statusClass}` },
                    { content: remainingSick, isText: true, className: `leave-balance ${remainingSick <= 0 ? 'balance-negative' : remainingSick <= 2 ? 'balance-warning' : 'balance-positive'}` },
                    { content: remainingEmergency, isText: true, className: `leave-balance ${remainingEmergency <= 0 ? 'balance-negative' : 'balance-positive'}` },
                    { content: status, isText: true, className: statusClass }
                ];

                // Add data cells
                cells.forEach(cellData => {
                    const cell = row.insertCell();
                    if (cellData.isText) {
                        cell.textContent = cellData.content;
                    } else {
                        cell.innerHTML = cellData.content;
                    }
                    if (cellData.className) {
                        cell.className = cellData.className;
                    }
                });

                // Add action buttons cell (safe HTML) - use original index
                const actionsCell = row.insertCell();
                actionsCell.innerHTML = `
                    <button class="btn btn-small" onclick="editEmployee(${originalIndex})" title="تعديل البيانات">✏️ تعديل</button>
                    <button class="btn btn-success btn-small" onclick="openLeaveModal(${originalIndex})" title="إدارة الإجازات">📅 إجازات</button>
                    <button class="btn btn-danger btn-small" onclick="deleteEmployee(${originalIndex})" title="حذف الموظف">🗑️ حذف</button>
                `;
            });
        }

        function hasActiveFilters() {
            const searchTerm = document.getElementById('searchInput').value.trim();
            const statusFilter = document.getElementById('statusFilter').value;
            const departmentFilter = document.getElementById('departmentFilter').value;

            return searchTerm !== '' || statusFilter !== '' || departmentFilter !== '';
        }

        function editEmployee(index) {
            const employee = employees[index];
            document.getElementById('employeeName').value = employee.name;
            document.getElementById('employeeId').value = employee.id;
            document.getElementById('department').value = employee.department;
            document.getElementById('hireDate').value = employee.hireDate;
            document.getElementById('annualLeave').value = employee.annualLeave;
            document.getElementById('carriedOverLeave').value = employee.carriedOverLeave || 0;
            document.getElementById('sickLeave').value = employee.sickLeave;
            document.getElementById('emergencyLeave').value = employee.emergencyLeave;

            editingIndex = index;
            document.getElementById('addEmployeeBtn').textContent = 'تحديث الموظف';

            // Scroll to top
            window.scrollTo(0, 0);
        }

        function deleteEmployee(index) {
            const employee = employees[index];
            if (confirm(`هل أنت متأكد من حذف الموظف "${employee.name}"؟\nسيتم حذف جميع سجلات الإجازات المرتبطة به.`)) {
                employees.splice(index, 1);
                updateDepartmentFilter();
                filterEmployees();
                updateStats();
                saveData();
                showAlert(`تم حذف الموظف "${escapeHtml(employee.name)}" بنجاح`, 'success');
            }
        }

        // Enhanced leave management
        function openLeaveModal(index) {
            currentEmployeeIndex = index;
            const employee = employees[index];

            document.getElementById('modalEmployeeName').textContent = employee.name;
            document.getElementById('leaveType').value = 'annual';
            document.getElementById('leaveDays').value = '';
            document.getElementById('leaveStartDate').value = '';
            document.getElementById('leaveEndDate').value = '';
            document.getElementById('leaveReason').value = '';

            // Show current balance information
            updateLeaveBalanceInfo(employee);
            updateLeaveHistory();
            document.getElementById('leaveModal').style.display = 'block';
        }

        function updateLeaveBalanceInfo(employee) {
            const totalAvailable = employee.annualLeave + employee.carriedOverLeave;
            const remainingTotal = totalAvailable - employee.usedAnnual;

            // Create or update balance info display
            let balanceInfo = document.getElementById('leaveBalanceInfo');
            if (!balanceInfo) {
                balanceInfo = document.createElement('div');
                balanceInfo.id = 'leaveBalanceInfo';
                balanceInfo.className = 'carried-over-info';

                // Insert after the employee name
                const modalContent = document.querySelector('.modal-content h2');
                modalContent.parentNode.insertBefore(balanceInfo, modalContent.nextSibling);
            }

            balanceInfo.innerHTML = `
                <strong>📊 معلومات الرصيد:</strong><br>
                • الرصيد السنوي: ${employee.annualLeave} يوم<br>
                • الرصيد المرحل: <span class="carried-over-highlight">${employee.carriedOverLeave} يوم</span><br>
                • إجمالي المتاح: <strong>${totalAvailable} يوم</strong><br>
                • المستخدم: ${employee.usedAnnual} يوم<br>
                • المتبقي: <strong style="color: ${remainingTotal <= 5 ? '#e74c3c' : '#27ae60'}">${remainingTotal} يوم</strong>
            `;
        }

        function closeLeaveModal() {
            document.getElementById('leaveModal').style.display = 'none';
            currentEmployeeIndex = -1;
        }

        function addLeaveRecord() {
            if (currentEmployeeIndex === -1) return;

            const employee = employees[currentEmployeeIndex];
            const leaveType = sanitizeInput(document.getElementById('leaveType').value);
            const leaveDays = parseFloat(document.getElementById('leaveDays').value);
            const startDate = document.getElementById('leaveStartDate').value;
            const endDate = document.getElementById('leaveEndDate').value;
            const reason = sanitizeInput(document.getElementById('leaveReason').value.trim());

            // Validation
            if (!leaveDays || leaveDays <= 0) {
                showAlert('يرجى إدخال عدد أيام صحيح', 'warning');
                return;
            }

            if (!startDate || !endDate) {
                showAlert('يرجى تحديد تاريخ البداية والنهاية', 'warning');
                return;
            }

            if (new Date(startDate) > new Date(endDate)) {
                showAlert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'warning');
                return;
            }

            // Check available balance
            let currentUsed = 0;
            let maxAllowed = 0;
            let leaveTypeName = '';

            switch(leaveType) {
                case 'annual':
                    currentUsed = employee.usedAnnual;
                    maxAllowed = employee.annualLeave + employee.carriedOverLeave;
                    leaveTypeName = 'إجازة سنوية';
                    break;
                case 'sick':
                    currentUsed = employee.usedSick;
                    maxAllowed = employee.sickLeave;
                    leaveTypeName = 'إجازة مرضية';
                    break;
                case 'emergency':
                    currentUsed = employee.usedEmergency;
                    maxAllowed = employee.emergencyLeave;
                    leaveTypeName = 'إجازة طارئة';
                    break;
            }

            if (currentUsed + leaveDays > maxAllowed) {
                const remaining = maxAllowed - currentUsed;
                showAlert(`الرصيد غير كافي. المتبقي: ${remaining} يوم فقط`, 'warning');
                return;
            }

            // Add leave record
            const leaveRecord = {
                id: Date.now(),
                type: leaveType,
                typeName: leaveTypeName,
                days: leaveDays,
                startDate,
                endDate,
                reason: reason || 'غير محدد',
                addedDate: new Date().toISOString()
            };

            if (!employee.leaveHistory) {
                employee.leaveHistory = [];
            }
            employee.leaveHistory.push(leaveRecord);

            // Update used days
            switch(leaveType) {
                case 'annual':
                    employee.usedAnnual += leaveDays;
                    break;
                case 'sick':
                    employee.usedSick += leaveDays;
                    break;
                case 'emergency':
                    employee.usedEmergency += leaveDays;
                    break;
            }

            filterEmployees();
            updateStats();
            updateLeaveBalanceInfo(employee);
            updateLeaveHistory();
            saveData();

            showAlert(`تم إضافة ${leaveTypeName} لمدة ${leaveDays} يوم للموظف ${employee.name}`, 'success');

            // Clear form
            document.getElementById('leaveDays').value = '';
            document.getElementById('leaveStartDate').value = '';
            document.getElementById('leaveEndDate').value = '';
            document.getElementById('leaveReason').value = '';
        }

        function updateLeaveHistory() {
            if (currentEmployeeIndex === -1) return;

            const employee = employees[currentEmployeeIndex];
            const historyContent = document.getElementById('leaveHistoryContent');

            if (!employee.leaveHistory || employee.leaveHistory.length === 0) {
                historyContent.innerHTML = '<p style="text-align: center; padding: 20px; color: #666;">لا توجد إجازات مسجلة</p>';
                return;
            }

            // Sort by date (newest first)
            const sortedHistory = [...employee.leaveHistory].sort((a, b) => new Date(b.addedDate) - new Date(a.addedDate));

            historyContent.innerHTML = sortedHistory.map(record => `
                <div class="leave-record">
                    <div class="leave-record-header">
                        <strong>${record.typeName}</strong>
                        <button class="delete-leave" onclick="deleteLeaveRecord(${record.id})" title="حذف الإجازة">×</button>
                    </div>
                    <div>📅 من ${new Date(record.startDate).toLocaleDateString('ar-SA')} إلى ${new Date(record.endDate).toLocaleDateString('ar-SA')}</div>
                    <div>⏱️ المدة: ${record.days} يوم</div>
                    <div>📝 السبب: ${record.reason}</div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">تم الإضافة: ${new Date(record.addedDate).toLocaleDateString('ar-SA')}</div>
                </div>
            `).join('');
        }

        function deleteLeaveRecord(recordId) {
            if (currentEmployeeIndex === -1) return;

            const employee = employees[currentEmployeeIndex];
            const recordIndex = employee.leaveHistory.findIndex(record => record.id === recordId);

            if (recordIndex === -1) return;

            const record = employee.leaveHistory[recordIndex];

            if (confirm(`هل أنت متأكد من حذف إجازة ${record.typeName} لمدة ${record.days} يوم؟`)) {
                // Restore the used days
                switch(record.type) {
                    case 'annual':
                        employee.usedAnnual -= record.days;
                        break;
                    case 'sick':
                        employee.usedSick -= record.days;
                        break;
                    case 'emergency':
                        employee.usedEmergency -= record.days;
                        break;
                }

                // Remove the record
                employee.leaveHistory.splice(recordIndex, 1);

                filterEmployees();
                updateStats();
                updateLeaveBalanceInfo(employee);
                updateLeaveHistory();
                saveData();

                showAlert(`تم حذف إجازة ${record.typeName} بنجاح`, 'success');
            }
        }

        function updateStats() {
            const totalEmployees = employees.length;
            const totalCarriedOver = employees.reduce((sum, emp) => sum + (emp.carriedOverLeave || 0), 0);
            const totalUsedLeaves = employees.reduce((sum, emp) => sum + emp.usedAnnual + emp.usedSick + emp.usedEmergency, 0);
            const totalRemainingLeaves = employees.reduce((sum, emp) =>
                sum + ((emp.annualLeave + emp.carriedOverLeave) - emp.usedAnnual) + (emp.sickLeave - emp.usedSick) + (emp.emergencyLeave - emp.usedEmergency), 0);
            const totalAllowedLeaves = employees.reduce((sum, emp) => sum + emp.annualLeave + emp.carriedOverLeave + emp.sickLeave + emp.emergencyLeave, 0);
            const averageUsage = totalAllowedLeaves > 0 ? Math.round((totalUsedLeaves / totalAllowedLeaves) * 100) : 0;

            document.getElementById('totalEmployees').textContent = totalEmployees;
            document.getElementById('totalCarriedOver').textContent = totalCarriedOver;
            document.getElementById('totalUsedLeaves').textContent = totalUsedLeaves;
            document.getElementById('totalRemainingLeaves').textContent = totalRemainingLeaves;
            document.getElementById('averageUsage').textContent = averageUsage + '%';
        }

        // Export functions
        function exportToCSV() {
            if (employees.length === 0) {
                showAlert('لا توجد بيانات للتصدير', 'warning');
                return;
            }

            const headers = [
                'اسم الموظف', 'الرقم الوظيفي', 'القسم', 'تاريخ التوظيف', 'سنوات الخدمة',
                'الرصيد السنوي', 'الرصيد المرحل', 'إجمالي المتاح', 'المستخدم السنوي', 'المتبقي السنوي',
                'الرصيد المرضي', 'المستخدم المرضي', 'المتبقي المرضي',
                'الرصيد الطارئ', 'المستخدم الطارئ', 'المتبقي الطارئ', 'الحالة'
            ];

            const csvContent = [
                headers.join(','),
                ...employees.map(emp => {
                    const yearsOfService = calculateYearsOfService(emp.hireDate);
                    const totalAvailable = emp.annualLeave + emp.carriedOverLeave;
                    const remainingTotal = totalAvailable - emp.usedAnnual;
                    const status = getEmployeeStatus(emp);
                    return [
                        emp.name, emp.id, emp.department, emp.hireDate, yearsOfService,
                        emp.annualLeave, emp.carriedOverLeave, totalAvailable, emp.usedAnnual, remainingTotal,
                        emp.sickLeave, emp.usedSick, emp.sickLeave - emp.usedSick,
                        emp.emergencyLeave, emp.usedEmergency, emp.emergencyLeave - emp.usedEmergency,
                        status
                    ].join(',');
                })
            ].join('\n');

            downloadFile(csvContent, 'employee_leaves.csv', 'text/csv;charset=utf-8;');
            showAlert('تم تصدير البيانات بصيغة CSV بنجاح', 'success');
        }

        function exportToJSON() {
            const data = saveData();
            downloadFile(data, `employee_leaves_backup_${new Date().toISOString().split('T')[0]}.json`, 'application/json');
            showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
        }

        function downloadFile(content, filename, contentType) {
            const blob = new Blob([content], { type: contentType });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        }

        function importData() {
            const fileInput = document.getElementById('importFile');
            const file = fileInput.files[0];

            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);

                    if (data.employees && Array.isArray(data.employees)) {
                        if (confirm('هل أنت متأكد من استيراد البيانات؟ سيتم استبدال البيانات الحالية.')) {
                            employees = data.employees;
                            updateTable();
                            updateStats();
                            saveData();
                            showAlert(`تم استيراد ${employees.length} موظف بنجاح`, 'success');
                        }
                    } else {
                        showAlert('ملف غير صالح. يرجى التأكد من صحة الملف', 'danger');
                    }
                } catch (error) {
                    showAlert('خطأ في قراءة الملف. يرجى التأكد من صحة الملف', 'danger');
                }
            };
            reader.readAsText(file);

            // Clear the file input
            fileInput.value = '';
        }

        function resetData() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإجازات المستخدمة؟\nسيتم الاحتفاظ ببيانات الموظفين ولكن سيتم مسح سجل الإجازات.')) {
                employees.forEach(emp => {
                    emp.usedAnnual = 0;
                    emp.usedSick = 0;
                    emp.usedEmergency = 0;
                    emp.leaveHistory = [];
                });
                updateTable();
                updateStats();
                saveData();
                showAlert('تم إعادة تعيين البيانات بنجاح', 'success');
            }
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من حذف جميع البيانات؟\nسيتم حذف البيانات من التخزين المحلي أيضاً.\nهذا الإجراء لا يمكن التراجع عنه.')) {
                if (confirm('تأكيد أخير: سيتم حذف جميع بيانات الموظفين والإجازات نهائياً من الذاكرة والتخزين المحلي!')) {
                    employees = [];
                    clearLocalStorage();
                    updateTable();
                    updateStats();
                    showAlert('تم مسح جميع البيانات من الذاكرة والتخزين المحلي', 'success');
                }
            }
        }

        // Date calculation helpers
        function calculateLeaveDays() {
            const startDate = document.getElementById('leaveStartDate').value;
            const endDate = document.getElementById('leaveEndDate').value;

            if (startDate && endDate) {
                const start = new Date(startDate);
                const end = new Date(endDate);
                const diffTime = Math.abs(end - start);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end dates

                document.getElementById('leaveDays').value = diffDays;
            }
        }

        // Event listeners
        document.getElementById('leaveStartDate').addEventListener('change', calculateLeaveDays);
        document.getElementById('leaveEndDate').addEventListener('change', calculateLeaveDays);

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('leaveModal');
            if (event.target === modal) {
                closeLeaveModal();
            }
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeLeaveModal();
            }
        });

        // Test data generation functions
        function generateTestData() {
            const testEmployees = [
                {
                    name: 'أحمد محمد العلي',
                    id: 'EMP001',
                    department: 'تقنية المعلومات',
                    hireDate: '2020-01-15',
                    annualLeave: 30,
                    carriedOverLeave: 12,
                    sickLeave: 15,
                    emergencyLeave: 5,
                    usedAnnual: 8,
                    usedSick: 2,
                    usedEmergency: 0,
                    leaveHistory: [
                        {
                            id: Date.now() + 1,
                            type: 'annual',
                            typeName: 'إجازة سنوية',
                            days: 5,
                            startDate: '2024-01-10',
                            endDate: '2024-01-14',
                            reason: 'إجازة شتوية',
                            addedDate: '2024-01-08T10:00:00.000Z'
                        },
                        {
                            id: Date.now() + 2,
                            type: 'annual',
                            typeName: 'إجازة سنوية',
                            days: 3,
                            startDate: '2024-02-20',
                            endDate: '2024-02-22',
                            reason: 'مناسبة عائلية',
                            addedDate: '2024-02-15T14:30:00.000Z'
                        },
                        {
                            id: Date.now() + 3,
                            type: 'sick',
                            typeName: 'إجازة مرضية',
                            days: 2,
                            startDate: '2024-03-05',
                            endDate: '2024-03-06',
                            reason: 'إنفلونزا',
                            addedDate: '2024-03-05T08:00:00.000Z'
                        }
                    ]
                },
                {
                    name: 'Sarah Johnson',
                    id: 'EMP002',
                    department: 'Human Resources',
                    hireDate: '2019-03-20',
                    annualLeave: 35,
                    carriedOverLeave: 8,
                    sickLeave: 20,
                    emergencyLeave: 7,
                    usedAnnual: 25,
                    usedSick: 5,
                    usedEmergency: 2,
                    leaveHistory: [
                        {
                            id: Date.now() + 4,
                            type: 'annual',
                            typeName: 'إجازة سنوية',
                            days: 15,
                            startDate: '2024-01-15',
                            endDate: '2024-01-29',
                            reason: 'Summer vacation',
                            addedDate: '2024-01-10T09:00:00.000Z'
                        },
                        {
                            id: Date.now() + 5,
                            type: 'annual',
                            typeName: 'إجازة سنوية',
                            days: 10,
                            startDate: '2024-03-10',
                            endDate: '2024-03-19',
                            reason: 'Family trip',
                            addedDate: '2024-03-05T11:00:00.000Z'
                        },
                        {
                            id: Date.now() + 6,
                            type: 'sick',
                            typeName: 'إجازة مرضية',
                            days: 3,
                            startDate: '2024-02-12',
                            endDate: '2024-02-14',
                            reason: 'Medical treatment',
                            addedDate: '2024-02-12T07:30:00.000Z'
                        },
                        {
                            id: Date.now() + 7,
                            type: 'sick',
                            typeName: 'إجازة مرضية',
                            days: 2,
                            startDate: '2024-04-01',
                            endDate: '2024-04-02',
                            reason: 'Dental surgery',
                            addedDate: '2024-03-28T16:00:00.000Z'
                        },
                        {
                            id: Date.now() + 8,
                            type: 'emergency',
                            typeName: 'إجازة طارئة',
                            days: 2,
                            startDate: '2024-02-25',
                            endDate: '2024-02-26',
                            reason: 'Family emergency',
                            addedDate: '2024-02-25T06:00:00.000Z'
                        }
                    ]
                },
                {
                    name: 'فاطمة أحمد الزهراني',
                    id: 'EMP003',
                    department: 'المالية',
                    hireDate: '2021-06-10',
                    annualLeave: 25,
                    carriedOverLeave: 5,
                    sickLeave: 12,
                    emergencyLeave: 4,
                    usedAnnual: 28,
                    usedSick: 8,
                    usedEmergency: 1,
                    leaveHistory: [
                        {
                            id: Date.now() + 9,
                            type: 'annual',
                            typeName: 'إجازة سنوية',
                            days: 20,
                            startDate: '2024-01-20',
                            endDate: '2024-02-08',
                            reason: 'إجازة سنوية طويلة',
                            addedDate: '2024-01-15T12:00:00.000Z'
                        },
                        {
                            id: Date.now() + 10,
                            type: 'annual',
                            typeName: 'إجازة سنوية',
                            days: 8,
                            startDate: '2024-03-15',
                            endDate: '2024-03-22',
                            reason: 'عطلة ربيعية',
                            addedDate: '2024-03-10T10:30:00.000Z'
                        },
                        {
                            id: Date.now() + 11,
                            type: 'sick',
                            typeName: 'إجازة مرضية',
                            days: 5,
                            startDate: '2024-02-15',
                            endDate: '2024-02-19',
                            reason: 'عملية جراحية',
                            addedDate: '2024-02-14T15:00:00.000Z'
                        },
                        {
                            id: Date.now() + 12,
                            type: 'sick',
                            typeName: 'إجازة مرضية',
                            days: 3,
                            startDate: '2024-04-10',
                            endDate: '2024-04-12',
                            reason: 'متابعة طبية',
                            addedDate: '2024-04-09T13:00:00.000Z'
                        },
                        {
                            id: Date.now() + 13,
                            type: 'emergency',
                            typeName: 'إجازة طارئة',
                            days: 1,
                            startDate: '2024-03-25',
                            endDate: '2024-03-25',
                            reason: 'ظرف طارئ',
                            addedDate: '2024-03-25T08:30:00.000Z'
                        }
                    ]
                },
                {
                    name: 'محمد عبدالله الشمري',
                    id: 'EMP004',
                    department: 'التسويق',
                    hireDate: '2022-09-01',
                    annualLeave: 28,
                    carriedOverLeave: 0,
                    sickLeave: 10,
                    emergencyLeave: 3,
                    usedAnnual: 15,
                    usedSick: 0,
                    usedEmergency: 0,
                    leaveHistory: [
                        {
                            id: Date.now() + 14,
                            type: 'annual',
                            typeName: 'إجازة سنوية',
                            days: 10,
                            startDate: '2024-02-01',
                            endDate: '2024-02-10',
                            reason: 'إجازة شهر العسل',
                            addedDate: '2024-01-25T09:00:00.000Z'
                        },
                        {
                            id: Date.now() + 15,
                            type: 'annual',
                            typeName: 'إجازة سنوية',
                            days: 5,
                            startDate: '2024-04-15',
                            endDate: '2024-04-19',
                            reason: 'عطلة ربيعية',
                            addedDate: '2024-04-10T11:00:00.000Z'
                        }
                    ]
                },
                {
                    name: 'Lisa Chen',
                    id: 'EMP005',
                    department: 'تقنية المعلومات',
                    hireDate: '2018-11-12',
                    annualLeave: 40,
                    carriedOverLeave: 15,
                    sickLeave: 25,
                    emergencyLeave: 8,
                    usedAnnual: 52,
                    usedSick: 12,
                    usedEmergency: 3,
                    leaveHistory: [
                        {
                            id: Date.now() + 16,
                            type: 'annual',
                            typeName: 'إجازة سنوية',
                            days: 25,
                            startDate: '2024-01-05',
                            endDate: '2024-01-29',
                            reason: 'Annual vacation',
                            addedDate: '2024-01-01T10:00:00.000Z'
                        },
                        {
                            id: Date.now() + 17,
                            type: 'annual',
                            typeName: 'إجازة سنوية',
                            days: 15,
                            startDate: '2024-03-01',
                            endDate: '2024-03-15',
                            reason: 'Spring break',
                            addedDate: '2024-02-25T14:00:00.000Z'
                        },
                        {
                            id: Date.now() + 18,
                            type: 'annual',
                            typeName: 'إجازة سنوية',
                            days: 12,
                            startDate: '2024-04-20',
                            endDate: '2024-05-01',
                            reason: 'Conference and training',
                            addedDate: '2024-04-15T16:00:00.000Z'
                        },
                        {
                            id: Date.now() + 19,
                            type: 'sick',
                            typeName: 'إجازة مرضية',
                            days: 7,
                            startDate: '2024-02-10',
                            endDate: '2024-02-16',
                            reason: 'Surgery recovery',
                            addedDate: '2024-02-09T08:00:00.000Z'
                        },
                        {
                            id: Date.now() + 20,
                            type: 'sick',
                            typeName: 'إجازة مرضية',
                            days: 5,
                            startDate: '2024-03-20',
                            endDate: '2024-03-24',
                            reason: 'Medical treatment',
                            addedDate: '2024-03-19T12:00:00.000Z'
                        },
                        {
                            id: Date.now() + 21,
                            type: 'emergency',
                            typeName: 'إجازة طارئة',
                            days: 3,
                            startDate: '2024-01-30',
                            endDate: '2024-02-01',
                            reason: 'Family emergency',
                            addedDate: '2024-01-30T07:00:00.000Z'
                        }
                    ]
                },
                {
                    name: 'عبدالرحمن سالم القحطاني',
                    id: 'EMP006',
                    department: 'العمليات',
                    hireDate: '2023-02-14',
                    annualLeave: 22,
                    carriedOverLeave: 3,
                    sickLeave: 8,
                    emergencyLeave: 2,
                    usedAnnual: 25,
                    usedSick: 6,
                    usedEmergency: 2,
                    leaveHistory: [
                        {
                            id: Date.now() + 22,
                            type: 'annual',
                            typeName: 'إجازة سنوية',
                            days: 12,
                            startDate: '2024-01-08',
                            endDate: '2024-01-19',
                            reason: 'إجازة شتوية',
                            addedDate: '2024-01-05T09:30:00.000Z'
                        },
                        {
                            id: Date.now() + 23,
                            type: 'annual',
                            typeName: 'إجازة سنوية',
                            days: 8,
                            startDate: '2024-03-25',
                            endDate: '2024-04-01',
                            reason: 'عطلة عيد الفطر',
                            addedDate: '2024-03-20T13:00:00.000Z'
                        },
                        {
                            id: Date.now() + 24,
                            type: 'annual',
                            typeName: 'إجازة سنوية',
                            days: 5,
                            startDate: '2024-04-25',
                            endDate: '2024-04-29',
                            reason: 'إجازة ربيعية',
                            addedDate: '2024-04-20T10:00:00.000Z'
                        },
                        {
                            id: Date.now() + 25,
                            type: 'sick',
                            typeName: 'إجازة مرضية',
                            days: 4,
                            startDate: '2024-02-05',
                            endDate: '2024-02-08',
                            reason: 'التهاب رئوي',
                            addedDate: '2024-02-05T07:00:00.000Z'
                        },
                        {
                            id: Date.now() + 26,
                            type: 'sick',
                            typeName: 'إجازة مرضية',
                            days: 2,
                            startDate: '2024-03-12',
                            endDate: '2024-03-13',
                            reason: 'فحوصات طبية',
                            addedDate: '2024-03-11T15:00:00.000Z'
                        },
                        {
                            id: Date.now() + 27,
                            type: 'emergency',
                            typeName: 'إجازة طارئة',
                            days: 1,
                            startDate: '2024-02-20',
                            endDate: '2024-02-20',
                            reason: 'ظرف عائلي طارئ',
                            addedDate: '2024-02-20T06:30:00.000Z'
                        },
                        {
                            id: Date.now() + 28,
                            type: 'emergency',
                            typeName: 'إجازة طارئة',
                            days: 1,
                            startDate: '2024-04-05',
                            endDate: '2024-04-05',
                            reason: 'حالة طوارئ',
                            addedDate: '2024-04-05T08:00:00.000Z'
                        }
                    ]
                },
                {
                    name: 'نورا خالد المطيري',
                    id: 'EMP007',
                    department: 'الموارد البشرية',
                    hireDate: '2017-05-08',
                    annualLeave: 38,
                    carriedOverLeave: 20,
                    sickLeave: 18,
                    emergencyLeave: 6,
                    usedAnnual: 45,
                    usedSick: 3,
                    usedEmergency: 1,
                    leaveHistory: [
                        {
                            id: Date.now() + 29,
                            type: 'annual',
                            typeName: 'إجازة سنوية',
                            days: 30,
                            startDate: '2024-01-01',
                            endDate: '2024-01-30',
                            reason: 'إجازة سنوية طويلة',
                            addedDate: '2023-12-25T10:00:00.000Z'
                        },
                        {
                            id: Date.now() + 30,
                            type: 'annual',
                            typeName: 'إجازة سنوية',
                            days: 15,
                            startDate: '2024-04-01',
                            endDate: '2024-04-15',
                            reason: 'عطلة ربيعية',
                            addedDate: '2024-03-25T11:00:00.000Z'
                        },
                        {
                            id: Date.now() + 31,
                            type: 'sick',
                            typeName: 'إجازة مرضية',
                            days: 3,
                            startDate: '2024-02-28',
                            endDate: '2024-03-01',
                            reason: 'إنفلونزا موسمية',
                            addedDate: '2024-02-28T08:00:00.000Z'
                        },
                        {
                            id: Date.now() + 32,
                            type: 'emergency',
                            typeName: 'إجازة طارئة',
                            days: 1,
                            startDate: '2024-03-18',
                            endDate: '2024-03-18',
                            reason: 'ظرف طارئ',
                            addedDate: '2024-03-18T07:30:00.000Z'
                        }
                    ]
                },
                {
                    name: 'David Rodriguez',
                    id: 'EMP008',
                    department: 'المبيعات',
                    hireDate: '2024-01-02',
                    annualLeave: 20,
                    carriedOverLeave: 0,
                    sickLeave: 5,
                    emergencyLeave: 2,
                    usedAnnual: 0,
                    usedSick: 0,
                    usedEmergency: 0,
                    leaveHistory: []
                }
            ];

            return testEmployees;
        }

        // Load test data function
        function loadTestData() {
            if (confirm('هل أنت متأكد من تحميل البيانات التجريبية؟\nسيتم استبدال البيانات الحالية بـ 8 موظفين تجريبيين مع سجلات إجازات متنوعة.')) {
                const testData = generateTestData();
                employees = testData.map(emp => sanitizeEmployee(emp));

                // Initialize filters
                filteredEmployees = [...employees];
                updateDepartmentFilter();
                updateTable();
                updateStats();
                updateFilterResults();
                saveData();

                showAlert(`تم تحميل ${employees.length} موظف تجريبي بنجاح مع سجلات إجازاتهم`, 'success');
            }
        }

        // Comprehensive test function
        function runComprehensiveTest() {
            showAlert('🧪 بدء الاختبار الشامل للنظام...', 'info');

            setTimeout(() => {
                testBasicFunctionality();
            }, 1000);

            setTimeout(() => {
                testCarriedOverLeave();
            }, 2000);

            setTimeout(() => {
                testSearchAndFilter();
            }, 3000);

            setTimeout(() => {
                testValidation();
            }, 4000);

            setTimeout(() => {
                showAlert('✅ تم إكمال الاختبار الشامل بنجاح! تحقق من النتائج في وحدة التحكم (F12)', 'success');
            }, 5000);
        }

        function testBasicFunctionality() {
            console.log('🔍 اختبار الوظائف الأساسية:');
            console.log(`- عدد الموظفين: ${employees.length}`);
            console.log(`- التخزين المحلي متاح: ${isLocalStorageAvailable()}`);
            console.log(`- البيانات محفوظة: ${localStorage.getItem('employeeLeaveSystem') ? 'نعم' : 'لا'}`);

            const stats = {
                totalEmployees: employees.length,
                totalCarriedOver: employees.reduce((sum, emp) => sum + (emp.carriedOverLeave || 0), 0),
                totalUsed: employees.reduce((sum, emp) => sum + emp.usedAnnual + emp.usedSick + emp.usedEmergency, 0),
                totalRemaining: employees.reduce((sum, emp) =>
                    sum + ((emp.annualLeave + emp.carriedOverLeave) - emp.usedAnnual) + (emp.sickLeave - emp.usedSick) + (emp.emergencyLeave - emp.usedEmergency), 0)
            };

            console.log('📊 الإحصائيات:', stats);
            showAlert(`📊 الإحصائيات: ${stats.totalEmployees} موظف، ${stats.totalCarriedOver} يوم مرحل، ${stats.totalUsed} يوم مستخدم`, 'info');
        }

        function testCarriedOverLeave() {
            console.log('🔍 اختبار خاصية الرصيد المرحل:');

            employees.forEach((emp, index) => {
                const totalAvailable = emp.annualLeave + emp.carriedOverLeave;
                const remaining = totalAvailable - emp.usedAnnual;
                const status = getEmployeeStatus(emp);

                console.log(`${index + 1}. ${emp.name}:`);
                console.log(`   - الرصيد السنوي: ${emp.annualLeave}`);
                console.log(`   - الرصيد المرحل: ${emp.carriedOverLeave}`);
                console.log(`   - إجمالي المتاح: ${totalAvailable}`);
                console.log(`   - المستخدم: ${emp.usedAnnual}`);
                console.log(`   - المتبقي: ${remaining}`);
                console.log(`   - الحالة: ${status}`);
                console.log('   ---');
            });

            showAlert('🔍 تم اختبار خاصية الرصيد المرحل - تحقق من وحدة التحكم للتفاصيل', 'info');
        }

        function testSearchAndFilter() {
            console.log('🔍 اختبار البحث والفلترة:');

            // Test search
            document.getElementById('searchInput').value = 'أحمد';
            filterEmployees();
            console.log(`- البحث عن "أحمد": ${filteredEmployees.length} نتيجة`);

            // Test department filter
            document.getElementById('searchInput').value = '';
            document.getElementById('departmentFilter').value = 'تقنية المعلومات';
            filterEmployees();
            console.log(`- فلترة قسم "تقنية المعلومات": ${filteredEmployees.length} نتيجة`);

            // Test status filter
            document.getElementById('departmentFilter').value = '';
            document.getElementById('statusFilter').value = 'نفد الرصيد';
            filterEmployees();
            console.log(`- فلترة حالة "نفد الرصيد": ${filteredEmployees.length} نتيجة`);

            // Clear filters
            clearFilters();
            console.log(`- بعد مسح الفلاتر: ${filteredEmployees.length} نتيجة`);

            showAlert('🔍 تم اختبار البحث والفلترة بنجاح', 'info');
        }

        function testValidation() {
            console.log('🔍 اختبار التحقق من صحة البيانات:');

            // Test number validation
            const testInput = document.createElement('input');
            testInput.type = 'number';
            testInput.value = '-5';
            const negativeTest = validateNumberInput(testInput, 0, 365);
            console.log(`- اختبار القيم السالبة: ${negativeTest ? 'فشل' : 'نجح'}`);

            testInput.value = '500';
            const maxTest = validateNumberInput(testInput, 0, 365);
            console.log(`- اختبار القيم الكبيرة: ${maxTest ? 'فشل' : 'نجح'}`);

            testInput.value = '30';
            const validTest = validateNumberInput(testInput, 0, 365);
            console.log(`- اختبار القيم الصحيحة: ${validTest ? 'نجح' : 'فشل'}`);

            // Test text validation
            const textInput = document.createElement('input');
            textInput.value = 'أحمد محمد';
            const arabicTest = validateTextInput(textInput, /^[\u0600-\u06FFa-zA-Z\s]+$/, 'خطأ');
            console.log(`- اختبار النص العربي: ${arabicTest ? 'نجح' : 'فشل'}`);

            textInput.value = 'Ahmed123';
            const mixedTest = validateTextInput(textInput, /^[\u0600-\u06FFa-zA-Z\s]+$/, 'خطأ');
            console.log(`- اختبار النص المختلط: ${mixedTest ? 'فشل' : 'نجح'}`);

            showAlert('🔍 تم اختبار التحقق من صحة البيانات', 'info');
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadData();

            // Set default hire date to today
            document.getElementById('hireDate').value = new Date().toISOString().split('T')[0];

            // Show welcome message based on localStorage availability
            if (isLocalStorageAvailable()) {
                showAlert('مرحباً بك في نظام إدارة إجازات الموظفين المتطور مع الحفظ التلقائي', 'success');
            } else {
                showAlert('مرحباً بك في نظام إدارة إجازات الموظفين - تحذير: التخزين المحلي غير متاح', 'warning');
            }
        });

        // Auto-save every 30 seconds
        setInterval(() => {
            if (employees.length > 0) {
                saveData();
            }
        }, 30000);
    </script>
</body>
</html>