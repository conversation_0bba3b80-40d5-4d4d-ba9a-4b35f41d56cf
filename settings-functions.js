/**
 * وظائف الإعدادات الجديدة
 * نظام إدارة إجازات الموظفين المتطور
 */

// ===== إعدادات النظام العامة =====

// متغيرات الإعدادات
window.systemSettings = {
    fontSize: 'medium',
    itemsPerPage: 10,
    defaultAnnualLeave: 30,
    defaultSickLeave: 15,
    defaultEmergencyLeave: 5,
    lowBalanceAlert: true,
    alertThreshold: 5,
    autoSave: true
};

/**
 * تحميل الإعدادات من التخزين المحلي
 */
function loadSystemSettings() {
    try {
        const savedSettings = localStorage.getItem('systemSettings');
        if (savedSettings) {
            window.systemSettings = { ...window.systemSettings, ...JSON.parse(savedSettings) };
        }
        applySystemSettings();
    } catch (error) {
        console.warn('فشل في تحميل الإعدادات:', error);
    }
}

/**
 * حفظ الإعدادات في التخزين المحلي
 */
function saveSystemSettings() {
    try {
        localStorage.setItem('systemSettings', JSON.stringify(window.systemSettings));
        showNotification('تم حفظ الإعدادات بنجاح', 'success', 3000);
    } catch (error) {
        console.error('فشل في حفظ الإعدادات:', error);
        showNotification('فشل في حفظ الإعدادات', 'danger', 5000);
    }
}

/**
 * تطبيق الإعدادات على النظام
 */
function applySystemSettings() {
    // تطبيق حجم الخط
    changeFontSize(window.systemSettings.fontSize);
    
    // تطبيق عدد العناصر في الصفحة
    if (window.itemsPerPage !== window.systemSettings.itemsPerPage) {
        window.itemsPerPage = window.systemSettings.itemsPerPage;
        if (typeof updateEmployeeList === 'function') {
            updateEmployeeList();
        }
    }
}

// ===== وظائف إعدادات الواجهة =====

/**
 * تغيير حجم الخط
 */
function changeFontSize(size) {
    window.systemSettings.fontSize = size;
    
    const body = document.body;
    body.classList.remove('font-small', 'font-medium', 'font-large');
    
    switch (size) {
        case 'small':
            body.classList.add('font-small');
            break;
        case 'large':
            body.classList.add('font-large');
            break;
        default:
            body.classList.add('font-medium');
    }
    
    saveSystemSettings();
}

/**
 * تغيير عدد العناصر في الصفحة
 */
function changeItemsPerPage(count) {
    window.systemSettings.itemsPerPage = parseInt(count);
    window.itemsPerPage = window.systemSettings.itemsPerPage;
    
    if (typeof updateEmployeeList === 'function') {
        updateEmployeeList();
    }
    
    saveSystemSettings();
    showNotification(`تم تغيير عدد العناصر إلى ${count} عنصر في الصفحة`, 'info', 3000);
}

// ===== وظائف إعدادات الإجازات =====

/**
 * تحديث الرصيد السنوي الافتراضي
 */
function updateDefaultAnnualLeave(value) {
    window.systemSettings.defaultAnnualLeave = parseInt(value);
    saveSystemSettings();
    showNotification(`تم تحديث الرصيد السنوي الافتراضي إلى ${value} يوم`, 'info', 3000);
}

/**
 * تحديث الإجازة المرضية الافتراضية
 */
function updateDefaultSickLeave(value) {
    window.systemSettings.defaultSickLeave = parseInt(value);
    saveSystemSettings();
    showNotification(`تم تحديث الإجازة المرضية الافتراضية إلى ${value} يوم`, 'info', 3000);
}

/**
 * تحديث الإجازة الطارئة الافتراضية
 */
function updateDefaultEmergencyLeave(value) {
    window.systemSettings.defaultEmergencyLeave = parseInt(value);
    saveSystemSettings();
    showNotification(`تم تحديث الإجازة الطارئة الافتراضية إلى ${value} يوم`, 'info', 3000);
}

// ===== وظائف إعدادات التنبيهات =====

/**
 * تبديل تنبيه الرصيد المنخفض
 */
function toggleLowBalanceAlert(enabled) {
    window.systemSettings.lowBalanceAlert = enabled;
    saveSystemSettings();
    
    const message = enabled ? 'تم تفعيل تنبيهات الرصيد المنخفض' : 'تم إلغاء تنبيهات الرصيد المنخفض';
    showNotification(message, 'info', 3000);
}

/**
 * تحديث حد التنبيه
 */
function updateAlertThreshold(value) {
    window.systemSettings.alertThreshold = parseInt(value);
    saveSystemSettings();
    showNotification(`تم تحديث حد التنبيه إلى ${value} أيام`, 'info', 3000);
}

/**
 * تبديل الحفظ التلقائي
 */
function toggleAutoSave(enabled) {
    window.systemSettings.autoSave = enabled;
    saveSystemSettings();
    
    const message = enabled ? 'تم تفعيل الحفظ التلقائي' : 'تم إلغاء الحفظ التلقائي';
    showNotification(message, 'info', 3000);
}

// ===== وظائف المساعدة والمعلومات =====

/**
 * عرض معلومات النظام
 */
function showSystemInfo() {
    const systemInfo = {
        version: '4.1.0',
        buildDate: '2024-01-15',
        browser: navigator.userAgent.split(' ')[0],
        language: 'العربية',
        calendar: 'ميلادي',
        storage: isLocalStorageAvailable() ? 'متاح' : 'غير متاح',
        employees: window.employees ? window.employees.length : 0,
        lastSaved: localStorage.getItem('employeeLeaveSystem') ? 'متوفر' : 'غير متوفر'
    };
    
    const infoHTML = `
        <div class="modal-overlay" onclick="closeSystemInfo()">
            <div class="modal-content" onclick="event.stopPropagation()" style="max-width: 600px;">
                <div class="modal-header">
                    <h3>ℹ️ معلومات النظام</h3>
                    <button class="close-btn" onclick="closeSystemInfo()">×</button>
                </div>
                <div class="modal-body">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div class="info-item">
                            <strong>إصدار النظام:</strong><br>
                            <span style="color: #3498db;">${systemInfo.version}</span>
                        </div>
                        <div class="info-item">
                            <strong>تاريخ البناء:</strong><br>
                            <span style="color: #3498db;">${systemInfo.buildDate}</span>
                        </div>
                        <div class="info-item">
                            <strong>المتصفح:</strong><br>
                            <span style="color: #3498db;">${systemInfo.browser}</span>
                        </div>
                        <div class="info-item">
                            <strong>اللغة:</strong><br>
                            <span style="color: #3498db;">${systemInfo.language}</span>
                        </div>
                        <div class="info-item">
                            <strong>التقويم:</strong><br>
                            <span style="color: #3498db;">${systemInfo.calendar}</span>
                        </div>
                        <div class="info-item">
                            <strong>التخزين المحلي:</strong><br>
                            <span style="color: ${systemInfo.storage === 'متاح' ? '#27ae60' : '#e74c3c'};">${systemInfo.storage}</span>
                        </div>
                        <div class="info-item">
                            <strong>عدد الموظفين:</strong><br>
                            <span style="color: #3498db;">${systemInfo.employees}</span>
                        </div>
                        <div class="info-item">
                            <strong>آخر حفظ:</strong><br>
                            <span style="color: ${systemInfo.lastSaved === 'متوفر' ? '#27ae60' : '#e74c3c'};">${systemInfo.lastSaved}</span>
                        </div>
                    </div>
                    
                    <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <h4 style="margin: 0 0 10px 0; color: #2c3e50;">📊 إحصائيات الاستخدام</h4>
                        <div style="font-size: 14px; color: #666;">
                            <p>• تم تطوير هذا النظام لإدارة إجازات الموظفين بكفاءة عالية</p>
                            <p>• يدعم النظام اللغة العربية والتقويم الميلادي</p>
                            <p>• جميع البيانات محفوظة محلياً في متصفحك</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', infoHTML);
}

/**
 * إغلاق نافذة معلومات النظام
 */
function closeSystemInfo() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) modal.remove();
}

/**
 * إنشاء تقارير تجريبية
 */
function generateSampleReports() {
    if (window.employees.length === 0) {
        showNotification('❌ يرجى تحميل البيانات التجريبية أولاً', 'warning', 5000);
        return;
    }
    
    // إنشاء تقارير متنوعة للاختبار
    const reports = [
        { name: 'التقرير الشهري', func: generateMonthlyReport },
        { name: 'تقرير الأرصدة', func: generateLeaveBalanceReport },
        { name: 'الأرصدة المنخفضة', func: generateLowBalanceReport },
        { name: 'التقرير المفصل', func: generateDetailedReport }
    ];
    
    let currentReport = 0;
    
    function showNextReport() {
        if (currentReport < reports.length) {
            const report = reports[currentReport];
            showNotification(`📊 إنشاء ${report.name}...`, 'info', 2000);
            
            setTimeout(() => {
                report.func();
                currentReport++;
                
                if (currentReport < reports.length) {
                    setTimeout(showNextReport, 3000);
                } else {
                    showNotification('✅ تم إنشاء جميع التقارير التجريبية بنجاح', 'success', 5000);
                }
            }, 1000);
        }
    }
    
    showNextReport();
}

// ===== تهيئة الإعدادات =====

/**
 * تهيئة نظام الإعدادات
 */
function initializeSettings() {
    loadSystemSettings();
    
    // إضافة أنماط CSS لأحجام الخطوط
    const style = document.createElement('style');
    style.textContent = `
        .font-small { font-size: 14px; }
        .font-medium { font-size: 16px; }
        .font-large { font-size: 18px; }
        
        .font-small .btn { font-size: 12px; padding: 8px 15px; }
        .font-large .btn { font-size: 18px; padding: 15px 25px; }
        
        .font-small .card-title { font-size: 16px; }
        .font-large .card-title { font-size: 22px; }
    `;
    document.head.appendChild(style);
}

// تهيئة الإعدادات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initializeSettings);

console.log('⚙️ تم تحميل وظائف الإعدادات بنجاح');
