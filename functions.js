/**
 * نظام إدارة إجازات الموظفين المتطور
 * ملف الوظائف الأساسية
 * يحتوي على وظائف إدارة الموظفين والإجازات والتقارير
 */

// ===== وظائف إدارة الموظفين =====

/**
 * إضافة موظف جديد
 */
function addEmployee() {
    const name = sanitizeInput(document.getElementById('employeeName').value.trim());
    const id = sanitizeInput(document.getElementById('employeeId').value.trim());
    const department = sanitizeInput(document.getElementById('department').value.trim());
    const hireDate = document.getElementById('hireDate').value;
    const annualLeave = parseInt(document.getElementById('annualLeave').value) || 0;
    const carriedOverLeave = parseInt(document.getElementById('carriedOverLeave').value) || 0;
    const sickLeave = parseInt(document.getElementById('sickLeave').value) || 0;
    const emergencyLeave = parseInt(document.getElementById('emergencyLeave').value) || 0;

    // التحقق المحسن من صحة البيانات
    if (!name || !id || !department || !hireDate) {
        showAlert('يرجى ملء جميع الحقول المطلوبة المميزة بـ *', 'warning');
        return;
    }

    if (!/^[\u0600-\u06FFa-zA-Z\s]+$/.test(name)) {
        showAlert('اسم الموظف يجب أن يحتوي على أحرف عربية أو إنجليزية فقط', 'warning');
        return;
    }

    if (!/^[a-zA-Z0-9]+$/.test(id)) {
        showAlert('الرقم الوظيفي يجب أن يحتوي على أرقام وأحرف إنجليزية فقط', 'warning');
        return;
    }

    if (editingIndex === -1 && window.employees.some(emp => emp.id === id)) {
        showAlert('الرقم الوظيفي موجود مسبقاً. يرجى استخدام رقم مختلف', 'warning');
        return;
    }

    if (new Date(hireDate) > new Date()) {
        showAlert('تاريخ التوظيف لا يمكن أن يكون في المستقبل', 'warning');
        return;
    }

    if (annualLeave < 0 || carriedOverLeave < 0 || sickLeave < 0 || emergencyLeave < 0) {
        showAlert('قيم الإجازات يجب أن تكون أرقام موجبة', 'warning');
        return;
    }

    if (annualLeave > 365 || carriedOverLeave > 365 || sickLeave > 90 || emergencyLeave > 30) {
        showAlert('قيم الإجازات تبدو غير منطقية. يرجى المراجعة', 'warning');
        return;
    }

    if (carriedOverLeave > annualLeave * 2) {
        showAlert('الرصيد المرحل لا يمكن أن يتجاوز ضعف الرصيد السنوي', 'warning');
        return;
    }

    const employee = sanitizeEmployee({
        name,
        id,
        department,
        hireDate,
        annualLeave,
        carriedOverLeave,
        sickLeave,
        emergencyLeave,
        usedAnnual: 0,
        usedSick: 0,
        usedEmergency: 0,
        leaveHistory: []
    });

    if (editingIndex === -1) {
        window.employees.push(employee);
        showAlert(`تم إضافة الموظف ${name} بنجاح`, 'success');
    } else {
        employee.leaveHistory = window.employees[editingIndex].leaveHistory || [];
        window.employees[editingIndex] = employee;
        showAlert(`تم تحديث بيانات الموظف ${name} بنجاح`, 'success');
        editingIndex = -1;
        document.getElementById('addEmployeeBtn').innerHTML = '➕ إضافة موظف';
    }

    clearForm();
    updateDepartmentFilter();
    filterEmployees();
    updateStats();
    updateQuickEmployeeView();
    saveData();
}

/**
 * مسح النموذج
 */
function clearForm() {
    document.getElementById('employeeName').value = '';
    document.getElementById('employeeId').value = '';
    document.getElementById('department').value = '';
    document.getElementById('hireDate').value = new Date().toISOString().split('T')[0];
    document.getElementById('annualLeave').value = '30';
    document.getElementById('carriedOverLeave').value = '0';
    document.getElementById('sickLeave').value = '15';
    document.getElementById('emergencyLeave').value = '5';
}

/**
 * تعديل موظف - محسن
 */
function editEmployee(index) {
    const employee = window.employees[index];

    if (!employee) {
        showAlert('الموظف غير موجود', 'warning');
        return;
    }

    console.log(`🔄 بدء تعديل الموظف: ${employee.name} (فهرس: ${index})`);

    // التبديل إلى قسم الموظفين أولاً
    showSection('employees');

    // انتظار تحميل القسم ثم تعبئة البيانات
    setTimeout(() => {
        try {
            // تعبئة جميع الحقول مع التحقق من وجودها
            const fields = {
                'employeeName': employee.name,
                'employeeId': employee.id,
                'department': employee.department,
                'hireDate': employee.hireDate,
                'annualLeave': employee.annualLeave,
                'carriedOverLeave': employee.carriedOverLeave || 0,
                'sickLeave': employee.sickLeave,
                'emergencyLeave': employee.emergencyLeave
            };

            let fieldsLoaded = 0;
            Object.entries(fields).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.value = value;
                    fieldsLoaded++;
                } else {
                    console.warn(`⚠️ عنصر غير موجود: ${id}`);
                }
            });



            // تحديث حالة التعديل
            editingIndex = index;
            const btn = document.getElementById('addEmployeeBtn');
            if (btn) {
                btn.innerHTML = '🔄 تحديث الموظف';
            }

            // التمرير للنموذج
            setTimeout(() => {
                const card = document.querySelector('.card');
                if (card) {
                    card.scrollIntoView({ behavior: 'smooth' });
                }
            }, 100);

            showAlert(`تم تحميل بيانات الموظف ${employee.name} للتعديل`, 'info');


        } catch (error) {
            console.error('❌ خطأ في تحميل بيانات التعديل:', error);
            showAlert('خطأ في تحميل بيانات التعديل', 'danger');
        }
    }, 600); // وقت كافي لتحميل القسم
}

/**
 * حذف موظف
 */
function deleteEmployee(index) {
    const employee = window.employees[index];
    if (confirm(`هل أنت متأكد من حذف الموظف "${employee.name}"؟\nسيتم حذف جميع سجلات الإجازات المرتبطة به.`)) {
        window.employees.splice(index, 1);
        updateDepartmentFilter();
        filterEmployees();
        updateStats();
        updateQuickEmployeeView();
        saveData();
        showAlert(`تم حذف الموظف "${employee.name}" بنجاح`, 'success');
    }
}

// ===== وظائف الجدول والعرض =====

/**
 * تحديث الجدول
 */
function updateTable() {
    try {
        console.log('📋 تحديث جدول الموظفين...');

        const tbody = document.getElementById('employeeTableBody');
        if (!tbody) {
            console.warn('⚠️ لم يتم العثور على جدول الموظفين');
            return;
        }

        // التأكد من وجود المصفوفات
        if (!window.employees || !Array.isArray(window.employees)) {
            window.employees = [];
        }

        if (!window.filteredEmployees || !Array.isArray(window.filteredEmployees)) {
            window.filteredEmployees = [...window.employees];
        }

        tbody.innerHTML = '';

        if (window.employees.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="14" style="text-align: center; padding: 40px; color: #7f8c8d;">
                        <div style="font-size: 48px; margin-bottom: 15px;">👥</div>
                        <h3 style="margin-bottom: 10px;">لا توجد بيانات موظفين</h3>
                        <p style="margin-bottom: 20px;">ابدأ بإضافة موظفين جدد أو تحميل بيانات تجريبية</p>
                        <button class="btn btn-primary" onclick="document.getElementById('employeeName').focus()">
                            ➕ إضافة موظف جديد
                        </button>
                        <button class="btn btn-secondary" onclick="loadTestData()" style="margin-right: 10px;">
                            🧪 تحميل بيانات تجريبية
                        </button>
                    </td>
                </tr>
            `;

            return;
        }

        // حساب التصفح
        calculatePagination();
        const employeesToShow = getPaginatedData();

        console.log(`📊 عرض ${employeesToShow.length} موظف من أصل ${window.employees.length}`);

        employeesToShow.forEach((employee, filteredIndex) => {
            const originalIndex = window.employees.findIndex(emp => emp.id === employee.id);
            const row = tbody.insertRow();
            const yearsOfService = calculateYearsOfService(employee.hireDate);
            const totalAvailable = (employee.annualLeave || 0) + (employee.carriedOverLeave || 0);
            const remainingTotal = totalAvailable - (employee.usedAnnual || 0);
            const remainingSick = (employee.sickLeave || 0) - (employee.usedSick || 0);
            const remainingEmergency = (employee.emergencyLeave || 0) - (employee.usedEmergency || 0);
            const status = getEmployeeStatus(employee);

            let statusClass = 'balance-positive';
            if (remainingTotal <= 0) statusClass = 'balance-negative';
            else if (remainingTotal <= 5) statusClass = 'balance-warning';

            const cells = [
                { content: employee.name, isText: true },
                { content: employee.id, isText: true },
                { content: employee.department, isText: true },
                { content: new Date(employee.hireDate).toLocaleDateString('ar-SA'), isText: true },
                { content: yearsOfService, isText: true },
                { content: employee.annualLeave, isText: true },
                { content: employee.carriedOverLeave, isText: true, className: 'carried-over-highlight' },
                { content: totalAvailable, isText: true, className: 'balance-positive' },
                { content: employee.usedAnnual, isText: true },
                { content: remainingTotal, isText: true, className: statusClass },
                { content: remainingSick, isText: true, className: remainingSick <= 0 ? 'balance-negative' : remainingSick <= 2 ? 'balance-warning' : 'balance-positive' },
                { content: remainingEmergency, isText: true, className: remainingEmergency <= 0 ? 'balance-negative' : 'balance-positive' },
                { content: status, isText: true, className: statusClass }
            ];

            cells.forEach(cellData => {
                const cell = row.insertCell();
                cell.textContent = cellData.content;
                cell.style.padding = '12px';
                cell.style.textAlign = 'center';
                cell.style.borderBottom = '1px solid #e9ecef';
                if (cellData.className) {
                    if (cellData.className.includes('balance-positive')) cell.style.color = '#27ae60';
                    if (cellData.className.includes('balance-warning')) cell.style.color = '#f39c12';
                    if (cellData.className.includes('balance-negative')) cell.style.color = '#e74c3c';
                    if (cellData.className.includes('carried-over-highlight')) {
                        cell.style.backgroundColor = '#e8f5e8';
                        cell.style.fontWeight = '600';
                    }
                }
            });

            const actionsCell = row.insertCell();
            actionsCell.style.padding = '12px';
            actionsCell.style.textAlign = 'center';
            actionsCell.style.borderBottom = '1px solid #e9ecef';
            actionsCell.innerHTML = `
                <div class="btn-group">
                    <button class="btn btn-small btn-secondary" onclick="editEmployee(${originalIndex})" title="تعديل البيانات">✏️</button>
                    <button class="btn btn-small btn-success" onclick="openLeaveManagement(${originalIndex})" title="إدارة الإجازات">📅</button>
                    <button class="btn btn-small btn-danger" onclick="deleteEmployee(${originalIndex})" title="حذف الموظف">🗑️</button>
                </div>
            `;
        });

        // إضافة عناصر التحكم في التصفح
        const paginationContainer = document.getElementById('paginationControls');
        if (paginationContainer) {
            paginationContainer.innerHTML = createPaginationControls();
        }



    } catch (error) {
        console.error('❌ خطأ في تحديث الجدول:', error);
        showAlert('خطأ في تحديث جدول الموظفين', 'danger');
    }
}

/**
 * إنشاء عناصر التحكم في التصفح
 */
function createPaginationControls() {
    if (totalPages <= 1) return '';

    let paginationHTML = '<div class="pagination-container">';

    // زر السابق
    paginationHTML += `<button class="pagination-btn" onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>السابق</button>`;

    // أرقام الصفحات
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            paginationHTML += `<button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">${i}</button>`;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            paginationHTML += '<span class="pagination-info">...</span>';
        }
    }

    // زر التالي
    paginationHTML += `<button class="pagination-btn" onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>التالي</button>`;

    // معلومات
    const startItem = (currentPage - 1) * itemsPerPage + 1;
    const endItem = Math.min(currentPage * itemsPerPage, window.filteredEmployees.length || window.employees.length);
    const totalItems = window.filteredEmployees.length || window.employees.length;
    paginationHTML += `<div class="pagination-info">عرض ${startItem}-${endItem} من ${totalItems}</div>`;

    paginationHTML += '</div>';
    return paginationHTML;
}

// ===== وظائف البحث والفلترة المحسنة =====

/**
 * فلترة الموظفين حسب معايير متعددة
 */
function filterEmployees(searchTerm = '', department = '', status = '') {
    if (!window.employees || window.employees.length === 0) {
        updateTable();
        return;
    }

    try {
        window.filteredEmployees = window.employees.filter(emp => {
            // البحث في الاسم والرقم الوظيفي
            const matchesSearch = !searchTerm ||
                emp.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                emp.id.toLowerCase().includes(searchTerm.toLowerCase());

            // فلترة حسب القسم
            const matchesDepartment = !department || emp.department === department;

            // فلترة حسب حالة الرصيد
            let matchesStatus = true;
            if (status) {
                // استخدام getEmployeeStatus بدلاً من getLeaveStatus للتوافق
                const empStatus = getEmployeeStatus(emp);
                matchesStatus = empStatus === status;
            }

            return matchesSearch && matchesDepartment && matchesStatus;
        });

        updateTable();

        // عرض نتائج البحث
        const resultCount = window.filteredEmployees.length;
        const totalCount = window.employees.length;

        if (searchTerm || department || status) {
            showNotification(`تم العثور على ${resultCount} من أصل ${totalCount} موظف`, 'info', 3000);
        }

    } catch (error) {
        console.error('خطأ في فلترة الموظفين:', error);
        showNotification('خطأ في البحث والفلترة', 'danger', 5000);
    }
}

/**
 * البحث السريع في الموظفين
 */
function quickSearch(searchTerm) {
    filterEmployees(searchTerm, '', '');
}

/**
 * فلترة حسب القسم
 */
function filterByDepartment(department) {
    filterEmployees('', department, '');
}

/**
 * فلترة حسب حالة الرصيد
 */
function filterByStatus(status) {
    filterEmployees('', '', status);
}

/**
 * إعادة تعيين الفلاتر
 */
function resetFilters() {
    window.filteredEmployees = null;
    updateTable();

    // مسح حقول البحث - البحث عن جميع الـ IDs المحتملة
    const searchInputs = [
        document.getElementById('searchInput'),
        document.getElementById('mainSearchInput')
    ];
    const departmentFilters = [
        document.getElementById('departmentFilter'),
        document.getElementById('mainDepartmentFilter')
    ];
    const statusFilters = [
        document.getElementById('statusFilter'),
        document.getElementById('mainStatusFilter')
    ];

    searchInputs.forEach(input => {
        if (input) input.value = '';
    });

    departmentFilters.forEach(filter => {
        if (filter) filter.value = '';
    });

    statusFilters.forEach(filter => {
        if (filter) filter.value = '';
    });

    showNotification('تم إعادة تعيين جميع الفلاتر', 'info', 3000);
}

/**
 * الحصول على قائمة الأقسام المتاحة
 */
function getAvailableDepartments() {
    if (!window.employees || window.employees.length === 0) {
        return [];
    }

    const departments = [...new Set(window.employees.map(emp => emp.department))];
    return departments.sort();
}

/**
 * الحصول على قائمة حالات الرصيد المتاحة
 */
function getAvailableStatuses() {
    return ['طبيعي', 'منخفض', 'نفد الرصيد', 'مرتفع'];
}

/**
 * تحديث قائمة الأقسام في فلتر البحث
 */
function updateDepartmentFilter() {
    const departmentFilters = [
        document.getElementById('departmentFilter'),
        document.getElementById('mainDepartmentFilter')
    ];

    const departments = getAvailableDepartments();

    departmentFilters.forEach(departmentFilter => {
        if (!departmentFilter) return;

        // الاحتفاظ بالقيمة المحددة حالياً
        const currentValue = departmentFilter.value;

        // مسح الخيارات الحالية (عدا الخيار الأول)
        departmentFilter.innerHTML = '<option value="">جميع الأقسام</option>';

        // إضافة الأقسام المتاحة
        departments.forEach(dept => {
            const option = document.createElement('option');
            option.value = dept;
            option.textContent = dept;
            if (dept === currentValue) {
                option.selected = true;
            }
            departmentFilter.appendChild(option);
        });
    });
}


