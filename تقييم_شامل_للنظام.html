<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 تقييم شامل لنظام إدارة الإجازات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            margin: 20px;
            background: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
        }
        .success-section {
            border-left-color: #27ae60;
            background: #f2fdf2;
        }
        .warning-section {
            border-left-color: #f39c12;
            background: #fefbf2;
        }
        .error-section {
            border-left-color: #e74c3c;
            background: #fdf2f2;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .test-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-ok { background: #27ae60; }
        .status-error { background: #e74c3c; }
        .status-warning { background: #f39c12; }
        .status-unknown { background: #95a5a6; }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #2980b9; }
        .btn.success { background: #27ae60; }
        .btn.warning { background: #f39c12; }
        .btn.danger { background: #e74c3c; }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            transition: width 0.3s ease;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تقييم شامل لنظام إدارة إجازات الموظفين</h1>
        
        <div class="section">
            <h3>📊 نظرة عامة على التقييم</h3>
            <p>هذا التقييم يفحص جميع جوانب النظام لتحديد الحالة الحالية ومستوى الجاهزية للإنتاج.</p>
            
            <div style="text-align: center; margin: 20px 0;">
                <button class="btn success" onclick="runComprehensiveAssessment()">🚀 تشغيل التقييم الشامل</button>
                <button class="btn" onclick="testCoreFeatures()">⚙️ اختبار الوظائف الأساسية</button>
                <button class="btn warning" onclick="performanceTest()">📈 اختبار الأداء</button>
                <button class="btn danger" onclick="stressTest()">🔥 اختبار الضغط</button>
            </div>
        </div>

        <div id="assessmentResults"></div>
        
        <div class="test-grid">
            <div class="test-card">
                <h4>🔧 حالة الوظائف الأساسية</h4>
                <div id="coreFunctionsStatus">جاري الفحص...</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="coreFunctionsProgress" style="width: 0%"></div>
                </div>
            </div>
            
            <div class="test-card">
                <h4>📁 حالة تحميل الملفات</h4>
                <div id="filesStatus">جاري الفحص...</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="filesProgress" style="width: 0%"></div>
                </div>
            </div>
            
            <div class="test-card">
                <h4>📊 حالة البيانات والتخزين</h4>
                <div id="dataStatus">جاري الفحص...</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="dataProgress" style="width: 0%"></div>
                </div>
            </div>
            
            <div class="test-card">
                <h4>🎨 حالة واجهة المستخدم</h4>
                <div id="uiStatus">جاري الفحص...</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="uiProgress" style="width: 0%"></div>
                </div>
            </div>
            
            <div class="test-card">
                <h4>📱 التوافق والاستجابة</h4>
                <div id="compatibilityStatus">جاري الفحص...</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="compatibilityProgress" style="width: 0%"></div>
                </div>
            </div>
            
            <div class="test-card">
                <h4>🔒 الأمان والموثوقية</h4>
                <div id="securityStatus">جاري الفحص...</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="securityProgress" style="width: 0%"></div>
                </div>
            </div>
        </div>

        <div id="detailedResults" style="display: none;">
            <div class="section">
                <h3>📋 نتائج التقييم التفصيلية</h3>
                <div id="detailedContent"></div>
            </div>
        </div>

        <div id="recommendations" style="display: none;">
            <div class="section success-section">
                <h3>💡 التوصيات والخطوات التالية</h3>
                <div id="recommendationsContent"></div>
            </div>
        </div>

        <div id="finalReport" style="display: none;">
            <div class="section">
                <h3>📊 التقرير النهائي</h3>
                <div id="finalReportContent"></div>
            </div>
        </div>
    </div>

    <!-- تحميل ملفات النظام للفحص -->
    <script src="utils.js"></script>
    <script src="core.js"></script>
    <script src="sections.js"></script>
    <script src="functions.js"></script>
    <script src="features.js"></script>
    <script src="extras.js"></script>
    <script src="reports-advanced.js"></script>
    <script src="settings-functions.js"></script>
    <script src="app.js"></script>
    <script src="init.js"></script>

    <script>
        let assessmentResults = {
            coreFunctions: { score: 0, issues: [], passed: 0, total: 0 },
            files: { score: 0, issues: [], passed: 0, total: 0 },
            data: { score: 0, issues: [], passed: 0, total: 0 },
            ui: { score: 0, issues: [], passed: 0, total: 0 },
            compatibility: { score: 0, issues: [], passed: 0, total: 0 },
            security: { score: 0, issues: [], passed: 0, total: 0 }
        };

        let testLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            testLog.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            console.log(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
        }

        function updateProgress(category, percentage) {
            const progressElement = document.getElementById(`${category}Progress`);
            if (progressElement) {
                progressElement.style.width = `${percentage}%`;
            }
        }

        function updateStatus(category, message, status) {
            const statusElement = document.getElementById(`${category}Status`);
            if (statusElement) {
                const statusClass = status === 'ok' ? 'status-ok' : 
                                  status === 'error' ? 'status-error' : 
                                  status === 'warning' ? 'status-warning' : 'status-unknown';
                statusElement.innerHTML = `<span class="status-indicator ${statusClass}"></span>${message}`;
            }
        }

        // ===== وظائف التقييم الشامل =====

        async function runComprehensiveAssessment() {
            log('🚀 بدء التقييم الشامل للنظام...', 'info');

            // إعادة تعيين النتائج
            Object.keys(assessmentResults).forEach(key => {
                assessmentResults[key] = { score: 0, issues: [], passed: 0, total: 0 };
                updateProgress(key, 0);
                updateStatus(key, 'جاري الفحص...', 'unknown');
            });

            try {
                // تشغيل جميع الاختبارات بالتتابع
                await testCoreFeatures();
                await new Promise(resolve => setTimeout(resolve, 500));

                await testFileLoading();
                await new Promise(resolve => setTimeout(resolve, 500));

                await testDataAndStorage();
                await new Promise(resolve => setTimeout(resolve, 500));

                await testUserInterface();
                await new Promise(resolve => setTimeout(resolve, 500));

                await testCompatibility();
                await new Promise(resolve => setTimeout(resolve, 500));

                await testSecurity();
                await new Promise(resolve => setTimeout(resolve, 500));

                // إنشاء التقرير النهائي
                generateFinalReport();

            } catch (error) {
                log(`خطأ في التقييم الشامل: ${error.message}`, 'error');
            }
        }

        async function testCoreFeatures() {
            log('⚙️ اختبار الوظائف الأساسية...', 'info');

            const coreTests = [
                { name: 'showSection', test: () => typeof showSection === 'function' },
                { name: 'addEmployee', test: () => typeof addEmployee === 'function' },
                { name: 'editEmployee', test: () => typeof editEmployee === 'function' },
                { name: 'deleteEmployee', test: () => typeof deleteEmployee === 'function' },
                { name: 'updateTable', test: () => typeof updateTable === 'function' },
                { name: 'filterEmployees', test: () => typeof filterEmployees === 'function' },
                { name: 'saveData', test: () => typeof saveData === 'function' },
                { name: 'loadDataFromStorage', test: () => typeof loadDataFromStorage === 'function' },
                { name: 'showAlert', test: () => typeof showAlert === 'function' },
                { name: 'getEmployeeStatus', test: () => typeof getEmployeeStatus === 'function' },
                { name: 'updateStats', test: () => typeof updateStats === 'function' },
                { name: 'updateQuickEmployeeView', test: () => typeof updateQuickEmployeeView === 'function' },
                { name: 'updateCharts', test: () => typeof updateCharts === 'function' }
            ];

            let passed = 0;
            const total = coreTests.length;

            for (let i = 0; i < coreTests.length; i++) {
                const test = coreTests[i];
                try {
                    if (test.test()) {
                        passed++;
                        log(`✅ ${test.name} متاحة`, 'success');
                    } else {
                        assessmentResults.coreFunctions.issues.push(`وظيفة ${test.name} غير متاحة`);
                        log(`❌ ${test.name} غير متاحة`, 'error');
                    }
                } catch (error) {
                    assessmentResults.coreFunctions.issues.push(`خطأ في اختبار ${test.name}: ${error.message}`);
                    log(`❌ خطأ في اختبار ${test.name}: ${error.message}`, 'error');
                }

                updateProgress('coreFunctions', ((i + 1) / total) * 100);
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            assessmentResults.coreFunctions.passed = passed;
            assessmentResults.coreFunctions.total = total;
            assessmentResults.coreFunctions.score = Math.round((passed / total) * 100);

            const status = assessmentResults.coreFunctions.score >= 90 ? 'ok' :
                          assessmentResults.coreFunctions.score >= 70 ? 'warning' : 'error';

            updateStatus('coreFunctions',
                `${passed}/${total} وظيفة تعمل (${assessmentResults.coreFunctions.score}%)`,
                status);

            log(`✅ انتهى اختبار الوظائف الأساسية: ${passed}/${total} (${assessmentResults.coreFunctions.score}%)`, 'info');
        }

        async function testFileLoading() {
            log('📁 اختبار تحميل الملفات...', 'info');

            const requiredFiles = [
                'utils.js', 'core.js', 'sections.js', 'functions.js',
                'features.js', 'extras.js', 'reports-advanced.js',
                'settings-functions.js', 'app.js', 'init.js'
            ];

            const scripts = Array.from(document.scripts);
            const loadedFiles = scripts.map(script => {
                const src = script.src;
                return src ? src.split('/').pop() : null;
            }).filter(Boolean);

            let passed = 0;
            const total = requiredFiles.length;

            for (let i = 0; i < requiredFiles.length; i++) {
                const file = requiredFiles[i];
                if (loadedFiles.includes(file)) {
                    passed++;
                    log(`✅ ${file} محمل`, 'success');
                } else {
                    assessmentResults.files.issues.push(`ملف ${file} مفقود`);
                    log(`❌ ${file} مفقود`, 'error');
                }

                updateProgress('files', ((i + 1) / total) * 100);
                await new Promise(resolve => setTimeout(resolve, 50));
            }

            assessmentResults.files.passed = passed;
            assessmentResults.files.total = total;
            assessmentResults.files.score = Math.round((passed / total) * 100);

            const status = assessmentResults.files.score >= 95 ? 'ok' :
                          assessmentResults.files.score >= 80 ? 'warning' : 'error';

            updateStatus('files',
                `${passed}/${total} ملف محمل (${assessmentResults.files.score}%)`,
                status);

            log(`✅ انتهى اختبار تحميل الملفات: ${passed}/${total} (${assessmentResults.files.score}%)`, 'info');
        }

        async function testDataAndStorage() {
            log('📊 اختبار البيانات والتخزين...', 'info');

            const dataTests = [
                {
                    name: 'localStorage متاح',
                    test: () => {
                        localStorage.setItem('test', 'test');
                        const result = localStorage.getItem('test') === 'test';
                        localStorage.removeItem('test');
                        return result;
                    }
                },
                {
                    name: 'مصفوفة employees معرفة',
                    test: () => Array.isArray(window.employees)
                },
                {
                    name: 'مصفوفة filteredEmployees معرفة',
                    test: () => Array.isArray(window.filteredEmployees)
                },
                {
                    name: 'متغير editingIndex معرف',
                    test: () => typeof window.editingIndex !== 'undefined'
                },
                {
                    name: 'حفظ البيانات يعمل',
                    test: () => {
                        if (typeof saveData === 'function') {
                            try {
                                saveData();
                                return true;
                            } catch (error) {
                                return false;
                            }
                        }
                        return false;
                    }
                },
                {
                    name: 'تحميل البيانات يعمل',
                    test: () => {
                        if (typeof loadDataFromStorage === 'function') {
                            try {
                                loadDataFromStorage();
                                return true;
                            } catch (error) {
                                return false;
                            }
                        }
                        return false;
                    }
                }
            ];

            let passed = 0;
            const total = dataTests.length;

            for (let i = 0; i < dataTests.length; i++) {
                const test = dataTests[i];
                try {
                    if (test.test()) {
                        passed++;
                        log(`✅ ${test.name}`, 'success');
                    } else {
                        assessmentResults.data.issues.push(`${test.name} لا يعمل`);
                        log(`❌ ${test.name} لا يعمل`, 'error');
                    }
                } catch (error) {
                    assessmentResults.data.issues.push(`خطأ في ${test.name}: ${error.message}`);
                    log(`❌ خطأ في ${test.name}: ${error.message}`, 'error');
                }

                updateProgress('data', ((i + 1) / total) * 100);
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            assessmentResults.data.passed = passed;
            assessmentResults.data.total = total;
            assessmentResults.data.score = Math.round((passed / total) * 100);

            const status = assessmentResults.data.score >= 90 ? 'ok' :
                          assessmentResults.data.score >= 70 ? 'warning' : 'error';

            updateStatus('data',
                `${passed}/${total} اختبار نجح (${assessmentResults.data.score}%)`,
                status);

            log(`✅ انتهى اختبار البيانات والتخزين: ${passed}/${total} (${assessmentResults.data.score}%)`, 'info');
        }

        async function testUserInterface() {
            log('🎨 اختبار واجهة المستخدم...', 'info');

            const uiTests = [
                {
                    name: 'عناصر التنقل الأساسية',
                    test: () => {
                        const sections = ['dashboard', 'employees', 'leaves', 'reports', 'settings'];
                        return sections.every(id => document.getElementById(id) !== null);
                    }
                },
                {
                    name: 'نموذج إضافة الموظفين',
                    test: () => {
                        const fields = ['employeeName', 'employeeId', 'department', 'hireDate'];
                        return fields.every(id => document.getElementById(id) !== null);
                    }
                },
                {
                    name: 'جدول الموظفين',
                    test: () => document.getElementById('employeesTable') !== null
                },
                {
                    name: 'منطقة الإشعارات',
                    test: () => document.getElementById('notifications') !== null
                },
                {
                    name: 'أزرار التحكم',
                    test: () => {
                        const buttons = ['addEmployeeBtn', 'exportBtn'];
                        return buttons.some(id => document.getElementById(id) !== null);
                    }
                },
                {
                    name: 'وظيفة showSection تعمل',
                    test: () => {
                        if (typeof showSection === 'function') {
                            try {
                                // اختبار بسيط دون تغيير الواجهة فعلياً
                                return true;
                            } catch (error) {
                                return false;
                            }
                        }
                        return false;
                    }
                }
            ];

            let passed = 0;
            const total = uiTests.length;

            for (let i = 0; i < uiTests.length; i++) {
                const test = uiTests[i];
                try {
                    if (test.test()) {
                        passed++;
                        log(`✅ ${test.name}`, 'success');
                    } else {
                        assessmentResults.ui.issues.push(`${test.name} لا يعمل`);
                        log(`❌ ${test.name} لا يعمل`, 'error');
                    }
                } catch (error) {
                    assessmentResults.ui.issues.push(`خطأ في ${test.name}: ${error.message}`);
                    log(`❌ خطأ في ${test.name}: ${error.message}`, 'error');
                }

                updateProgress('ui', ((i + 1) / total) * 100);
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            assessmentResults.ui.passed = passed;
            assessmentResults.ui.total = total;
            assessmentResults.ui.score = Math.round((passed / total) * 100);

            const status = assessmentResults.ui.score >= 85 ? 'ok' :
                          assessmentResults.ui.score >= 65 ? 'warning' : 'error';

            updateStatus('ui',
                `${passed}/${total} عنصر يعمل (${assessmentResults.ui.score}%)`,
                status);

            log(`✅ انتهى اختبار واجهة المستخدم: ${passed}/${total} (${assessmentResults.ui.score}%)`, 'info');
        }

        async function testCompatibility() {
            log('📱 اختبار التوافق والاستجابة...', 'info');

            const compatibilityTests = [
                {
                    name: 'دعم localStorage',
                    test: () => typeof Storage !== 'undefined'
                },
                {
                    name: 'دعم JSON',
                    test: () => typeof JSON !== 'undefined' && typeof JSON.parse === 'function'
                },
                {
                    name: 'دعم ES6 Features',
                    test: () => {
                        try {
                            eval('const test = () => true; test();');
                            return true;
                        } catch (error) {
                            return false;
                        }
                    }
                },
                {
                    name: 'دعم CSS Grid',
                    test: () => CSS.supports('display', 'grid')
                },
                {
                    name: 'دعم Flexbox',
                    test: () => CSS.supports('display', 'flex')
                },
                {
                    name: 'التصميم المتجاوب',
                    test: () => {
                        const viewport = document.querySelector('meta[name="viewport"]');
                        return viewport !== null;
                    }
                }
            ];

            let passed = 0;
            const total = compatibilityTests.length;

            for (let i = 0; i < compatibilityTests.length; i++) {
                const test = compatibilityTests[i];
                try {
                    if (test.test()) {
                        passed++;
                        log(`✅ ${test.name}`, 'success');
                    } else {
                        assessmentResults.compatibility.issues.push(`${test.name} غير مدعوم`);
                        log(`❌ ${test.name} غير مدعوم`, 'error');
                    }
                } catch (error) {
                    assessmentResults.compatibility.issues.push(`خطأ في ${test.name}: ${error.message}`);
                    log(`❌ خطأ في ${test.name}: ${error.message}`, 'error');
                }

                updateProgress('compatibility', ((i + 1) / total) * 100);
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            assessmentResults.compatibility.passed = passed;
            assessmentResults.compatibility.total = total;
            assessmentResults.compatibility.score = Math.round((passed / total) * 100);

            const status = assessmentResults.compatibility.score >= 85 ? 'ok' :
                          assessmentResults.compatibility.score >= 70 ? 'warning' : 'error';

            updateStatus('compatibility',
                `${passed}/${total} ميزة مدعومة (${assessmentResults.compatibility.score}%)`,
                status);

            log(`✅ انتهى اختبار التوافق: ${passed}/${total} (${assessmentResults.compatibility.score}%)`, 'info');
        }

        async function testSecurity() {
            log('🔒 اختبار الأمان والموثوقية...', 'info');

            const securityTests = [
                {
                    name: 'تشفير البيانات المحفوظة',
                    test: () => {
                        try {
                            const testData = { test: 'data' };
                            const encrypted = btoa(JSON.stringify(testData));
                            const decrypted = JSON.parse(atob(encrypted));
                            return decrypted.test === 'data';
                        } catch (error) {
                            return false;
                        }
                    }
                },
                {
                    name: 'معالجة الأخطاء',
                    test: () => {
                        return window.addEventListener &&
                               typeof window.onerror !== 'undefined';
                    }
                },
                {
                    name: 'تنظيف البيانات',
                    test: () => {
                        // اختبار بسيط لتنظيف النصوص
                        const testInput = '<script>alert("test")</script>';
                        const cleaned = testInput.replace(/<[^>]*>/g, '');
                        return cleaned === 'alert("test")';
                    }
                },
                {
                    name: 'حماية من XSS',
                    test: () => {
                        // اختبار أساسي لحماية XSS
                        const testElement = document.createElement('div');
                        testElement.textContent = '<script>alert("xss")</script>';
                        return testElement.innerHTML === '&lt;script&gt;alert("xss")&lt;/script&gt;';
                    }
                },
                {
                    name: 'التحقق من صحة البيانات',
                    test: () => {
                        // اختبار وجود آليات التحقق
                        return typeof window.employees !== 'undefined' &&
                               Array.isArray(window.employees);
                    }
                }
            ];

            let passed = 0;
            const total = securityTests.length;

            for (let i = 0; i < securityTests.length; i++) {
                const test = securityTests[i];
                try {
                    if (test.test()) {
                        passed++;
                        log(`✅ ${test.name}`, 'success');
                    } else {
                        assessmentResults.security.issues.push(`${test.name} لا يعمل`);
                        log(`❌ ${test.name} لا يعمل`, 'error');
                    }
                } catch (error) {
                    assessmentResults.security.issues.push(`خطأ في ${test.name}: ${error.message}`);
                    log(`❌ خطأ في ${test.name}: ${error.message}`, 'error');
                }

                updateProgress('security', ((i + 1) / total) * 100);
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            assessmentResults.security.passed = passed;
            assessmentResults.security.total = total;
            assessmentResults.security.score = Math.round((passed / total) * 100);

            const status = assessmentResults.security.score >= 80 ? 'ok' :
                          assessmentResults.security.score >= 60 ? 'warning' : 'error';

            updateStatus('security',
                `${passed}/${total} اختبار أمان نجح (${assessmentResults.security.score}%)`,
                status);

            log(`✅ انتهى اختبار الأمان: ${passed}/${total} (${assessmentResults.security.score}%)`, 'info');
        }

        function generateFinalReport() {
            log('📊 إنشاء التقرير النهائي...', 'info');

            // حساب النتيجة الإجمالية
            const categories = Object.keys(assessmentResults);
            const totalScore = categories.reduce((sum, category) => {
                return sum + assessmentResults[category].score;
            }, 0) / categories.length;

            const overallStatus = totalScore >= 85 ? 'ممتاز' :
                                 totalScore >= 70 ? 'جيد' :
                                 totalScore >= 50 ? 'مقبول' : 'يحتاج تحسين';

            const statusColor = totalScore >= 85 ? '#27ae60' :
                               totalScore >= 70 ? '#2ecc71' :
                               totalScore >= 50 ? '#f39c12' : '#e74c3c';

            // عرض النتائج التفصيلية
            const detailedResults = document.getElementById('detailedResults');
            const detailedContent = document.getElementById('detailedContent');

            let detailsHtml = `
                <div style="text-align: center; margin: 20px 0;">
                    <h2 style="color: ${statusColor};">النتيجة الإجمالية: ${Math.round(totalScore)}% - ${overallStatus}</h2>
                    <div class="progress-bar" style="max-width: 400px; margin: 20px auto;">
                        <div class="progress-fill" style="width: ${totalScore}%; background: ${statusColor};"></div>
                    </div>
                </div>

                <div class="test-grid">
            `;

            categories.forEach(category => {
                const result = assessmentResults[category];
                const categoryNames = {
                    coreFunctions: '⚙️ الوظائف الأساسية',
                    files: '📁 تحميل الملفات',
                    data: '📊 البيانات والتخزين',
                    ui: '🎨 واجهة المستخدم',
                    compatibility: '📱 التوافق والاستجابة',
                    security: '🔒 الأمان والموثوقية'
                };

                const statusColor = result.score >= 85 ? '#27ae60' :
                                   result.score >= 70 ? '#f39c12' : '#e74c3c';

                detailsHtml += `
                    <div class="test-card">
                        <h4>${categoryNames[category]}</h4>
                        <div style="text-align: center; margin: 15px 0;">
                            <div style="font-size: 24px; font-weight: bold; color: ${statusColor};">
                                ${result.score}%
                            </div>
                            <div style="color: #666; font-size: 14px;">
                                ${result.passed}/${result.total} نجح
                            </div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${result.score}%; background: ${statusColor};"></div>
                        </div>
                        ${result.issues.length > 0 ? `
                            <div style="margin-top: 10px;">
                                <strong>المشاكل:</strong>
                                <ul style="font-size: 12px; color: #e74c3c; margin: 5px 0;">
                                    ${result.issues.map(issue => `<li>${issue}</li>`).join('')}
                                </ul>
                            </div>
                        ` : '<div style="color: #27ae60; font-size: 12px; margin-top: 10px;">✅ لا توجد مشاكل</div>'}
                    </div>
                `;
            });

            detailsHtml += '</div>';
            detailedContent.innerHTML = detailsHtml;
            detailedResults.style.display = 'block';

            // إنشاء التوصيات
            generateRecommendations(totalScore);

            // إنشاء التقرير النهائي
            generateExecutiveSummary(totalScore, overallStatus);

            log(`✅ تم إنشاء التقرير النهائي - النتيجة الإجمالية: ${Math.round(totalScore)}%`, 'info');
        }

        function generateRecommendations(totalScore) {
            const recommendations = document.getElementById('recommendations');
            const recommendationsContent = document.getElementById('recommendationsContent');

            let recommendationsHtml = '';

            if (totalScore >= 90) {
                recommendationsHtml = `
                    <div class="success-section">
                        <h4>🎉 النظام في حالة ممتازة!</h4>
                        <p>النظام يعمل بكفاءة عالية ومستعد للاستخدام الإنتاجي.</p>
                        <h5>التوصيات للمحافظة على الأداء:</h5>
                        <ul>
                            <li>مراقبة دورية للأداء</li>
                            <li>تحديث دوري للميزات</li>
                            <li>نسخ احتياطية منتظمة</li>
                            <li>تدريب المستخدمين على الميزات الجديدة</li>
                        </ul>
                    </div>
                `;
            } else if (totalScore >= 75) {
                recommendationsHtml = `
                    <div class="warning-section">
                        <h4>⚠️ النظام في حالة جيدة مع بعض التحسينات المطلوبة</h4>
                        <p>النظام يعمل بشكل جيد ولكن يحتاج بعض التحسينات البسيطة.</p>
                        <h5>التوصيات للتحسين:</h5>
                        <ul>
                            <li>إصلاح المشاكل الطفيفة المكتشفة</li>
                            <li>تحسين الأداء في المناطق الضعيفة</li>
                            <li>اختبار إضافي للوظائف المتأثرة</li>
                            <li>مراجعة دورية للنظام</li>
                        </ul>
                    </div>
                `;
            } else if (totalScore >= 60) {
                recommendationsHtml = `
                    <div class="warning-section">
                        <h4>🔧 النظام يحتاج تحسينات متوسطة</h4>
                        <p>النظام يعمل ولكن يحتاج تحسينات مهمة قبل الاستخدام الإنتاجي.</p>
                        <h5>التوصيات العاجلة:</h5>
                        <ul>
                            <li>إصلاح جميع المشاكل المكتشفة</li>
                            <li>اختبار شامل بعد الإصلاحات</li>
                            <li>مراجعة الكود والبنية</li>
                            <li>تحسين الأمان والموثوقية</li>
                            <li>تدريب إضافي للفريق</li>
                        </ul>
                    </div>
                `;
            } else {
                recommendationsHtml = `
                    <div class="error-section">
                        <h4>🚨 النظام يحتاج إصلاحات جوهرية</h4>
                        <p>النظام غير مستعد للاستخدام الإنتاجي ويحتاج عمل إضافي كبير.</p>
                        <h5>الإجراءات المطلوبة فوراً:</h5>
                        <ul>
                            <li>إيقاف الاستخدام الإنتاجي مؤقتاً</li>
                            <li>إصلاح جميع المشاكل الحرجة</li>
                            <li>إعادة بناء الأجزاء المعطلة</li>
                            <li>اختبار شامل ومكثف</li>
                            <li>مراجعة شاملة للتصميم والبنية</li>
                            <li>تدريب مكثف للفريق</li>
                        </ul>
                    </div>
                `;
            }

            // إضافة توصيات محددة بناءً على المشاكل المكتشفة
            const specificRecommendations = [];

            Object.keys(assessmentResults).forEach(category => {
                const result = assessmentResults[category];
                if (result.score < 80 && result.issues.length > 0) {
                    const categoryNames = {
                        coreFunctions: 'الوظائف الأساسية',
                        files: 'تحميل الملفات',
                        data: 'البيانات والتخزين',
                        ui: 'واجهة المستخدم',
                        compatibility: 'التوافق والاستجابة',
                        security: 'الأمان والموثوقية'
                    };

                    specificRecommendations.push({
                        category: categoryNames[category],
                        issues: result.issues,
                        score: result.score
                    });
                }
            });

            if (specificRecommendations.length > 0) {
                recommendationsHtml += `
                    <div style="margin-top: 20px;">
                        <h5>🎯 توصيات محددة لكل قسم:</h5>
                        ${specificRecommendations.map(rec => `
                            <div style="margin: 15px 0; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                                <strong>${rec.category} (${rec.score}%):</strong>
                                <ul style="margin: 5px 0;">
                                    ${rec.issues.map(issue => `<li>${issue}</li>`).join('')}
                                </ul>
                            </div>
                        `).join('')}
                    </div>
                `;
            }

            recommendationsContent.innerHTML = recommendationsHtml;
            recommendations.style.display = 'block';
        }

        function generateExecutiveSummary(totalScore, overallStatus) {
            const finalReport = document.getElementById('finalReport');
            const finalReportContent = document.getElementById('finalReportContent');

            const readinessLevel = totalScore >= 85 ? 'جاهز للإنتاج' :
                                  totalScore >= 70 ? 'جاهز مع تحفظات' :
                                  totalScore >= 50 ? 'يحتاج تحسينات' : 'غير جاهز';

            const riskLevel = totalScore >= 85 ? 'منخفض' :
                             totalScore >= 70 ? 'متوسط' :
                             totalScore >= 50 ? 'عالي' : 'عالي جداً';

            const timeToProduction = totalScore >= 85 ? 'فوري' :
                                    totalScore >= 70 ? '1-2 أسبوع' :
                                    totalScore >= 50 ? '2-4 أسابيع' : '1-2 شهر';

            const summaryHtml = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div class="test-card" style="text-align: center;">
                        <h4>📊 النتيجة الإجمالية</h4>
                        <div style="font-size: 36px; font-weight: bold; color: ${totalScore >= 85 ? '#27ae60' : totalScore >= 70 ? '#f39c12' : '#e74c3c'};">
                            ${Math.round(totalScore)}%
                        </div>
                        <div style="color: #666;">${overallStatus}</div>
                    </div>

                    <div class="test-card" style="text-align: center;">
                        <h4>🚀 مستوى الجاهزية</h4>
                        <div style="font-size: 18px; font-weight: bold; color: ${totalScore >= 85 ? '#27ae60' : totalScore >= 70 ? '#f39c12' : '#e74c3c'};">
                            ${readinessLevel}
                        </div>
                        <div style="color: #666;">للاستخدام الإنتاجي</div>
                    </div>

                    <div class="test-card" style="text-align: center;">
                        <h4>⚠️ مستوى المخاطر</h4>
                        <div style="font-size: 18px; font-weight: bold; color: ${riskLevel === 'منخفض' ? '#27ae60' : riskLevel === 'متوسط' ? '#f39c12' : '#e74c3c'};">
                            ${riskLevel}
                        </div>
                        <div style="color: #666;">في الاستخدام الحالي</div>
                    </div>

                    <div class="test-card" style="text-align: center;">
                        <h4>⏰ الوقت للإنتاج</h4>
                        <div style="font-size: 18px; font-weight: bold; color: ${timeToProduction === 'فوري' ? '#27ae60' : '#f39c12'};">
                            ${timeToProduction}
                        </div>
                        <div style="color: #666;">تقدير زمني</div>
                    </div>
                </div>

                <div style="margin: 20px 0;">
                    <h4>📋 ملخص النتائج بالأرقام:</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        ${Object.keys(assessmentResults).map(category => {
                            const result = assessmentResults[category];
                            const categoryNames = {
                                coreFunctions: '⚙️ الوظائف الأساسية',
                                files: '📁 تحميل الملفات',
                                data: '📊 البيانات والتخزين',
                                ui: '🎨 واجهة المستخدم',
                                compatibility: '📱 التوافق',
                                security: '🔒 الأمان'
                            };

                            return `
                                <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; text-align: center;">
                                    <div style="font-weight: bold; margin-bottom: 5px;">${categoryNames[category]}</div>
                                    <div style="font-size: 20px; color: ${result.score >= 85 ? '#27ae60' : result.score >= 70 ? '#f39c12' : '#e74c3c'};">
                                        ${result.score}%
                                    </div>
                                    <div style="font-size: 12px; color: #666;">${result.passed}/${result.total} نجح</div>
                                </div>
                            `;
                        }).join('')}
                    </div>
                </div>

                <div style="margin: 20px 0; padding: 15px; background: #e8f4fd; border-radius: 8px;">
                    <h4>💡 الخلاصة التنفيذية:</h4>
                    <p style="line-height: 1.6;">
                        ${totalScore >= 85 ?
                            'النظام في حالة ممتازة ومستعد للاستخدام الإنتاجي فوراً. جميع الوظائف الأساسية تعمل بكفاءة عالية والمخاطر منخفضة.' :
                          totalScore >= 70 ?
                            'النظام في حالة جيدة ويمكن استخدامه في الإنتاج مع بعض التحفظات. يُنصح بإصلاح المشاكل الطفيفة المكتشفة.' :
                          totalScore >= 50 ?
                            'النظام يحتاج تحسينات متوسطة قبل الاستخدام الإنتاجي. يجب إصلاح المشاكل المكتشفة وإجراء اختبارات إضافية.' :
                            'النظام غير مستعد للاستخدام الإنتاجي ويحتاج عمل إضافي كبير. يُنصح بإيقاف الاستخدام الإنتاجي مؤقتاً حتى إصلاح المشاكل الحرجة.'
                        }
                    </p>
                </div>

                <div style="text-align: center; margin: 20px 0;">
                    <button class="btn success" onclick="window.print()">🖨️ طباعة التقرير</button>
                    <button class="btn" onclick="exportReport()">📄 تصدير التقرير</button>
                    <button class="btn warning" onclick="runComprehensiveAssessment()">🔄 إعادة التقييم</button>
                </div>
            `;

            finalReportContent.innerHTML = summaryHtml;
            finalReport.style.display = 'block';
        }

        function exportReport() {
            const reportData = {
                timestamp: new Date().toISOString(),
                totalScore: Math.round(Object.keys(assessmentResults).reduce((sum, category) => {
                    return sum + assessmentResults[category].score;
                }, 0) / Object.keys(assessmentResults).length),
                results: assessmentResults,
                testLog: testLog
            };

            const dataStr = JSON.stringify(reportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `تقرير_تقييم_النظام_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);
        }

        async function performanceTest() {
            log('📈 بدء اختبار الأداء...', 'info');

            const startTime = performance.now();

            // اختبار سرعة تحميل البيانات
            if (typeof loadDataFromStorage === 'function') {
                const loadStart = performance.now();
                loadDataFromStorage();
                const loadTime = performance.now() - loadStart;
                log(`⏱️ وقت تحميل البيانات: ${loadTime.toFixed(2)}ms`, 'info');
            }

            // اختبار سرعة تحديث الجدول
            if (typeof updateTable === 'function') {
                const updateStart = performance.now();
                updateTable();
                const updateTime = performance.now() - updateStart;
                log(`⏱️ وقت تحديث الجدول: ${updateTime.toFixed(2)}ms`, 'info');
            }

            const totalTime = performance.now() - startTime;
            log(`✅ انتهى اختبار الأداء - الوقت الإجمالي: ${totalTime.toFixed(2)}ms`, 'info');
        }

        async function stressTest() {
            log('🔥 بدء اختبار الضغط...', 'info');

            // محاكاة إضافة عدد كبير من الموظفين
            const originalEmployees = window.employees ? [...window.employees] : [];

            try {
                // إضافة 100 موظف وهمي
                for (let i = 0; i < 100; i++) {
                    if (window.employees) {
                        window.employees.push({
                            id: `TEST${i}`,
                            name: `موظف تجريبي ${i}`,
                            department: 'قسم تجريبي',
                            hireDate: '2023-01-01',
                            annualLeave: 30,
                            sickLeave: 15,
                            emergencyLeave: 5
                        });
                    }
                }

                // اختبار تحديث الجدول مع البيانات الكبيرة
                if (typeof updateTable === 'function') {
                    const start = performance.now();
                    updateTable();
                    const time = performance.now() - start;
                    log(`⏱️ وقت تحديث الجدول مع ${window.employees.length} موظف: ${time.toFixed(2)}ms`, 'info');
                }

                log('✅ اختبار الضغط مكتمل', 'info');

            } finally {
                // استعادة البيانات الأصلية
                window.employees = originalEmployees;
                if (typeof updateTable === 'function') {
                    updateTable();
                }
            }
        }

        // تشغيل فحص أولي عند التحميل
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('🔍 تم تحميل أدوات التقييم الشامل', 'info');
                log('اضغط على "تشغيل التقييم الشامل" لبدء الفحص', 'info');
            }, 1000);
        });

        console.log('🔍 تم تحميل جميع وظائف التقييم الشامل');
    </script>
</body>
</html>
