<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الإجازات المتكامل</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #0078d4;
            color: white;
            padding: 20px 0;
            text-align: center;
            border-radius: 5px 5px 0 0;
            margin-bottom: 20px;
        }
        h1 {
            margin: 0;
            font-size: 28px;
        }
        .dashboard {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            flex: 1 1 300px;
        }
        .card h2 {
            color: #0078d4;
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .menu {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 30px;
        }
        .menu-item {
            background-color: #0078d4;
            color: white;
            padding: 15px 25px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: bold;
            text-align: center;
            flex: 1 1 200px;
            transition: background-color 0.3s;
        }
        .menu-item:hover {
            background-color: #005a9e;
        }
        .menu-item.green {
            background-color: #107c10;
        }
        .menu-item.green:hover {
            background-color: #0b5e0b;
        }
        .menu-item.orange {
            background-color: #d83b01;
        }
        .menu-item.orange:hover {
            background-color: #a32d01;
        }
        .menu-item.red {
            background-color: #e81123;
        }
        .menu-item.red:hover {
            background-color: #b80d1d;
        }
        .menu-item.purple {
            background-color: #5c2d91;
        }
        .menu-item.purple:hover {
            background-color: #4a2276;
        }
        form {
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background-color: #0078d4;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        button:hover {
            background-color: #005a9e;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: right;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .alert-info {
            background-color: #d9edf7;
            border: 1px solid #bce8f1;
            color: #31708f;
        }
        .alert-success {
            background-color: #dff0d8;
            border: 1px solid #d6e9c6;
            color: #3c763d;
        }
        .alert-warning {
            background-color: #fcf8e3;
            border: 1px solid #faebcc;
            color: #8a6d3b;
        }
        .alert-danger {
            background-color: #f2dede;
            border: 1px solid #ebccd1;
            color: #a94442;
        }
        footer {
            text-align: center;
            margin-top: 50px;
            padding: 20px;
            background-color: #f2f2f2;
            border-radius: 0 0 5px 5px;
        }
        @media (max-width: 768px) {
            .menu-item {
                flex: 1 1 100%;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>نظام الإجازات المتكامل</h1>
        </div>
    </header>

    <div class="container">
        <div class="alert alert-info">
            مرحباً بك في نظام الإجازات المتكامل. يمكنك استخدام هذه الواجهة للوصول إلى جميع ميزات النظام بسهولة.
        </div>

        <div class="menu">
            <a href="#dashboard" class="menu-item">لوحة المعلومات</a>
            <a href="#new-vacation" class="menu-item green">إدخال إجازة جديدة</a>
            <a href="#reports" class="menu-item orange">تقرير الإجازات</a>
            <a href="#balance" class="menu-item red">رصيد الإجازات</a>
            <a href="#alerts" class="menu-item purple">التنبيهات</a>
        </div>

        <section id="dashboard">
            <h2>لوحة المعلومات</h2>
            <div class="dashboard">
                <div class="card">
                    <h2>ملخص الإجازات</h2>
                    <p>إجمالي الإجازات: <strong>120</strong></p>
                    <p>الإجازات المعتمدة: <strong>95</strong></p>
                    <p>الإجازات قيد الانتظار: <strong>25</strong></p>
                </div>
                <div class="card">
                    <h2>الإجازات حسب النوع</h2>
                    <p>إجازات سنوية: <strong>65</strong></p>
                    <p>إجازات مرضية: <strong>30</strong></p>
                    <p>إجازات اضطرارية: <strong>15</strong></p>
                    <p>إجازات بدون راتب: <strong>10</strong></p>
                </div>
                <div class="card">
                    <h2>الإجازات حسب القسم</h2>
                    <p>قسم الموارد البشرية: <strong>20</strong></p>
                    <p>قسم تكنولوجيا المعلومات: <strong>25</strong></p>
                    <p>قسم المالية: <strong>15</strong></p>
                    <p>قسم العمليات: <strong>30</strong></p>
                    <p>قسم التسويق: <strong>30</strong></p>
                </div>
            </div>
        </section>

        <section id="new-vacation">
            <h2>إدخال إجازة جديدة</h2>
            <form>
                <div class="form-group">
                    <label for="employee-name">اسم الموظف:</label>
                    <input type="text" id="employee-name" name="employee-name" required>
                </div>
                <div class="form-group">
                    <label for="employee-id">الرقم الوظيفي:</label>
                    <input type="text" id="employee-id" name="employee-id" required>
                </div>
                <div class="form-group">
                    <label for="department">القسم:</label>
                    <select id="department" name="department" required>
                        <option value="">اختر القسم</option>
                        <option value="hr">الموارد البشرية</option>
                        <option value="it">تكنولوجيا المعلومات</option>
                        <option value="finance">المالية</option>
                        <option value="operations">العمليات</option>
                        <option value="marketing">التسويق</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="vacation-type">نوع الإجازة:</label>
                    <select id="vacation-type" name="vacation-type" required>
                        <option value="">اختر نوع الإجازة</option>
                        <option value="annual">سنوية</option>
                        <option value="sick">مرضية</option>
                        <option value="emergency">اضطرارية</option>
                        <option value="unpaid">بدون راتب</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="start-date">تاريخ البداية:</label>
                    <input type="date" id="start-date" name="start-date" required>
                </div>
                <div class="form-group">
                    <label for="end-date">تاريخ النهاية:</label>
                    <input type="date" id="end-date" name="end-date" required>
                </div>
                <div class="form-group">
                    <label for="days-count">عدد الأيام:</label>
                    <input type="number" id="days-count" name="days-count" readonly>
                </div>
                <div class="form-group">
                    <label for="notes">ملاحظات:</label>
                    <textarea id="notes" name="notes" rows="4"></textarea>
                </div>
                <button type="submit">حفظ الإجازة</button>
            </form>
        </section>

        <section id="reports">
            <h2>تقرير الإجازات</h2>
            <div class="form-group">
                <label for="filter-employee">تصفية حسب الموظف:</label>
                <input type="text" id="filter-employee" name="filter-employee">
            </div>
            <div class="form-group">
                <label for="filter-department">تصفية حسب القسم:</label>
                <select id="filter-department" name="filter-department">
                    <option value="">جميع الأقسام</option>
                    <option value="hr">الموارد البشرية</option>
                    <option value="it">تكنولوجيا المعلومات</option>
                    <option value="finance">المالية</option>
                    <option value="operations">العمليات</option>
                    <option value="marketing">التسويق</option>
                </select>
            </div>
            <div class="form-group">
                <label for="filter-type">تصفية حسب نوع الإجازة:</label>
                <select id="filter-type" name="filter-type">
                    <option value="">جميع الأنواع</option>
                    <option value="annual">سنوية</option>
                    <option value="sick">مرضية</option>
                    <option value="emergency">اضطرارية</option>
                    <option value="unpaid">بدون راتب</option>
                    <option value="other">أخرى</option>
                </select>
            </div>
            <button type="button">تطبيق التصفية</button>
            <button type="button">تصدير إلى Excel</button>
            <button type="button">طباعة التقرير</button>
            
            <table>
                <thead>
                    <tr>
                        <th>م</th>
                        <th>اسم الموظف</th>
                        <th>الرقم الوظيفي</th>
                        <th>القسم</th>
                        <th>نوع الإجازة</th>
                        <th>تاريخ البداية</th>
                        <th>تاريخ النهاية</th>
                        <th>عدد الأيام</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>أحمد محمد</td>
                        <td>1001</td>
                        <td>تكنولوجيا المعلومات</td>
                        <td>سنوية</td>
                        <td>2023/06/01</td>
                        <td>2023/06/15</td>
                        <td>15</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>سارة أحمد</td>
                        <td>1002</td>
                        <td>الموارد البشرية</td>
                        <td>مرضية</td>
                        <td>2023/07/10</td>
                        <td>2023/07/12</td>
                        <td>3</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>محمد علي</td>
                        <td>1003</td>
                        <td>المالية</td>
                        <td>اضطرارية</td>
                        <td>2023/08/05</td>
                        <td>2023/08/07</td>
                        <td>3</td>
                    </tr>
                </tbody>
            </table>
        </section>

        <section id="balance">
            <h2>رصيد الإجازات</h2>
            <table>
                <thead>
                    <tr>
                        <th>م</th>
                        <th>اسم الموظف</th>
                        <th>الرقم الوظيفي</th>
                        <th>الرصيد السنوي</th>
                        <th>الإجازات المستخدمة</th>
                        <th>الرصيد المتبقي</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>أحمد محمد</td>
                        <td>1001</td>
                        <td>30</td>
                        <td>15</td>
                        <td>15</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>سارة أحمد</td>
                        <td>1002</td>
                        <td>30</td>
                        <td>3</td>
                        <td>27</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>محمد علي</td>
                        <td>1003</td>
                        <td>30</td>
                        <td>3</td>
                        <td>27</td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        </section>

        <section id="alerts">
            <h2>التنبيهات</h2>
            <div class="alert alert-warning">
                <strong>تنبيه:</strong> أحمد محمد لديه إجازة مقبلة تبدأ في 2023/12/01.
            </div>
            <div class="alert alert-danger">
                <strong>تنبيه:</strong> سارة أحمد متبقي لها 2 أيام فقط من رصيد الإجازات السنوية.
            </div>
            <div class="alert alert-info">
                <strong>تنبيه:</strong> محمد علي لديه تداخل محتمل في الإجازات مع زملاء في نفس القسم.
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>م</th>
                        <th>اسم الموظف</th>
                        <th>نوع التنبيه</th>
                        <th>التاريخ</th>
                        <th>الحالة</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>أحمد محمد</td>
                        <td>إجازة مقبلة</td>
                        <td>2023/12/01</td>
                        <td>نشط</td>
                        <td>إجازة سنوية</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>سارة أحمد</td>
                        <td>رصيد منخفض</td>
                        <td>2023/11/15</td>
                        <td>نشط</td>
                        <td>متبقي 2 أيام فقط</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>محمد علي</td>
                        <td>تداخل إجازات</td>
                        <td>2023/12/10</td>
                        <td>نشط</td>
                        <td>تداخل مع إجازات في نفس القسم</td>
                    </tr>
                </tbody>
            </table>
        </section>
    </div>

    <footer>
        <div class="container">
            <p>© 2023 نظام الإجازات المتكامل - تم التطوير لتحسين إدارة الإجازات وزيادة الكفاءة في العمل</p>
        </div>
    </footer>

    <script>
        // حساب عدد أيام الإجازة تلقائياً
        document.addEventListener('DOMContentLoaded', function() {
            const startDateInput = document.getElementById('start-date');
            const endDateInput = document.getElementById('end-date');
            const daysCountInput = document.getElementById('days-count');
            
            function calculateDays() {
                if (startDateInput.value && endDateInput.value) {
                    const startDate = new Date(startDateInput.value);
                    const endDate = new Date(endDateInput.value);
                    const diffTime = Math.abs(endDate - startDate);
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end days
                    
                    if (diffDays >= 0) {
                        daysCountInput.value = diffDays;
                    } else {
                        daysCountInput.value = 0;
                    }
                }
            }
            
            startDateInput.addEventListener('change', calculateDays);
            endDateInput.addEventListener('change', calculateDays);
        });
    </script>
</body>
</html>