/*
 * نظام إدارة إجازات الموظفين المتطور
 * ملف التنسيقات الرئيسي
 * يحتوي على جميع أكواد CSS للنظام
 */

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', 'Helvetica', sans-serif;
    background: #f5f7fa;
    min-height: 100vh;
    direction: rtl;
    color: #333;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* تنسيقات الشريط الجانبي */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    z-index: 1000;
    transition: transform 0.3s ease;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
}

.sidebar.collapsed {
    transform: translateX(220px);
}

.sidebar-header {
    padding: 20px;
    background: rgba(0,0,0,0.1);
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.sidebar-subtitle {
    font-size: 12px;
    opacity: 0.7;
}

.sidebar-toggle {
    position: absolute;
    top: 20px;
    left: 15px;
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: background 0.3s;
}

.sidebar-toggle:hover {
    background: rgba(255,255,255,0.1);
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    cursor: pointer;
    transition: all 0.3s;
    border-right: 3px solid transparent;
}

.nav-item:hover {
    background: rgba(255,255,255,0.1);
    border-right-color: #3498db;
}

.nav-item.active {
    background: rgba(52, 152, 219, 0.2);
    border-right-color: #3498db;
}

.nav-icon {
    font-size: 20px;
    margin-left: 15px;
    width: 25px;
    text-align: center;
}

.nav-text {
    font-size: 14px;
    font-weight: 500;
}

/* تنسيقات المحتوى الرئيسي */
.main-container {
    margin-right: 280px;
    min-height: 100vh;
    transition: margin-right 0.3s ease;
}

.main-container.expanded {
    margin-right: 60px;
}

.main-header {
    background: white;
    padding: 20px 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-bottom: 1px solid #e9ecef;
    position: relative;
}

.main-title {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.main-subtitle {
    font-size: 14px;
    color: #7f8c8d;
}

.save-indicator {
    position: absolute;
    top: 20px;
    left: 30px;
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 12px;
    border: 1px solid rgba(39, 174, 96, 0.2);
}

.save-indicator.saving {
    background: rgba(255,193,7,0.1);
    color: #f39c12;
    border-color: rgba(255,193,7,0.2);
}

.main-content {
    padding: 30px;
}

/* تنسيقات الأقسام */
.section {
    display: none;
    animation: fadeIn 0.3s ease;
}

.section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تنسيقات البطاقات */
.card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    margin-bottom: 30px;
    overflow: hidden;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.card-body {
    padding: 30px;
}

/* تنسيقات النماذج */
.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    align-items: end;
}

.form-group {
    flex: 1;
    min-width: 200px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

.form-group input, .form-group select, .form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-group input:focus, .form-group select:focus, .form-group textarea:focus {
    outline: none;
    border-color: #3498db;
}

/* تنسيقات الأزرار */
.btn {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s;
    margin: 5px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
}

.btn-small {
    padding: 8px 15px;
    font-size: 12px;
}

.btn-large {
    padding: 15px 30px;
    font-size: 16px;
}

/* شبكة الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    text-align: center;
    border-top: 4px solid #3498db;
}

.stat-card h3 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 14px;
}

.stat-card .number {
    font-size: 2.5em;
    font-weight: bold;
    color: #3498db;
    margin-bottom: 5px;
}

.stat-card .change {
    font-size: 12px;
    color: #7f8c8d;
}

/* تنسيقات التنبيهات */
.alert {
    padding: 15px 20px;
    margin: 20px 0;
    border-radius: 8px;
    font-weight: 500;
    border-left: 4px solid;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border-left-color: #28a745;
}

.alert-warning {
    background: #fff3cd;
    color: #856404;
    border-left-color: #ffc107;
}

.alert-danger {
    background: #f8d7da;
    color: #721c24;
    border-left-color: #dc3545;
}

.alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border-left-color: #17a2b8;
}

/* حقل البحث مع أيقونة */
.search-input {
    background-image: url('data:image/svg+xml;charset=utf-8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'16\' height=\'16\' fill=\'%23999\' viewBox=\'0 0 16 16\'%3E%3Cpath d=\'M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z\'/%3E%3C/svg%3E');
    background-repeat: no-repeat;
    background-position: 12px center;
    padding-left: 40px !important;
}

/* تحسين استجابة الجداول */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table-responsive table {
    min-width: 1200px;
    white-space: nowrap;
}

/* تنسيقات مجموعة الأزرار */
.btn-group {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

/* تحسين عرض النصوص العربية */
* {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

input, select, textarea, button {
    font-family: inherit;
    text-rendering: optimizeLegibility;
}

/* إخفاء المحتوى المشوه */
.hidden-content {
    display: none !important;
}

/* تحسين المسافات */
.form-row {
    margin-bottom: 25px;
}

.card + .card {
    margin-top: 30px;
}

/* تنسيقات التصفح */
.pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
    gap: 10px;
}

.pagination-btn {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    color: #333;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s;
}

.pagination-btn:hover {
    background: #f8f9fa;
}

.pagination-btn.active {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    font-size: 14px;
    color: #666;
    margin: 0 15px;
}

/* تنسيقات الوضع الليلي */
body.dark-mode {
    background: #1a1a1a;
    color: #e0e0e0;
}

.dark-mode .sidebar {
    background: linear-gradient(180deg, #1e1e1e 0%, #2d2d2d 100%);
}

.dark-mode .main-header {
    background: #2d2d2d;
    border-bottom-color: #404040;
}

.dark-mode .card {
    background: #2d2d2d;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.dark-mode .form-group input,
.dark-mode .form-group select,
.dark-mode .form-group textarea {
    background: #404040;
    border-color: #555;
    color: #e0e0e0;
}

.dark-mode .stat-card {
    background: #2d2d2d;
    border-top-color: #3498db;
}

.dark-mode .pagination-btn {
    background: #404040;
    border-color: #555;
    color: #e0e0e0;
}

.dark-mode .pagination-btn:hover {
    background: #555;
}

/* زر تبديل الوضع الليلي */
.theme-toggle {
    position: fixed;
    top: 20px;
    left: 100px;
    background: #3498db;
    color: white;
    border: none;
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    z-index: 1001;
    transition: all 0.3s;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    background: #2980b9;
    transform: scale(1.1);
}

/* تنسيقات الإشعارات */
.notification-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1002;
    max-width: 300px;
}

.notification {
    background: white;
    border-left: 4px solid #3498db;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    animation: slideIn 0.3s ease;
}

.notification.warning {
    border-left-color: #f39c12;
    background: #fff3cd;
}

.notification.danger {
    border-left-color: #e74c3c;
    background: #f8d7da;
}

@keyframes slideIn {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* حاوية الرسوم البيانية */
.chart-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.dark-mode .chart-container {
    background: #2d2d2d;
}

/* البحث المتقدم */
.advanced-search {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #e9ecef;
}

.dark-mode .advanced-search {
    background: #404040;
    border-color: #555;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-container {
        margin-right: 0;
    }

    .form-row {
        flex-direction: column;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .theme-toggle {
        top: 10px;
        left: 10px;
    }

    .notification-container {
        left: 10px;
        right: 10px;
        max-width: none;
    }
}

/* ===== تنسيقات التقارير المتقدمة ===== */

.advanced-report-modal {
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.report-options {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.option-group {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.option-group h4 {
    margin: 0 0 10px 0;
    color: #3498db;
    font-size: 14px;
    font-weight: 600;
}

.radio-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.radio-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.radio-group label:hover {
    background: rgba(52, 152, 219, 0.1);
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.checkbox-group label:hover {
    background: rgba(52, 152, 219, 0.1);
}

.date-range {
    display: grid;
    grid-template-columns: auto 1fr auto 1fr;
    gap: 10px;
    align-items: center;
}

.date-range label {
    font-weight: 500;
    color: #333;
}

.date-range input {
    padding: 8px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-size: 14px;
}

/* تنسيقات معاينة التقارير */
.report-preview-modal {
    max-width: 900px;
    width: 95%;
    max-height: 90vh;
}

.preview-content {
    max-height: 60vh;
    overflow-y: auto;
    padding: 10px;
}

.preview-section {
    margin-bottom: 25px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.preview-section h4 {
    margin: 0 0 15px 0;
    color: #3498db;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid #3498db;
    padding-bottom: 5px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.stat-label {
    font-weight: 500;
    color: #333;
}

.stat-value {
    font-weight: 600;
    color: #3498db;
    font-size: 16px;
}

.departments-preview,
.employees-preview {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.dept-item,
.emp-item {
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    font-size: 14px;
}

.employee-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background: white;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.info-label {
    font-weight: 500;
    color: #333;
}

.info-value {
    font-weight: 600;
    color: #3498db;
}

.leave-balance {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.balance-item {
    padding: 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    font-size: 14px;
    line-height: 1.5;
}

.leave-history {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.history-item {
    padding: 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    font-size: 14px;
    line-height: 1.5;
}

.more-items {
    padding: 8px;
    text-align: center;
    color: #7f8c8d;
    font-style: italic;
    background: rgba(52, 152, 219, 0.1);
    border-radius: 4px;
}

/* تنسيقات الوضع الليلي للتقارير */
.dark-mode .option-group {
    background: #2d2d2d;
    border-color: #404040;
}

.dark-mode .preview-section {
    background: #2d2d2d;
    border-color: #404040;
}

.dark-mode .stat-item,
.dark-mode .dept-item,
.dark-mode .emp-item,
.dark-mode .info-row,
.dark-mode .balance-item,
.dark-mode .history-item {
    background: #1a1a1a;
    border-color: #404040;
    color: #e0e0e0;
}

.dark-mode .date-range input {
    background: #1a1a1a;
    border-color: #404040;
    color: #e0e0e0;
}

.dark-mode .more-items {
    background: rgba(52, 152, 219, 0.2);
    color: #e0e0e0;
}

/* تنسيقات متجاوبة للتقارير */
@media (max-width: 768px) {
    .advanced-report-modal,
    .report-preview-modal {
        width: 95%;
        margin: 10px;
    }

    .date-range {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .info-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

@media (max-width: 480px) {
    .radio-group,
    .checkbox-group {
        gap: 12px;
    }

    .radio-group label,
    .checkbox-group label {
        padding: 8px;
        font-size: 14px;
    }

    .preview-content {
        padding: 5px;
    }

    .preview-section {
        padding: 10px;
        margin-bottom: 15px;
    }
}

/* تأثيرات التحميل للتقارير */
.report-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    gap: 15px;
}

.report-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e9ecef;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.report-loading-text {
    color: #333;
    font-size: 16px;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تنسيقات أزرار التقارير المتقدمة */
.advanced-reports-section {
    background: linear-gradient(135deg, #3498db, #2c3e50);
    color: white;
    padding: 20px;
    border-radius: 12px;
    margin: 20px 0;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.advanced-reports-section h3 {
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
}

.advanced-reports-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.advanced-report-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.advanced-report-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.advanced-report-btn:active {
    transform: translateY(0);
}

/* تنسيقات النوافذ المنبثقة للتقارير */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 90%;
    max-height: 90%;
    overflow: hidden;
    animation: slideUp 0.3s ease;
}

.modal-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

@keyframes slideUp {
    from { transform: translateY(50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* تنسيقات الوضع الليلي للنوافذ المنبثقة */
.dark-mode .modal-content {
    background: #2d2d2d;
    color: #e0e0e0;
}

.dark-mode .modal-footer {
    border-top-color: #404040;
}

/* ===== نهاية تنسيقات التقارير المتقدمة ===== */

/* ===== تنسيقات التقارير والإعدادات المحسنة ===== */

/* تنسيقات شبكة التقارير */
.reports-grid,
.export-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
}

.reports-grid .btn,
.export-grid .btn {
    height: auto;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    text-align: right;
    padding: 15px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.reports-grid .btn:hover,
.export-grid .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* تنسيقات شبكة الإعدادات */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.setting-group {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.setting-item:hover {
    background: #f8f9fa;
    border-color: #3498db;
}

.setting-item:last-child {
    margin-bottom: 0;
}

/* تنسيقات شبكة المساعدة */
.help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.help-grid .btn {
    height: auto;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    text-align: right;
    padding: 15px;
}

/* تنسيقات إدارة البيانات */
.data-management-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.data-group {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.data-group h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 600;
}

/* تنسيقات شبكة الاختبار */
.test-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.test-grid .btn {
    height: auto;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    text-align: right;
    padding: 15px;
}

/* تنسيقات منطقة عرض التقارير */
#reportDisplayCard {
    margin-top: 20px;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تنسيقات التقارير المختلفة */
.monthly-report,
.balance-report,
.low-balance-report,
.detailed-report,
.department-report,
.employee-report {
    padding: 20px;
}

.employee-info-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.leave-balances {
    margin-bottom: 20px;
}

.balance-card {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.balance-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* تنسيقات أحجام الخطوط */
.font-small {
    font-size: 14px;
}

.font-small .btn {
    font-size: 12px;
    padding: 8px 15px;
}

.font-small .card-title {
    font-size: 16px;
}

.font-medium {
    font-size: 16px;
}

.font-large {
    font-size: 18px;
}

.font-large .btn {
    font-size: 18px;
    padding: 15px 25px;
}

.font-large .card-title {
    font-size: 22px;
}

/* تنسيقات الوضع الليلي للتحسينات الجديدة */
.dark-mode .setting-group,
.dark-mode .data-group {
    background: #2d2d2d;
    border-color: #404040;
}

.dark-mode .setting-item {
    background: #1a1a1a;
    border-color: #404040;
    color: #e0e0e0;
}

.dark-mode .setting-item:hover {
    background: #2d2d2d;
    border-color: #3498db;
}

.dark-mode .employee-info-section,
.dark-mode .balance-card {
    background: #2d2d2d;
    border-color: #404040;
    color: #e0e0e0;
}

/* تنسيقات متجاوبة للتحسينات */
@media (max-width: 768px) {
    .reports-grid,
    .export-grid,
    .help-grid,
    .test-grid {
        grid-template-columns: 1fr;
    }

    .settings-grid,
    .data-management-grid {
        grid-template-columns: 1fr;
    }

    .reports-grid .btn,
    .export-grid .btn,
    .help-grid .btn,
    .test-grid .btn {
        min-height: 60px;
        padding: 12px;
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .monthly-report,
    .balance-report,
    .low-balance-report,
    .detailed-report,
    .department-report,
    .employee-report {
        padding: 15px;
    }

    .employee-info-section {
        padding: 15px;
    }

    .balance-card {
        padding: 12px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* ===== نهاية تنسيقات التقارير والإعدادات المحسنة ===== */
