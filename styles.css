/*
 * نظام إدارة إجازات الموظفين المتطور
 * ملف التنسيقات الرئيسي
 * يحتوي على جميع أكواد CSS للنظام
 */

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', 'Helvetica', sans-serif;
    background: #f5f7fa;
    min-height: 100vh;
    direction: rtl;
    color: #333;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* تنسيقات الشريط الجانبي */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    z-index: 1000;
    transition: transform 0.3s ease;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
}

.sidebar.collapsed {
    transform: translateX(220px);
}

.sidebar-header {
    padding: 20px;
    background: rgba(0,0,0,0.1);
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.sidebar-subtitle {
    font-size: 12px;
    opacity: 0.7;
}

.sidebar-toggle {
    position: absolute;
    top: 20px;
    left: 15px;
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: background 0.3s;
}

.sidebar-toggle:hover {
    background: rgba(255,255,255,0.1);
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    cursor: pointer;
    transition: all 0.3s;
    border-right: 3px solid transparent;
}

.nav-item:hover {
    background: rgba(255,255,255,0.1);
    border-right-color: #3498db;
}

.nav-item.active {
    background: rgba(52, 152, 219, 0.2);
    border-right-color: #3498db;
}

.nav-icon {
    font-size: 20px;
    margin-left: 15px;
    width: 25px;
    text-align: center;
}

.nav-text {
    font-size: 14px;
    font-weight: 500;
}

/* تنسيقات المحتوى الرئيسي */
.main-container {
    margin-right: 280px;
    min-height: 100vh;
    transition: margin-right 0.3s ease;
}

.main-container.expanded {
    margin-right: 60px;
}

.main-header {
    background: white;
    padding: 20px 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-bottom: 1px solid #e9ecef;
    position: relative;
}

.main-title {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.main-subtitle {
    font-size: 14px;
    color: #7f8c8d;
}

.save-indicator {
    position: absolute;
    top: 20px;
    left: 30px;
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 12px;
    border: 1px solid rgba(39, 174, 96, 0.2);
}

.save-indicator.saving {
    background: rgba(255,193,7,0.1);
    color: #f39c12;
    border-color: rgba(255,193,7,0.2);
}

.main-content {
    padding: 30px;
}

/* تنسيقات الأقسام */
.section {
    display: none;
    animation: fadeIn 0.3s ease;
}

.section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تنسيقات البطاقات */
.card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    margin-bottom: 30px;
    overflow: hidden;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.card-body {
    padding: 30px;
}

/* تنسيقات النماذج */
.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    align-items: end;
}

.form-group {
    flex: 1;
    min-width: 200px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

.form-group input, .form-group select, .form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-group input:focus, .form-group select:focus, .form-group textarea:focus {
    outline: none;
    border-color: #3498db;
}

/* تنسيقات الأزرار */
.btn {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s;
    margin: 5px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
}

.btn-small {
    padding: 8px 15px;
    font-size: 12px;
}

.btn-large {
    padding: 15px 30px;
    font-size: 16px;
}

/* شبكة الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    text-align: center;
    border-top: 4px solid #3498db;
}

.stat-card h3 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 14px;
}

.stat-card .number {
    font-size: 2.5em;
    font-weight: bold;
    color: #3498db;
    margin-bottom: 5px;
}

.stat-card .change {
    font-size: 12px;
    color: #7f8c8d;
}

/* تنسيقات التنبيهات */
.alert {
    padding: 15px 20px;
    margin: 20px 0;
    border-radius: 8px;
    font-weight: 500;
    border-left: 4px solid;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border-left-color: #28a745;
}

.alert-warning {
    background: #fff3cd;
    color: #856404;
    border-left-color: #ffc107;
}

.alert-danger {
    background: #f8d7da;
    color: #721c24;
    border-left-color: #dc3545;
}

.alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border-left-color: #17a2b8;
}

/* حقل البحث مع أيقونة */
.search-input {
    background-image: url('data:image/svg+xml;charset=utf-8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'16\' height=\'16\' fill=\'%23999\' viewBox=\'0 0 16 16\'%3E%3Cpath d=\'M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z\'/%3E%3C/svg%3E');
    background-repeat: no-repeat;
    background-position: 12px center;
    padding-left: 40px !important;
}

/* تحسين استجابة الجداول */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table-responsive table {
    min-width: 1200px;
    white-space: nowrap;
}

/* تنسيقات مجموعة الأزرار */
.btn-group {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

/* تحسين عرض النصوص العربية */
* {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

input, select, textarea, button {
    font-family: inherit;
    text-rendering: optimizeLegibility;
}

/* إخفاء المحتوى المشوه */
.hidden-content {
    display: none !important;
}

/* تحسين المسافات */
.form-row {
    margin-bottom: 25px;
}

.card + .card {
    margin-top: 30px;
}

/* تنسيقات التصفح */
.pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
    gap: 10px;
}

.pagination-btn {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    color: #333;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s;
}

.pagination-btn:hover {
    background: #f8f9fa;
}

.pagination-btn.active {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    font-size: 14px;
    color: #666;
    margin: 0 15px;
}

/* تنسيقات الوضع الليلي */
body.dark-mode {
    background: #1a1a1a;
    color: #e0e0e0;
}

.dark-mode .sidebar {
    background: linear-gradient(180deg, #1e1e1e 0%, #2d2d2d 100%);
}

.dark-mode .main-header {
    background: #2d2d2d;
    border-bottom-color: #404040;
}

.dark-mode .card {
    background: #2d2d2d;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.dark-mode .form-group input,
.dark-mode .form-group select,
.dark-mode .form-group textarea {
    background: #404040;
    border-color: #555;
    color: #e0e0e0;
}

.dark-mode .stat-card {
    background: #2d2d2d;
    border-top-color: #3498db;
}

.dark-mode .pagination-btn {
    background: #404040;
    border-color: #555;
    color: #e0e0e0;
}

.dark-mode .pagination-btn:hover {
    background: #555;
}

/* زر تبديل الوضع الليلي */
.theme-toggle {
    position: fixed;
    top: 20px;
    left: 100px;
    background: #3498db;
    color: white;
    border: none;
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    z-index: 1001;
    transition: all 0.3s;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    background: #2980b9;
    transform: scale(1.1);
}

/* تنسيقات الإشعارات */
.notification-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1002;
    max-width: 300px;
}

.notification {
    background: white;
    border-left: 4px solid #3498db;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    animation: slideIn 0.3s ease;
}

.notification.warning {
    border-left-color: #f39c12;
    background: #fff3cd;
}

.notification.danger {
    border-left-color: #e74c3c;
    background: #f8d7da;
}

@keyframes slideIn {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* حاوية الرسوم البيانية */
.chart-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.dark-mode .chart-container {
    background: #2d2d2d;
}

/* البحث المتقدم */
.advanced-search {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #e9ecef;
}

.dark-mode .advanced-search {
    background: #404040;
    border-color: #555;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-container {
        margin-right: 0;
    }

    .form-row {
        flex-direction: column;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .theme-toggle {
        top: 10px;
        left: 10px;
    }

    .notification-container {
        left: 10px;
        right: 10px;
        max-width: none;
    }
}
