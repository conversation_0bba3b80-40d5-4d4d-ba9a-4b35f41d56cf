<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام إدارة الإجازات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f7fa;
            direction: rtl;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار نظام إدارة الإجازات</h1>
        <p>هذا الملف لاختبار تحميل وعمل جميع ملفات النظام</p>
        
        <button onclick="runTests()">🚀 تشغيل الاختبارات</button>
        <button onclick="openMainSystem()">📋 فتح النظام الرئيسي</button>
        
        <div id="testResults"></div>
    </div>

    <!-- تحميل جميع ملفات النظام للاختبار -->
    <script src="utils.js"></script>
    <script src="core.js"></script>
    <script src="sections.js"></script>
    <script src="functions.js"></script>
    <script src="features.js"></script>
    <script src="extras.js"></script>
    <script src="app.js"></script>

    <script>
        function addTestResult(message, type = 'success') {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function runTests() {
            document.getElementById('testResults').innerHTML = '';
            addTestResult('🔄 بدء الاختبارات...', 'warning');

            // اختبار تحميل الملفات
            setTimeout(() => {
                try {
                    let passedTests = 0;
                    let totalTests = 0;

                    // اختبار الوظائف الأساسية
                    totalTests++;
                    if (typeof showSection === 'function') {
                        addTestResult('✅ تم تحميل app.js بنجاح');
                        passedTests++;
                    } else {
                        addTestResult('❌ فشل في تحميل app.js', 'error');
                    }

                    totalTests++;
                    if (typeof sanitizeInput === 'function') {
                        addTestResult('✅ تم تحميل utils.js بنجاح');
                        passedTests++;
                    } else {
                        addTestResult('❌ فشل في تحميل utils.js', 'error');
                    }

                    totalTests++;
                    if (typeof updateStats === 'function') {
                        addTestResult('✅ تم تحميل core.js بنجاح');
                        passedTests++;
                    } else {
                        addTestResult('❌ فشل في تحميل core.js', 'error');
                    }

                    totalTests++;
                    if (typeof loadEmployeesSection === 'function') {
                        addTestResult('✅ تم تحميل sections.js بنجاح');
                        passedTests++;
                    } else {
                        addTestResult('❌ فشل في تحميل sections.js', 'error');
                    }

                    totalTests++;
                    if (typeof addEmployee === 'function') {
                        addTestResult('✅ تم تحميل functions.js بنجاح');
                        passedTests++;
                    } else {
                        addTestResult('❌ فشل في تحميل functions.js', 'error');
                    }

                    totalTests++;
                    if (typeof exportToCSV === 'function') {
                        addTestResult('✅ تم تحميل features.js بنجاح');
                        passedTests++;
                    } else {
                        addTestResult('❌ فشل في تحميل features.js', 'error');
                    }

                    totalTests++;
                    if (typeof loadTestData === 'function') {
                        addTestResult('✅ تم تحميل extras.js بنجاح');
                        passedTests++;
                    } else {
                        addTestResult('❌ فشل في تحميل extras.js', 'error');
                    }

                    // اختبار المتغيرات العامة
                    totalTests++;
                    if (typeof window.employees !== 'undefined') {
                        addTestResult('✅ متغير employees متاح');
                        passedTests++;
                    } else {
                        addTestResult('❌ متغير employees غير متاح', 'error');
                    }

                    // اختبار التخزين المحلي
                    totalTests++;
                    if (typeof isLocalStorageAvailable === 'function' && isLocalStorageAvailable()) {
                        addTestResult('✅ التخزين المحلي متاح');
                        passedTests++;
                    } else {
                        addTestResult('⚠️ التخزين المحلي غير متاح', 'warning');
                        passedTests++; // لا نعتبرها خطأ
                    }

                    // اختبار البيانات التجريبية
                    totalTests++;
                    if (typeof loadTestData === 'function') {
                        try {
                            window.employees = [];
                            loadTestData();
                            if (window.employees.length > 0) {
                                addTestResult(`✅ تم تحميل ${window.employees.length} موظف تجريبي`);
                                passedTests++;
                            } else {
                                addTestResult('❌ فشل في تحميل البيانات التجريبية', 'error');
                            }
                        } catch (error) {
                            addTestResult('❌ خطأ في تحميل البيانات التجريبية: ' + error.message, 'error');
                        }
                    }

                    // اختبار إضافي للمتغيرات العامة
                    totalTests++;
                    if (typeof syncGlobalVariables === 'function') {
                        addTestResult('✅ وظائف مزامنة المتغيرات متاحة');
                        passedTests++;
                    } else {
                        addTestResult('⚠️ وظائف مزامنة المتغيرات غير متاحة', 'warning');
                    }

                    // النتيجة النهائية
                    const successRate = Math.round((passedTests / totalTests) * 100);
                    if (successRate >= 90) {
                        addTestResult(`🎉 تم إكمال جميع الاختبارات! النجاح: ${passedTests}/${totalTests} (${successRate}%)`, 'success');
                    } else if (successRate >= 70) {
                        addTestResult(`⚠️ تم إكمال الاختبارات مع تحذيرات. النجاح: ${passedTests}/${totalTests} (${successRate}%)`, 'warning');
                    } else {
                        addTestResult(`❌ فشل في عدة اختبارات. النجاح: ${passedTests}/${totalTests} (${successRate}%)`, 'error');
                    }

                } catch (error) {
                    addTestResult('❌ خطأ عام في الاختبارات: ' + error.message, 'error');
                }
            }, 1000);
        }

        function openMainSystem() {
            window.open('index.html', '_blank');
        }

        // تشغيل اختبار أساسي عند التحميل
        window.addEventListener('load', function() {
            addTestResult('📋 تم تحميل صفحة الاختبار بنجاح', 'success');
        });
    </script>
</body>
</html>
