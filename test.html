<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام إدارة الإجازات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f7fa;
            direction: rtl;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار نظام إدارة الإجازات</h1>
        <p>هذا الملف لاختبار تحميل وعمل جميع ملفات النظام</p>
        
        <button onclick="runTests()">🚀 تشغيل الاختبارات</button>
        <button onclick="openMainSystem()">📋 فتح النظام الرئيسي</button>
        
        <div id="testResults"></div>
    </div>

    <!-- تحميل جميع ملفات النظام للاختبار -->
    <script src="utils.js"></script>
    <script src="core.js"></script>
    <script src="sections.js"></script>
    <script src="functions.js"></script>
    <script src="features.js"></script>
    <script src="extras.js"></script>
    <script src="app.js"></script>

    <script>
        function addTestResult(message, type = 'success') {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function runTests() {
            document.getElementById('testResults').innerHTML = '';
            addTestResult('🔄 بدء الاختبارات...', 'warning');

            // اختبار تحميل الملفات
            setTimeout(() => {
                try {
                    // اختبار الوظائف الأساسية
                    if (typeof showSection === 'function') {
                        addTestResult('✅ تم تحميل app.js بنجاح');
                    } else {
                        addTestResult('❌ فشل في تحميل app.js', 'error');
                    }

                    if (typeof sanitizeInput === 'function') {
                        addTestResult('✅ تم تحميل utils.js بنجاح');
                    } else {
                        addTestResult('❌ فشل في تحميل utils.js', 'error');
                    }

                    if (typeof updateStats === 'function') {
                        addTestResult('✅ تم تحميل core.js بنجاح');
                    } else {
                        addTestResult('❌ فشل في تحميل core.js', 'error');
                    }

                    if (typeof loadEmployeesSection === 'function') {
                        addTestResult('✅ تم تحميل sections.js بنجاح');
                    } else {
                        addTestResult('❌ فشل في تحميل sections.js', 'error');
                    }

                    if (typeof addEmployee === 'function') {
                        addTestResult('✅ تم تحميل functions.js بنجاح');
                    } else {
                        addTestResult('❌ فشل في تحميل functions.js', 'error');
                    }

                    if (typeof exportToCSV === 'function') {
                        addTestResult('✅ تم تحميل features.js بنجاح');
                    } else {
                        addTestResult('❌ فشل في تحميل features.js', 'error');
                    }

                    if (typeof loadTestData === 'function') {
                        addTestResult('✅ تم تحميل extras.js بنجاح');
                    } else {
                        addTestResult('❌ فشل في تحميل extras.js', 'error');
                    }

                    // اختبار المتغيرات العامة
                    if (typeof window.employees !== 'undefined') {
                        addTestResult('✅ متغير employees متاح');
                    } else {
                        addTestResult('❌ متغير employees غير متاح', 'error');
                    }

                    // اختبار التخزين المحلي
                    if (typeof isLocalStorageAvailable === 'function' && isLocalStorageAvailable()) {
                        addTestResult('✅ التخزين المحلي متاح');
                    } else {
                        addTestResult('⚠️ التخزين المحلي غير متاح', 'warning');
                    }

                    // اختبار البيانات التجريبية
                    if (typeof loadTestData === 'function') {
                        try {
                            window.employees = [];
                            loadTestData();
                            if (window.employees.length > 0) {
                                addTestResult(`✅ تم تحميل ${window.employees.length} موظف تجريبي`);
                            } else {
                                addTestResult('❌ فشل في تحميل البيانات التجريبية', 'error');
                            }
                        } catch (error) {
                            addTestResult('❌ خطأ في تحميل البيانات التجريبية: ' + error.message, 'error');
                        }
                    }

                    addTestResult('🎉 تم إكمال جميع الاختبارات!', 'success');

                } catch (error) {
                    addTestResult('❌ خطأ عام في الاختبارات: ' + error.message, 'error');
                }
            }, 1000);
        }

        function openMainSystem() {
            window.open('index.html', '_blank');
        }

        // تشغيل اختبار أساسي عند التحميل
        window.addEventListener('load', function() {
            addTestResult('📋 تم تحميل صفحة الاختبار بنجاح', 'success');
        });
    </script>
</body>
</html>
