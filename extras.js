/**
 * نظام إدارة إجازات الموظفين المتطور
 * ملف الميزات الإضافية
 * يحتوي على الرسوم البيانية والبيانات التجريبية والإعدادات والتهيئة
 */

// ===== وظائف الرسوم البيانية =====

/**
 * تحديث الرسوم البيانية
 */
function updateCharts() {
    try {
        console.log('📊 تحديث الرسوم البيانية...');
        
        // رسم بياني للأقسام
        const departmentCanvas = document.getElementById('departmentChart');
        if (departmentCanvas) {
            drawDepartmentChart(departmentCanvas);
        }
        
        // رسم بياني لحالات الإجازات
        const statusCanvas = document.getElementById('leaveStatusChart');
        if (statusCanvas) {
            drawLeaveStatusChart(statusCanvas);
        }
        
        console.log('✅ تم تحديث الرسوم البيانية');
    } catch (error) {
        console.error('❌ خطأ في تحديث الرسوم البيانية:', error);
    }
}

/**
 * رسم الرسم البياني للأقسام
 */
function drawDepartmentChart(canvas) {
    if (!canvas || window.employees.length === 0) return;
    
    const ctx = canvas.getContext('2d');
    const departments = {};
    
    // حساب عدد الموظفين في كل قسم
    window.employees.forEach(emp => {
        departments[emp.department] = (departments[emp.department] || 0) + 1;
    });
    
    const colors = ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c', '#34495e'];
    const departmentNames = Object.keys(departments);
    const values = Object.values(departments);
    
    drawPieChart(ctx, canvas, departmentNames, values, colors);
}

/**
 * رسم الرسم البياني لحالات الإجازات
 */
function drawLeaveStatusChart(canvas) {
    if (!canvas || window.employees.length === 0) return;
    
    const ctx = canvas.getContext('2d');
    const statusCounts = {
        'طبيعي': 0,
        'رصيد منخفض': 0,
        'نفد الرصيد': 0
    };
    
    window.employees.forEach(emp => {
        const status = getEmployeeStatus(emp);
        statusCounts[status]++;
    });
    
    const colors = ['#2ecc71', '#f39c12', '#e74c3c'];
    const labels = Object.keys(statusCounts);
    const values = Object.values(statusCounts);
    
    drawPieChart(ctx, canvas, labels, values, colors);
}

/**
 * رسم رسم بياني دائري
 */
function drawPieChart(ctx, canvas, labels, values, colors) {
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = Math.min(centerX, centerY) - 20;
    
    // مسح الرسم السابق
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    if (values.every(v => v === 0)) {
        // رسم دائرة فارغة مع نص
        ctx.fillStyle = '#ecf0f1';
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        ctx.fill();
        
        ctx.fillStyle = '#7f8c8d';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('لا توجد بيانات', centerX, centerY);
        return;
    }
    
    const total = values.reduce((sum, value) => sum + value, 0);
    let currentAngle = -Math.PI / 2; // البدء من الأعلى
    
    // رسم القطاعات
    values.forEach((value, index) => {
        const sliceAngle = (value / total) * 2 * Math.PI;
        
        ctx.fillStyle = colors[index % colors.length];
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
        ctx.closePath();
        ctx.fill();
        
        // رسم النص
        if (value > 0) {
            const textAngle = currentAngle + sliceAngle / 2;
            const textX = centerX + Math.cos(textAngle) * (radius * 0.7);
            const textY = centerY + Math.sin(textAngle) * (radius * 0.7);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(value.toString(), textX, textY);
        }
        
        currentAngle += sliceAngle;
    });
    
    // رسم الأسطورة
    const legendY = canvas.height - 60;
    labels.forEach((label, index) => {
        if (values[index] > 0) {
            const legendX = 10 + (index * 80);
            
            // مربع اللون
            ctx.fillStyle = colors[index % colors.length];
            ctx.fillRect(legendX, legendY, 12, 12);
            
            // النص
            ctx.fillStyle = '#2c3e50';
            ctx.font = '10px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(label, legendX + 16, legendY + 10);
        }
    });
}

// ===== وظائف البيانات التجريبية =====

/**
 * تحميل بيانات تجريبية
 */
function loadTestData() {
    if (window.employees.length > 0) {
        if (!confirm('سيتم استبدال البيانات الحالية بالبيانات التجريبية. هل تريد المتابعة؟')) {
            return;
        }
    }
    
    const testEmployees = [
        {
            name: 'أحمد محمد علي',
            id: 'EMP001',
            department: 'تقنية المعلومات',
            hireDate: '2020-01-15',
            annualLeave: 30,
            carriedOverLeave: 5,
            sickLeave: 15,
            emergencyLeave: 5,
            usedAnnual: 12,
            usedSick: 3,
            usedEmergency: 1,
            leaveHistory: [
                {
                    id: Date.now() + 1,
                    type: 'annual',
                    typeName: 'إجازة سنوية',
                    days: 7,
                    startDate: '2024-01-15',
                    endDate: '2024-01-21',
                    reason: 'إجازة عائلية',
                    addedDate: new Date().toISOString()
                }
            ]
        },
        {
            name: 'فاطمة عبدالله',
            id: 'EMP002',
            department: 'الموارد البشرية',
            hireDate: '2019-03-10',
            annualLeave: 30,
            carriedOverLeave: 8,
            sickLeave: 15,
            emergencyLeave: 5,
            usedAnnual: 25,
            usedSick: 5,
            usedEmergency: 2,
            leaveHistory: []
        },
        {
            name: 'محمد سعد الدين',
            id: 'EMP003',
            department: 'المالية',
            hireDate: '2021-06-01',
            annualLeave: 30,
            carriedOverLeave: 0,
            sickLeave: 15,
            emergencyLeave: 5,
            usedAnnual: 15,
            usedSick: 2,
            usedEmergency: 0,
            leaveHistory: []
        },
        {
            name: 'Sarah Johnson',
            id: 'EMP004',
            department: 'Marketing',
            hireDate: '2018-09-15',
            annualLeave: 30,
            carriedOverLeave: 12,
            sickLeave: 15,
            emergencyLeave: 5,
            usedAnnual: 35,
            usedSick: 8,
            usedEmergency: 3,
            leaveHistory: []
        },
        {
            name: 'علي حسن محمود',
            id: 'EMP005',
            department: 'العمليات',
            hireDate: '2022-02-20',
            annualLeave: 30,
            carriedOverLeave: 3,
            sickLeave: 15,
            emergencyLeave: 5,
            usedAnnual: 8,
            usedSick: 1,
            usedEmergency: 0,
            leaveHistory: []
        }
    ];
    
    window.employees = testEmployees;
    window.filteredEmployees = [...window.employees];
    
    updateTable();
    updateStats();
    updateQuickEmployeeView();
    updateDepartmentFilter();
    updateCharts();
    saveData();
    
    showAlert(`تم تحميل ${testEmployees.length} موظف تجريبي بنجاح`, 'success');
    showNotification('🧪 تم تحميل البيانات التجريبية بنجاح! تتضمن موظفين من أقسام مختلفة مع رصيد مرحل متنوع.', 'info', 8000);
}

/**
 * تشغيل اختبار شامل
 */
function runComprehensiveTest() {
    showAlert('🧪 بدء الاختبار الشامل للنظام...', 'info');
    
    setTimeout(() => {
        const stats = {
            totalEmployees: window.employees.length,
            totalCarriedOver: window.employees.reduce((sum, emp) => sum + (emp.carriedOverLeave || 0), 0),
            totalUsed: window.employees.reduce((sum, emp) => sum + (emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0), 0)
        };
        
        console.log('📊 إحصائيات النظام:', stats);
        showAlert(`✅ تم إكمال الاختبار! الموظفين: ${stats.totalEmployees}، الرصيد المرحل: ${stats.totalCarriedOver}، المستخدم: ${stats.totalUsed}`, 'success');
    }, 1000);
}

/**
 * اختبار الوظائف الأساسية
 */
function testBasicFunctionality() {
    console.log('🔍 اختبار الوظائف الأساسية:');
    console.log(`- عدد الموظفين: ${window.employees.length}`);
    console.log(`- التخزين المحلي متاح: ${isLocalStorageAvailable()}`);
    console.log(`- البيانات محفوظة: ${localStorage.getItem('employeeLeaveSystem') ? 'نعم' : 'لا'}`);
    
    const stats = {
        totalEmployees: window.employees.length,
        totalCarriedOver: window.employees.reduce((sum, emp) => sum + (emp.carriedOverLeave || 0), 0),
        totalUsed: window.employees.reduce((sum, emp) => sum + (emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0), 0),
        totalRemaining: window.employees.reduce((sum, emp) =>
            sum + ((emp.annualLeave + emp.carriedOverLeave) - emp.usedAnnual) + (emp.sickLeave - emp.usedSick) + (emp.emergencyLeave - emp.usedEmergency), 0)
    };
    
    console.log('📊 الإحصائيات:', stats);
    return stats;
}

// ===== وظائف الإعدادات =====

/**
 * استيراد البيانات
 */
function importData() {
    const fileInput = document.getElementById('importFile');
    const file = fileInput.files[0];
    
    if (!file) {
        showAlert('يرجى اختيار ملف للاستيراد', 'warning');
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const data = JSON.parse(e.target.result);
            
            if (data.employees && Array.isArray(data.employees)) {
                if (confirm('هل أنت متأكد من استيراد البيانات؟ سيتم استبدال البيانات الحالية.')) {
                    window.employees = data.employees;
                    window.filteredEmployees = [...window.employees];
                    updateTable();
                    updateStats();
                    updateQuickEmployeeView();
                    updateDepartmentFilter();
                    updateCharts();
                    saveData();
                    showAlert(`تم استيراد ${window.employees.length} موظف بنجاح`, 'success');
                }
            } else {
                showAlert('ملف غير صالح. يرجى التأكد من صحة الملف', 'danger');
            }
        } catch (error) {
            showAlert('خطأ في قراءة الملف. يرجى التأكد من صحة الملف', 'danger');
        }
    };
    reader.readAsText(file);
    
    fileInput.value = '';
}

/**
 * إعادة تعيين الإجازات
 */
function resetData() {
    if (window.employees.length === 0) {
        showAlert('لا توجد بيانات لإعادة تعيينها', 'warning');
        return;
    }
    
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإجازات؟ سيتم مسح جميع سجلات الإجازات والعودة للقيم الافتراضية.')) {
        window.employees.forEach(emp => {
            emp.usedAnnual = 0;
            emp.usedSick = 0;
            emp.usedEmergency = 0;
            emp.leaveHistory = [];
        });
        
        updateTable();
        updateStats();
        updateQuickEmployeeView();
        updateCharts();
        saveData();
        
        showAlert('تم إعادة تعيين جميع الإجازات بنجاح', 'success');
    }
}

/**
 * مسح جميع البيانات
 */
function clearAllData() {
    if (confirm('⚠️ تحذير: هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        if (confirm('تأكيد أخير: سيتم مسح جميع بيانات الموظفين والإجازات نهائياً. هل تريد المتابعة؟')) {
            window.employees = [];
            window.filteredEmployees = [];
            localStorage.removeItem('employeeLeaveSystem');
            
            // مسح النسخ الاحتياطية
            const keys = Object.keys(localStorage);
            keys.filter(key => key.startsWith('backup_')).forEach(key => {
                localStorage.removeItem(key);
            });
            
            updateTable();
            updateStats();
            updateQuickEmployeeView();
            updateDepartmentFilter();
            updateCharts();
            
            showAlert('تم مسح جميع البيانات بنجاح', 'success');
        }
    }
}

/**
 * التحقق من توفر التخزين المحلي
 */
function isLocalStorageAvailable() {
    try {
        const test = 'test';
        localStorage.setItem(test, test);
        localStorage.removeItem(test);
        return true;
    } catch (e) {
        return false;
    }
}

// ===== وظائف الدليل والمساعدة =====

/**
 * بدء الجولة التعريفية
 */
function startUserGuide() {
    const guideContent = document.getElementById('guideContent');
    guideContent.style.display = 'block';
    guideContent.innerHTML = `
        <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
            <h3 style="margin: 0 0 15px 0;">🎯 مرحباً بك في نظام إدارة الإجازات المتطور!</h3>
            <p style="margin: 0; line-height: 1.6;">هذا الدليل سيساعدك على فهم جميع ميزات النظام واستخدامها بفعالية.</p>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
            <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e9ecef;">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">📊 لوحة التحكم</h4>
                <ul style="line-height: 1.8; color: #666;">
                    <li>عرض إحصائيات شاملة للموظفين والإجازات</li>
                    <li>رسوم بيانية تفاعلية للأقسام وحالات الرصيد</li>
                    <li>نظرة سريعة على آخر الموظفين المضافين</li>
                    <li>إجراءات سريعة للوصول للأقسام المختلفة</li>
                </ul>
            </div>

            <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e9ecef;">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">👥 إدارة الموظفين</h4>
                <ul style="line-height: 1.8; color: #666;">
                    <li>إضافة وتعديل بيانات الموظفين</li>
                    <li>دعم الرصيد المرحل من السنوات السابقة</li>
                    <li>بحث متقدم وفلترة حسب معايير متعددة</li>
                    <li>جدول تفاعلي مع تصفح الصفحات</li>
                </ul>
            </div>

            <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e9ecef;">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">📅 إدارة الإجازات</h4>
                <ul style="line-height: 1.8; color: #666;">
                    <li>إضافة إجازات بأنواعها (سنوية/مرضية/طارئة)</li>
                    <li>حساب تلقائي للأيام والرصيد المتبقي</li>
                    <li>سجل شامل لجميع الإجازات</li>
                    <li>إمكانية حذف وتعديل الإجازات</li>
                </ul>
            </div>

            <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e9ecef;">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">📈 التقارير</h4>
                <ul style="line-height: 1.8; color: #666;">
                    <li>تصدير البيانات بصيغة CSV للتحليل</li>
                    <li>إنشاء نسخ احتياطية بصيغة JSON</li>
                    <li>تقارير مفصلة حسب الأقسام</li>
                    <li>إحصائيات شاملة ومرئية</li>
                </ul>
            </div>
        </div>

        <div style="margin-top: 20px; padding: 15px; background: #e8f5e8; border-radius: 8px; border: 1px solid #c8e6c9;">
            <h4 style="color: #2d5a2d; margin-bottom: 10px;">💡 نصائح مهمة:</h4>
            <ul style="color: #2d5a2d; line-height: 1.6;">
                <li><strong>الرصيد المرحل:</strong> يمثل أيام الإجازة المتبقية من السنوات السابقة</li>
                <li><strong>الحفظ التلقائي:</strong> يتم حفظ البيانات تلقائياً كل 30 ثانية</li>
                <li><strong>النسخ الاحتياطية:</strong> يتم إنشاء نسخة احتياطية يومية تلقائياً</li>
                <li><strong>البحث المتقدم:</strong> استخدم الفلاتر المتعددة للعثور على الموظفين بسرعة</li>
            </ul>
        </div>
    `;
}

/**
 * عرض اختصارات لوحة المفاتيح
 */
function showKeyboardShortcuts() {
    const guideContent = document.getElementById('guideContent');
    guideContent.style.display = 'block';
    guideContent.innerHTML = `
        <div style="background: linear-gradient(135deg, #2c3e50, #34495e); color: white; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
            <h3 style="margin: 0 0 15px 0;">⌨️ اختصارات لوحة المفاتيح</h3>
            <p style="margin: 0; line-height: 1.6;">استخدم هذه الاختصارات لتسريع عملك في النظام.</p>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
            <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef;">
                <h4 style="color: #2c3e50; margin-bottom: 10px;">🧭 التنقل</h4>
                <div style="font-family: monospace; line-height: 1.8;">
                    <div><kbd>Ctrl + 1</kbd> - لوحة التحكم</div>
                    <div><kbd>Ctrl + 2</kbd> - إدارة الموظفين</div>
                    <div><kbd>Ctrl + 3</kbd> - إدارة الإجازات</div>
                    <div><kbd>Ctrl + 4</kbd> - التقارير</div>
                    <div><kbd>Ctrl + 5</kbd> - الإعدادات</div>
                </div>
            </div>

            <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef;">
                <h4 style="color: #2c3e50; margin-bottom: 10px;">💾 الحفظ والتصدير</h4>
                <div style="font-family: monospace; line-height: 1.8;">
                    <div><kbd>Ctrl + S</kbd> - حفظ البيانات</div>
                    <div><kbd>Ctrl + E</kbd> - تصدير CSV</div>
                    <div><kbd>Ctrl + B</kbd> - نسخة احتياطية</div>
                </div>
            </div>

            <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef;">
                <h4 style="color: #2c3e50; margin-bottom: 10px;">🔍 البحث والفلترة</h4>
                <div style="font-family: monospace; line-height: 1.8;">
                    <div><kbd>Ctrl + F</kbd> - التركيز على البحث</div>
                    <div><kbd>Ctrl + R</kbd> - مسح الفلاتر</div>
                    <div><kbd>F3</kbd> - البحث المتقدم</div>
                </div>
            </div>

            <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef;">
                <h4 style="color: #2c3e50; margin-bottom: 10px;">➕ إضافة سريعة</h4>
                <div style="font-family: monospace; line-height: 1.8;">
                    <div><kbd>Ctrl + N</kbd> - موظف جديد</div>
                    <div><kbd>Ctrl + L</kbd> - إجازة جديدة</div>
                    <div><kbd>Ctrl + T</kbd> - بيانات تجريبية</div>
                </div>
            </div>

            <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef;">
                <h4 style="color: #2c3e50; margin-bottom: 10px;">🎨 الواجهة</h4>
                <div style="font-family: monospace; line-height: 1.8;">
                    <div><kbd>Ctrl + D</kbd> - الوضع الليلي</div>
                    <div><kbd>Ctrl + M</kbd> - تبديل الشريط الجانبي</div>
                    <div><kbd>F11</kbd> - ملء الشاشة</div>
                </div>
            </div>

            <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef;">
                <h4 style="color: #2c3e50; margin-bottom: 10px;">❓ المساعدة</h4>
                <div style="font-family: monospace; line-height: 1.8;">
                    <div><kbd>F1</kbd> - دليل المستخدم</div>
                    <div><kbd>Ctrl + ?</kbd> - اختصارات المفاتيح</div>
                    <div><kbd>Ctrl + I</kbd> - معلومات النظام</div>
                </div>
            </div>
        </div>

        <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 8px; border: 1px solid #ffeaa7;">
            <p style="margin: 0; color: #856404;"><strong>💡 ملاحظة:</strong> جميع الاختصارات تعمل في أي مكان في النظام. استخدم <kbd>Ctrl + ?</kbd> لعرض هذه القائمة في أي وقت.</p>
        </div>
    `;
}

/**
 * عرض النصائح والحيل
 */
function showTips() {
    const guideContent = document.getElementById('guideContent');
    guideContent.style.display = 'block';
    guideContent.innerHTML = `
        <div style="background: linear-gradient(135deg, #f39c12, #e67e22); color: white; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
            <h3 style="margin: 0 0 15px 0;">💡 نصائح وحيل لاستخدام أفضل</h3>
            <p style="margin: 0; line-height: 1.6;">اكتشف الميزات المخفية والطرق الذكية لتحقيق أقصى استفادة من النظام.</p>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
            <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db;">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">🎯 نصائح الإنتاجية</h4>
                <ul style="line-height: 1.8; color: #666;">
                    <li>استخدم البحث السريع بكتابة أي جزء من اسم الموظف أو رقمه</li>
                    <li>انقر مرتين على أي موظف في الجدول للانتقال مباشرة لإدارة إجازاته</li>
                    <li>استخدم الفلاتر المتعددة معاً للحصول على نتائج دقيقة</li>
                    <li>احفظ البيانات بانتظام باستخدام Ctrl+S</li>
                </ul>
            </div>

            <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #27ae60;">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">📊 نصائح البيانات</h4>
                <ul style="line-height: 1.8; color: #666;">
                    <li>الرصيد المرحل يُضاف تلقائياً للرصيد السنوي</li>
                    <li>استخدم التقارير المفصلة لتحليل استخدام الإجازات</li>
                    <li>راقب الموظفين ذوي الرصيد المنخفض من لوحة التحكم</li>
                    <li>صدّر البيانات بصيغة CSV لتحليلها في Excel</li>
                </ul>
            </div>

            <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #e74c3c;">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">⚠️ نصائح الأمان</h4>
                <ul style="line-height: 1.8; color: #666;">
                    <li>أنشئ نسخة احتياطية قبل إجراء تغييرات كبيرة</li>
                    <li>تحقق من صحة البيانات قبل الاستيراد</li>
                    <li>استخدم أرقام وظيفية فريدة لكل موظف</li>
                    <li>راجع سجل الإجازات بانتظام للتأكد من دقته</li>
                </ul>
            </div>

            <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #9b59b6;">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">🚀 ميزات متقدمة</h4>
                <ul style="line-height: 1.8; color: #666;">
                    <li>استخدم الوضع الليلي للعمل في الإضاءة المنخفضة</li>
                    <li>النظام يحفظ تلقائياً كل 30 ثانية</li>
                    <li>يمكن استخدام النظام بدون اتصال بالإنترنت</li>
                    <li>البيانات محفوظة محلياً وآمنة في متصفحك</li>
                </ul>
            </div>
        </div>

        <div style="margin-top: 20px; padding: 20px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border-radius: 8px;">
            <h4 style="margin: 0 0 15px 0;">🎓 هل تعلم؟</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div>• يمكنك تغيير حجم الجدول بسحب الحدود</div>
                <div>• النظام يدعم التقويم الميلادي بالكامل</div>
                <div>• يمكن إضافة إجازات بأجزاء اليوم (0.5)</div>
                <div>• الرسوم البيانية تتحدث تلقائياً مع البيانات</div>
            </div>
        </div>
    `;
}
