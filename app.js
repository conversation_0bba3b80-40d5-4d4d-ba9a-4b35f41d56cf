/**
 * نظام إدارة إجازات الموظفين المتطور
 * ملف JavaScript الرئيسي
 * يحتوي على الوظائف الأساسية للتطبيق
 */

// ===== متغيرات التهيئة =====
let isInitialized = false;

// ===== وظائف التنقل والواجهة =====

/**
 * تبديل الشريط الجانبي
 */
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContainer = document.getElementById('mainContainer');
    
    sidebar.classList.toggle('collapsed');
    mainContainer.classList.toggle('expanded');
}

/**
 * عرض قسم معين
 */
function showSection(sectionName) {
    try {
        console.log(`🔄 التنقل إلى قسم: ${sectionName}`);
        
        // التأكد من وجود المصفوفات
        if (!window.employees) window.employees = [];
        if (!window.filteredEmployees) window.filteredEmployees = [];
        
        // إخفاء جميع الأقسام
        document.querySelectorAll('.section').forEach(section => {
            section.classList.remove('active');
            section.style.display = 'none';
        });
        
        // إزالة الفئة النشطة من جميع عناصر التنقل
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // عرض القسم المحدد
        const targetSection = document.getElementById(sectionName);
        if (targetSection) {
            targetSection.classList.add('active');
            targetSection.style.display = 'block';
            console.log(`✅ تم عرض قسم: ${sectionName}`);
        } else {
            console.error(`❌ لم يتم العثور على القسم: ${sectionName}`);
            return;
        }
        
        // إضافة الفئة النشطة لعنصر التنقل الصحيح
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            const itemText = item.textContent.trim();
            if ((sectionName === 'dashboard' && itemText.includes('لوحة التحكم')) ||
                (sectionName === 'employees' && itemText.includes('إدارة الموظفين')) ||
                (sectionName === 'leaves' && itemText.includes('إدارة الإجازات')) ||
                (sectionName === 'reports' && itemText.includes('التقارير')) ||
                (sectionName === 'settings' && itemText.includes('الإعدادات'))) {
                item.classList.add('active');
            }
        });
        
        // تحديث عنوان الصفحة
        updatePageHeader(sectionName);
        
        // تحميل محتوى القسم
        loadSectionContent(sectionName);
        
        // معالجة خاصة لكل قسم
        setTimeout(() => {
            switch(sectionName) {
                case 'dashboard':
                    updateStats();
                    updateQuickEmployeeView();
                    updateCharts();
                    console.log('✅ تم تحديث لوحة التحكم');
                    break;
                case 'employees':
                    updateTable();
                    updateDepartmentFilter();
                    console.log('✅ تم تحديث قسم الموظفين');
                    break;
                case 'leaves':
                    updateLeaveEmployeeSelect();
                    console.log('✅ تم تحديث قسم الإجازات');
                    break;
                case 'reports':
                    console.log('✅ تم تحميل قسم التقارير');
                    break;
                case 'settings':
                    console.log('✅ تم تحميل قسم الإعدادات');
                    break;
            }
        }, 100);
        
        // تحديث القسم الحالي
        window.currentSection = sectionName;
        console.log(`🎯 القسم الحالي: ${window.currentSection}`);
        
    } catch (error) {
        console.error(`❌ خطأ في التنقل إلى ${sectionName}:`, error);
        showNotification(`خطأ في التنقل إلى ${sectionName}`, 'danger', 5000);
    }
}

/**
 * تحديث عنوان الصفحة
 */
function updatePageHeader(sectionName) {
    const titles = {
        dashboard: { title: 'لوحة التحكم', subtitle: 'نظرة عامة على إحصائيات الإجازات' },
        employees: { title: 'إدارة الموظفين', subtitle: 'إضافة وتعديل بيانات الموظفين' },
        leaves: { title: 'إدارة الإجازات', subtitle: 'إدارة شاملة لإجازات الموظفين' },
        reports: { title: 'التقارير', subtitle: 'تقارير وإحصائيات مفصلة' },
        settings: { title: 'الإعدادات', subtitle: 'إعدادات النظام والبيانات' }
    };
    
    document.getElementById('pageTitle').textContent = titles[sectionName].title;
    document.getElementById('pageSubtitle').textContent = titles[sectionName].subtitle;
}

// ===== وظائف الوضع الليلي =====

/**
 * تبديل الوضع الليلي
 */
function toggleTheme() {
    isDarkMode = !isDarkMode;
    document.body.classList.toggle('dark-mode', isDarkMode);
    
    const themeButton = document.querySelector('.theme-toggle');
    themeButton.textContent = isDarkMode ? '☀️' : '🌙';
    themeButton.title = isDarkMode ? 'تبديل للوضع النهاري' : 'تبديل للوضع الليلي';
    
    // حفظ تفضيل الوضع
    localStorage.setItem('darkMode', isDarkMode);
    
    showNotification(isDarkMode ? 'تم تفعيل الوضع الليلي' : 'تم تفعيل الوضع النهاري', 'info');
}

/**
 * تحميل إعدادات الوضع الليلي
 */
function loadTheme() {
    const savedTheme = localStorage.getItem('darkMode');
    if (savedTheme === 'true') {
        isDarkMode = true;
        document.body.classList.add('dark-mode');
        const themeButton = document.querySelector('.theme-toggle');
        themeButton.textContent = '☀️';
        themeButton.title = 'تبديل للوضع النهاري';
    }
}

// ===== نظام الإشعارات =====

/**
 * عرض إشعار
 */
function showNotification(message, type = 'info', duration = 5000) {
    const container = document.getElementById('notificationContainer');
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 18px; cursor: pointer; color: #999;">×</button>
        </div>
    `;
    
    container.appendChild(notification);
    
    // إزالة تلقائية بعد المدة المحددة
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, duration);
}

/**
 * عرض تنبيه
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alertContainer');
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.textContent = message;
    
    alertContainer.appendChild(alertDiv);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// ===== مؤشر الحفظ =====

/**
 * عرض مؤشر الحفظ
 */
function showSaveIndicator(status) {
    const indicator = document.getElementById('saveIndicator');
    if (status === 'saving') {
        indicator.textContent = 'جاري الحفظ...';
        indicator.className = 'save-indicator saving';
    } else {
        indicator.textContent = 'محفوظ تلقائياً';
        indicator.className = 'save-indicator';
    }
}

// ===== وظائف حفظ وتحميل البيانات =====

/**
 * حفظ البيانات
 */
function saveData() {
    try {
        const data = {
            employees: window.employees,
            lastSaved: new Date().toISOString(),
            version: '4.0',
            checksum: generateChecksum(window.employees)
        };
        
        const compressedData = compressData(data);
        const encryptedData = simpleEncrypt(compressedData);
        
        localStorage.setItem('employeeLeaveSystem', encryptedData);
        
        // إنشاء نسخة احتياطية تلقائية
        const backupKey = `backup_${new Date().toISOString().split('T')[0]}`;
        localStorage.setItem(backupKey, encryptedData);
        
        // الاحتفاظ بآخر 7 أيام فقط من النسخ الاحتياطية
        cleanOldBackups();
        
        showSaveIndicator('saving');
        
        setTimeout(() => {
            showSaveIndicator('saved');
        }, 500);
        
        return JSON.stringify(data, null, 2);
    } catch (error) {
        console.error('Error saving data:', error);
        showAlert('خطأ في حفظ البيانات: ' + error.message, 'danger');
        return null;
    }
}

/**
 * تحميل البيانات من التخزين المحلي
 */
function loadDataFromStorage() {
    try {
        const savedData = localStorage.getItem('employeeLeaveSystem');
        if (savedData) {
            let data;
            
            // محاولة فك التشفير أولاً (التنسيق الجديد)
            const decryptedData = simpleDecrypt(savedData);
            if (decryptedData) {
                data = JSON.parse(decryptedData);
            } else {
                // العودة للتنسيق القديم
                data = JSON.parse(savedData);
            }
            
            if (data.employees && Array.isArray(data.employees)) {
                // التحقق من checksum إذا كان متوفراً
                if (data.checksum) {
                    const currentChecksum = generateChecksum(data.employees);
                    if (currentChecksum !== data.checksum) {
                        showNotification('⚠️ تم اكتشاف تغيير في البيانات. قد تكون البيانات معدلة.', 'warning', 10000);
                    }
                }
                
                window.employees = data.employees.map(emp => ({
                    name: emp.name || '',
                    id: emp.id || '',
                    department: emp.department || '',
                    hireDate: emp.hireDate || new Date().toISOString().split('T')[0],
                    annualLeave: emp.annualLeave || 30,
                    carriedOverLeave: emp.carriedOverLeave || 0,
                    sickLeave: emp.sickLeave || 15,
                    emergencyLeave: emp.emergencyLeave || 5,
                    usedAnnual: emp.usedAnnual || 0,
                    usedSick: emp.usedSick || 0,
                    usedEmergency: emp.usedEmergency || 0,
                    leaveHistory: emp.leaveHistory || []
                }));
                
                console.log(`تم تحميل ${window.employees.length} موظف من التخزين المحلي`);
                showNotification(`تم تحميل ${window.employees.length} موظف بنجاح`, 'info', 3000);
                return true;
            }
        }
        return false;
    } catch (error) {
        console.error('Error loading data from localStorage:', error);
        showAlert('خطأ في تحميل البيانات المحفوظة: ' + error.message, 'warning');
        
        // محاولة التحميل من النسخة الاحتياطية
        return loadFromBackup();
    }
}

/**
 * تحميل البيانات
 */
function loadData() {
    try {
        const loaded = loadDataFromStorage();

        if (!loaded) {
            window.employees = [];
            console.log('لا توجد بيانات محفوظة مسبقاً');
        } else {
            console.log(`تم تحميل ${window.employees.length} موظف بنجاح`);
        }

        window.filteredEmployees = [...window.employees];

        console.log('Data loading completed successfully');
        return loaded;
    } catch (error) {
        console.error('Error in loadData:', error);
        window.employees = [];
        window.filteredEmployees = [];
        return false;
    }
}

// ===== وظائف تحميل محتوى الأقسام =====

/**
 * تحميل محتوى القسم
 */
function loadSectionContent(sectionName) {
    const sectionElement = document.getElementById(sectionName);

    switch(sectionName) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'employees':
            loadEmployeesSection();
            break;
        case 'leaves':
            loadLeavesSection();
            break;
        case 'reports':
            loadReportsSection();
            break;
        case 'settings':
            loadSettingsSection();
            break;
    }
}

/**
 * تحميل لوحة التحكم
 */
function loadDashboard() {
    const dashboardElement = document.getElementById('dashboard');

    // إنشاء محتوى لوحة التحكم مع مؤشرات التحميل
    dashboardElement.innerHTML = `
        <div class="stats-grid">
            <div class="stat-card">
                <h3>إجمالي الموظفين</h3>
                <div class="number" id="totalEmployees">⏳</div>
                <div class="change">موظف نشط</div>
            </div>
            <div class="stat-card">
                <h3>إجمالي الرصيد المرحل</h3>
                <div class="number" id="totalCarriedOver">⏳</div>
                <div class="change">يوم مرحل</div>
            </div>
            <div class="stat-card">
                <h3>الإجازات المستخدمة</h3>
                <div class="number" id="totalUsedLeaves">⏳</div>
                <div class="change">يوم مستخدم</div>
            </div>
            <div class="stat-card">
                <h3>الإجازات المتبقية</h3>
                <div class="number" id="totalRemainingLeaves">⏳</div>
                <div class="change">يوم متبقي</div>
            </div>
            <div class="stat-card">
                <h3>متوسط الاستخدام</h3>
                <div class="number" id="averageUsage">⏳</div>
                <div class="change">من إجمالي الرصيد</div>
            </div>
        </div>

        <!-- قسم الرسوم البيانية -->
        <div class="chart-container">
            <h3 style="margin-bottom: 20px; color: #2c3e50;">📊 الرسوم البيانية</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div>
                    <h4 style="text-align: center; margin-bottom: 15px;">توزيع الموظفين حسب الأقسام</h4>
                    <canvas id="departmentChart" width="300" height="200" style="border: 1px solid #e9ecef; border-radius: 8px;"></canvas>
                </div>
                <div>
                    <h4 style="text-align: center; margin-bottom: 15px;">حالات رصيد الإجازات</h4>
                    <canvas id="leaveStatusChart" width="300" height="200" style="border: 1px solid #e9ecef; border-radius: 8px;"></canvas>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="card-title">📊 نظرة سريعة على الموظفين</h3>
            </div>
            <div class="card-body">
                <div id="quickEmployeeView">
                    <div style="text-align: center; padding: 40px;">
                        <div style="font-size: 24px; margin-bottom: 10px;">⏳</div>
                        <p style="color: #7f8c8d;">جاري تحميل بيانات الموظفين...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم الإجراءات السريعة -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">🚀 إجراءات سريعة</h3>
            </div>
            <div class="card-body">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <button class="btn btn-primary btn-large" onclick="showSection('employees')" style="padding: 15px;">
                        👥 إدارة الموظفين
                    </button>
                    <button class="btn btn-success btn-large" onclick="showSection('leaves')" style="padding: 15px;">
                        📅 إدارة الإجازات
                    </button>
                    <button class="btn btn-warning btn-large" onclick="showSection('reports')" style="padding: 15px;">
                        📊 التقارير
                    </button>
                    <button class="btn btn-secondary btn-large" onclick="loadTestData()" style="padding: 15px;">
                        🧪 بيانات تجريبية
                    </button>
                </div>
            </div>
        </div>
    `;

    // تحديث المحتوى فوراً
    setTimeout(() => {
        updateStats();
        updateQuickEmployeeView();
        updateCharts();
    }, 100);
}
